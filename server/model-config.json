{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/crm/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/crm/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Role": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db"}, "Item": {"dataSource": "transient", "public": false}, "FulfillItem": {"dataSource": "transient", "public": false}, "ExtOrder": {"dataSource": "transient", "public": false}, "ExtOrderV1": {"dataSource": "transient", "public": false}, "Discount": {"dataSource": "transient", "public": false}, "Option": {"dataSource": "transient", "public": false}, "OptionValue": {"dataSource": "transient", "public": false}, "Billing": {"dataSource": "transient", "public": false}, "TouchPoint": {"dataSource": "transient", "public": false}, "Geometry": {"dataSource": "transient", "public": false}, "Address": {"dataSource": "transient", "public": false}, "Spot": {"dataSource": "transient", "public": false}, "Locale": {"dataSource": "transient", "public": false}, "OpeningHour": {"dataSource": "transient", "public": false}, "Globalize": {"dataSource": "transient", "public": false}, "Tag": {"dataSource": "transient", "public": false}, "Note": {"dataSource": "transient", "public": false}, "Url": {"dataSource": "transient", "public": false}, "Callback": {"dataSource": "transient", "public": false}, "Order": {"dataSource": "trap"}, "Fulfillment": {"dataSource": "trap"}, "Booking": {"dataSource": "trap"}, "Account": {"dataSource": "accountRemote", "public": false}, "Product": {"dataSource": "productRemote", "public": false}, "Variant": {"dataSource": "productRemote", "public": false}, "Resource": {"dataSource": "productRemote", "public": false}, "Person": {"dataSource": "person<PERSON><PERSON><PERSON>", "public": false}, "Member": {"dataSource": "membershipRemote", "public": false}, "Membership": {"dataSource": "membershipRemote", "public": false}, "Program": {"dataSource": "membershipRemote", "public": false}, "Business": {"dataSource": "businessRemote", "public": false}, "Messaging": {"dataSource": "messagingRemote", "public": false}, "Offer": {"dataSource": "offerRemote", "public": false}, "OfferMaster": {"dataSource": "offerRemote", "public": false}, "Staff": {"dataSource": "businessRemote", "public": false}, "Place": {"dataSource": "placeRemote", "public": false}, "Numbering": {"dataSource": "placeRemote", "public": false}, "Printer": {"dataSource": "placeRemote", "public": false}, "Vending": {"dataSource": "placeRemote", "public": false}, "Kiosk": {"dataSource": "placeRemote", "public": false}, "Provider": {"dataSource": "businessRemote", "public": false}, "SharedProvider": {"dataSource": "businessRemote", "public": false}, "Payment": {"dataSource": "transient", "public": false}, "OrderProvider": {"dataSource": "transient", "public": false}, "FulfillmentProvider": {"dataSource": "transient", "public": false}, "kitchen": {"dataSource": "transient"}, "shopify": {"dataSource": "transient"}, "lalamove": {"dataSource": "transient", "public": false}, "grabfood": {"dataSource": "transient", "public": false}, "grabmart": {"dataSource": "transient", "public": false}, "ubereats": {"dataSource": "transient"}, "machine": {"dataSource": "transient"}, "nxiot": {"dataSource": "transient", "public": false}}