{"initial": {"compression": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}, "loopback#favicon": {}, "nocache": {}}, "session": {}, "auth:after": {"./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Orders", "/api/Fulfillments", "/api/Bookings", "/api/Metrics", "/api/_m-*", "/api/digital", "/api/kitchen", "/api/shopify", "/api/ubereats"], "params": {"tenant-code": "trap"}, "enabled": true}, "./lib/common/middleware/install": {"name": "install", "paths": ["/api/Fulfillments/staff", "/api/Orders/staff"]}, "./lib/common/middleware/location": {"name": "location", "paths": ["/api/Fulfillments/staff", "/api/Orders/staff"]}}, "parse:before": {"./lib/common/middleware/multitenant-ds": {"name": "multitenant-ds", "paths": ["/api/Orders", "/api/Fulfillments", "/api/Bookings", "/api/Metrics", "/api/_m-*", "/api/digital", "/api/kitchen", "/api/shopify", "/api/ubereats"], "enabled": true}}, "parse": {}, "routes": {"loopback#rest": {"paths": ["${restApiRoot}"]}}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}