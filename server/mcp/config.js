/**
 * Sales Service MCP Configuration
 * Modern configuration-driven approach using shared factory libraries
 */
const { z } = require('zod');

const SalesServiceConfig = {
  serviceConfig: {
    serviceName: 'Sales',
    defaultLimit: 20,
    maxLimit: 100,
    customOperations: {
      // Custom operations can be added here if needed
      // Example: activate, clone, etc.
    }
  },

  modelConfigs: {
    Order: {
      toolPrefix: 'order',
      responseKey: 'order',
      baseSchema: {
        // Core order fields
        amount: z.number().min(0).describe('Order amount in cents'),
        status: z.enum(['pending', 'paid', 'fulfilled', 'cancelled']).optional().describe('Order status'),
        createdAt: z.string().optional().describe('Order creation date (ISO format)'),
        updatedAt: z.string().optional().describe('Order last update date (ISO format)')
      },
      typeSpecificSchema: {
        // Sales-specific order fields
        'receipt.number': z.string().optional().describe('Receipt number'),
        'external.perkd.cardId': z.string().optional().describe('Customer card ID'),
        'when.paid': z.string().optional().describe('Payment timestamp'),
        'when.fulfilled': z.string().optional().describe('Fulfillment timestamp'),
        'when.cancelled': z.string().optional().describe('Cancellation timestamp')
      },
      specializedTools: [
        {
          name: 'findByStatus',
          title: 'Find Orders by Status',
          description: 'Find orders with specific status (paid, fulfilled, cancelled)',
          inputSchema: {
            status: z.enum(['paid', 'fulfilled', 'cancelled']).describe('Order status to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => {
            const { status } = args;
            if (status === 'fulfilled') {
              return { 'when.fulfilled': { neq: null } };
            } else if (status === 'cancelled') {
              return { 'when.cancelled': { neq: null } };
            } else if (status === 'paid') {
              return { 
                'when.paid': { neq: null },
                'when.fulfilled': null,
                'when.cancelled': null
              };
            }
            return {};
          },
          order: 'createdAt DESC'
        },
        {
          name: 'findByDateRange',
          title: 'Find Orders by Date Range',
          description: 'Find orders created within a specific date range',
          inputSchema: {
            dateFrom: z.string().describe('Start date (ISO format)'),
            dateTo: z.string().describe('End date (ISO format)'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({
            createdAt: {
              gte: new Date(args.dateFrom),
              lte: new Date(args.dateTo)
            }
          }),
          order: 'createdAt DESC'
        },
        {
          name: 'findByCustomer',
          title: 'Find Orders by Customer',
          description: 'Find orders for a specific customer by card ID or receipt number',
          inputSchema: {
            query: z.string().min(1).describe('Customer card ID or receipt number'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => {
            const { query } = args;
            // Check if it looks like an ID
            if (/^[0-9a-f]{24}$/i.test(query)) {
              return { id: query };
            }
            // Otherwise search by receipt number or card ID
            return {
              or: [
                { 'receipt.number': { like: query, options: 'i' } },
                { 'external.perkd.cardId': { like: query, options: 'i' } }
              ]
            };
          },
          order: 'createdAt DESC'
        }
      ]
    },

    Fulfillment: {
      toolPrefix: 'fulfillment',
      responseKey: 'fulfillment',
      baseSchema: {
        type: z.string().describe('Fulfillment type (deliver, pickup, etc.)'),
        status: z.string().describe('Fulfillment status'),
        orderId: z.string().describe('Associated order ID'),
        placeId: z.string().optional().describe('Fulfillment place ID'),
        scheduled: z.string().optional().describe('Scheduled fulfillment time'),
        flow: z.object({}).optional().describe('Fulfillment flow state'),
        createdAt: z.string().optional().describe('Creation date (ISO format)'),
        updatedAt: z.string().optional().describe('Last update date (ISO format)')
      },
      specializedTools: [
        {
          name: 'findByType',
          title: 'Find Fulfillments by Type',
          description: 'Find fulfillments of a specific type',
          inputSchema: {
            type: z.string().describe('Fulfillment type to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ type: args.type }),
          order: 'createdAt DESC'
        },
        {
          name: 'findByPlace',
          title: 'Find Fulfillments by Place',
          description: 'Find fulfillments for a specific place',
          inputSchema: {
            placeId: z.string().describe('Place ID to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ placeId: args.placeId }),
          order: 'createdAt DESC'
        },
        {
          name: 'findByStatus',
          title: 'Find Fulfillments by Status',
          description: 'Find fulfillments with specific status',
          inputSchema: {
            status: z.string().describe('Status to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ status: args.status }),
          order: 'createdAt DESC'
        }
      ]
    },

    Booking: {
      toolPrefix: 'booking',
      responseKey: 'booking',
      baseSchema: {
        status: z.enum(['pending', 'open', 'success', 'ended', 'cancelled', 'noshow', 'error']).describe('Booking status'),
        startTime: z.string().optional().describe('Booking start time (ISO format)'),
        endTime: z.string().optional().describe('Booking end time (ISO format)'),
        resourceId: z.string().optional().describe('Resource ID'),
        placeId: z.string().optional().describe('Place ID'),
        personId: z.string().optional().describe('Person ID'),
        note: z.string().optional().describe('Booking notes'),
        createdAt: z.string().optional().describe('Creation date (ISO format)'),
        updatedAt: z.string().optional().describe('Last update date (ISO format)')
      },
      specializedTools: [
        {
          name: 'findByResource',
          title: 'Find Bookings by Resource',
          description: 'Find bookings for a specific resource',
          inputSchema: {
            resourceId: z.string().describe('Resource ID to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ resourceId: args.resourceId }),
          order: 'startTime DESC'
        },
        {
          name: 'findByPlace',
          title: 'Find Bookings by Place',
          description: 'Find bookings for a specific place',
          inputSchema: {
            placeId: z.string().describe('Place ID to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ placeId: args.placeId }),
          order: 'startTime DESC'
        },
        {
          name: 'findByStatus',
          title: 'Find Bookings by Status',
          description: 'Find bookings with specific status',
          inputSchema: {
            status: z.enum(['pending', 'open', 'success', 'ended', 'cancelled', 'noshow', 'error']).describe('Status to filter by'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({ status: args.status }),
          order: 'createdAt DESC'
        },
        {
          name: 'findByDateRange',
          title: 'Find Bookings by Date Range',
          description: 'Find bookings within a specific date range',
          inputSchema: {
            dateFrom: z.string().describe('Start date (ISO format)'),
            dateTo: z.string().describe('End date (ISO format)'),
            limit: z.number().min(1).max(100).optional().default(20).describe('Maximum results to return'),
            skip: z.number().min(0).optional().default(0).describe('Number of results to skip')
          },
          filter: (args) => ({
            startTime: {
              gte: new Date(args.dateFrom),
              lte: new Date(args.dateTo)
            }
          }),
          order: 'startTime DESC'
        }
      ]
    }
  }
};

module.exports = { SalesServiceConfig };
