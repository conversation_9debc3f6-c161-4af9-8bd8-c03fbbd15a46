/**
 * Sales Service MCP Extension
 * Modern factory-based implementation using shared libraries from mcp-core v3.1.0
 */
const { BaseExtension, SharedDocumentationProvider } = require('@perkd/mcp-core')
const { SalesServiceConfig } = require('./config')

/**
 * Sales MCP Extension
 * Provides comprehensive sales data access through auto-generated tools
 *
 * Generated Tools (per model):
 * - {prefix}_create - Create new instance
 * - {prefix}_get - Get by ID
 * - {prefix}_list - List with pagination
 * - {prefix}_delete - Delete by ID
 * - {prefix}_query - Advanced query
 * - {prefix}_count - Count records
 * - {prefix}_{specialized} - Specialized tools (findByStatus, findByDateRange, etc.)
 *
 * Total Expected Tools: ~21 tools across 3 models
 * - Order: 9 tools (6 standard + 3 specialized)
 * - Fulfillment: 9 tools (6 standard + 3 specialized)
 * - Booking: 10 tools (6 standard + 4 specialized)
 */
class McpExtension extends BaseExtension {
	/**
   * Initialize the Sales MCP extension
   * Sets up factory libraries and registers all tools and resources
   */
	async initialize() {
		try {
			this.log('info', 'Initializing Sales MCP Extension with factory libraries...')

			// 1. Initialize shared factory libraries
			await this.initializeFactories(SalesServiceConfig, {
				generateSchema: true,        // Generate schema documentation
				generateExamples: true,      // Generate usage examples
				generateUploadDocs: false    // Sales service doesn't handle file uploads
			})

			// 2. Auto-register all factory-generated tools
			await this.registerAllFactoryTools()

			// 3. Register documentation resources
			await this.registerDocumentationResources()

			// 4. Register interactive prompts
			await this.registerServicePrompts()

			// Log summary of registered tools
			const toolCount = this.getRegisteredToolCount()
			this.log('info', `Registered ${toolCount} tools for Sales service`)

		}
		catch (error) {
			this.log('error', 'Failed to initialize Sales MCP Extension', error)
			throw error
		}
	}

	/**
   * Register documentation resources for all models
   * Auto-generates comprehensive documentation using shared providers
   */
	async registerDocumentationResources() {
		try {
			for (const modelType of Object.keys(SalesServiceConfig.modelConfigs)) {
				const modelConfig = SalesServiceConfig.modelConfigs[modelType]
				const toolPrefix = modelConfig.toolPrefix

				// Schema documentation
				this.registerStaticResource(
					`${toolPrefix}-schema`,
					`${toolPrefix}://schema`,
					{
						title: `${modelType} Schema`,
						description: `Complete schema definition for ${modelType} model`,
						mimeType: 'application/json'
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}
						return SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
					}
				)

				// Usage examples
				this.registerStaticResource(
					`${toolPrefix}-examples`,
					`${toolPrefix}://examples`,
					{
						title: `${modelType} Examples`,
						description: `Usage examples for all ${modelType} tools`,
						mimeType: 'application/json'
					},
					async () => {
						const Model = this.app.models[modelType]
						if (!Model) {
							throw new Error(`Model ${modelType} not found`)
						}
						const docs = SharedDocumentationProvider.generateCompleteDocumentation(Model, modelConfig)
						return docs.examples
					}
				)
			}

			// Service overview
			this.registerStaticResource(
				'sales-overview',
				'sales://overview',
				{
					title: 'Sales Service Overview',
					description: 'Complete overview of Sales service capabilities',
					mimeType: 'application/json'
				},
				() => this.generateServiceOverview()
			)

			// Model types reference
			this.registerStaticResource(
				'sales-types',
				'sales://types',
				{
					title: 'Sales Model Types',
					description: 'Reference guide for all Sales model types',
					mimeType: 'application/json'
				},
				() => this.generateModelTypesReference()
			)

			this.log('info', 'Registered documentation resources for all models')
		}
		catch (error) {
			this.log('error', 'Failed to register documentation resources', error)
			throw error
		}
	}

	/**
   * Register interactive service prompts
   * Provides guided assistance for using Sales tools
   */
	async registerServicePrompts() {
		try {
			// Sales management guide
			this.server.registerPrompt('sales-management-guide', {
				title: 'Sales Management Guide',
				description: 'Interactive guide for using Sales service tools'
			}, async () => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: 'What Sales tools are available and how do I use them?'
						}
					},
					{
						role: 'assistant',
						content: {
							type: 'text',
							text: this.generateToolsGuide()
						}
					}
				]
			}))

			// Sales analytics assistant
			this.server.registerPrompt('sales-analytics-assistant', {
				title: 'Sales Analytics Assistant',
				description: 'Step-by-step guidance for sales data analysis'
			}, async () => ({
				messages: [
					{
						role: 'user',
						content: {
							type: 'text',
							text: 'How can I analyze sales performance and trends?'
						}
					},
					{
						role: 'assistant',
						content: {
							type: 'text',
							text: this.generateAnalyticsGuide()
						}
					}
				]
			}))

			this.log('info', 'Registered interactive service prompts')
		}
		catch (error) {
			this.log('error', 'Failed to register service prompts', error)
			throw error
		}
	}

	/**
   * Generate service overview
   */
	generateServiceOverview() {
		const models = Object.keys(SalesServiceConfig.modelConfigs)
		const toolCount = this.getRegisteredToolCount()

		return {
			service: 'Sales',
			version: '1.0.0',
			description: 'Comprehensive sales data management and analytics',
			models,
			totalTools: toolCount,
			capabilities: [
				'Order management and tracking',
				'Fulfillment monitoring and analysis',
				'Booking management and optimization',
				'Advanced querying and filtering',
				'Real-time analytics and reporting'
			],
			documentation: {
				schemas: models.map(model => `${SalesServiceConfig.modelConfigs[model].toolPrefix}://schema`),
				examples: models.map(model => `${SalesServiceConfig.modelConfigs[model].toolPrefix}://examples`),
				overview: 'sales://overview',
				types: 'sales://types'
			}
		}
	}

	/**
   * Generate model types reference
   */
	generateModelTypesReference() {
		const types = {}

		for (const [ modelName, config ] of Object.entries(SalesServiceConfig.modelConfigs)) {
			types[modelName] = {
				toolPrefix: config.toolPrefix,
				responseKey: config.responseKey,
				description: `${modelName} model for sales operations`,
				tools: {
					standard: [ 'create', 'get', 'list', 'delete', 'query', 'count' ],
					specialized: config.specializedTools?.map(tool => tool.name) || []
				}
			}
		}

		return { types }
	}

	/**
   * Generate tools usage guide
   */
	generateToolsGuide() {
		const models = Object.keys(SalesServiceConfig.modelConfigs)
		let guide = '# Sales Service Tools Guide\n\n'

		guide += 'The Sales service provides comprehensive tools for managing orders, fulfillments, and bookings.\n\n'
		guide += '## Available Models\n\n'

		for (const modelName of models) {
			const config = SalesServiceConfig.modelConfigs[modelName]
			guide += `### ${modelName}\n`
			guide += `- **Tool Prefix**: \`${config.toolPrefix}\`\n`
			guide += '- **Standard Tools**: create, get, list, delete, query, count\n'
			if (config.specializedTools?.length > 0) {
				guide += `- **Specialized Tools**: ${config.specializedTools.map(t => t.name).join(', ')}\n`
			}
			guide += '\n'
		}

		guide += '## Usage Examples\n\n'
		guide += '```\n'
		guide += '// List recent orders\n'
		guide += 'order_list({ "limit": 10, "order": "createdAt DESC" })\n\n'
		guide += '// Find orders by status\n'
		guide += 'order_findByStatus({ "status": "paid", "limit": 20 })\n\n'
		guide += '// Get fulfillment details\n'
		guide += 'fulfillment_get({ "fulfillmentId": "12345" })\n'
		guide += '```\n'

		return guide
	}

	/**
   * Generate analytics guide
   */
	generateAnalyticsGuide() {
		return `# Sales Analytics Guide

Use these tools to analyze sales performance and trends:

## Order Analysis
- \`order_findByDateRange\` - Analyze orders over time periods
- \`order_findByStatus\` - Track order status distribution
- \`order_query\` - Advanced filtering and aggregation

## Fulfillment Analysis  
- \`fulfillment_findByType\` - Compare fulfillment methods
- \`fulfillment_findByPlace\` - Analyze location performance
- \`fulfillment_query\` - Custom fulfillment queries

## Booking Analysis
- \`booking_findByResource\` - Resource utilization analysis
- \`booking_findByDateRange\` - Booking trends over time
- \`booking_query\` - Advanced booking analytics

## Example Analytics Queries
\`\`\`
// Monthly sales performance
order_findByDateRange({
  "dateFrom": "2024-01-01T00:00:00Z",
  "dateTo": "2024-01-31T23:59:59Z",
  "limit": 100
})

// Fulfillment efficiency by type
fulfillment_findByType({ "type": "delivery", "limit": 50 })
\`\`\``
	}

	/**
   * Get count of registered tools (helper method)
   */
	getRegisteredToolCount() {
		// This would be implemented by the base class or calculated based on configuration
		const models = Object.keys(SalesServiceConfig.modelConfigs)
		let count = 0

		for (const modelName of models) {
			const config = SalesServiceConfig.modelConfigs[modelName]
			count += 6 // Standard tools: create, get, list, delete, query, count
			count += config.specializedTools?.length || 0
			count += config.enabledOperations?.length || 0
		}

		return count
	}

	/**
   * Cleanup resources when extension is terminated
   */
	async cleanup() {
		try {
			this.log('info', 'Cleaning up Sales MCP Extension...')
			await super.cleanup()
			this.log('info', 'Sales MCP Extension cleanup complete')
		}
		catch (error) {
			this.log('error', 'Error during Sales MCP Extension cleanup', error)
			throw error
		}
	}
}

module.exports = { McpExtension }
