/**
 *  @module Mixin:CallbackAPI (for X)
 */
const { Providers } = require('@crm/types')

const { PERKD } = Providers.PROVIDER

module.exports = function(Order, options) {

	/**
	 * Create an Order
	 * @param	{Object} order
	 * @param	{Object} options
	 * 			{String} userId - of Perkd
	 * 			{String} cardId - of Perkd
	 * 			{Boolean} activeMembership - must have valid membership to order, default TRUE
	 * 			{String} campaignId
	 * 			{String} messageId
	 * 			{Object} location - { type: 'place', longitude: 99, latitude: 99 }
	 * @return	{Order} new Order
	 */
	Order.appCreateOrder = async function(order, options = {}) {
		const { Membership } = Order.app.models,
			{ userId, cardId, campaignId, messageId, location, activeMembership = true } = options,
			orderId = order.id,
			provider = PERKD,
			customerId = userId,
			external = { provider, customerId, cardId, orderId, campaignId, messageId },
			membership = activeMembership
				? await Membership.findActiveByCardId(cardId).catchReturn()
				: null

		if (activeMembership && !membership) {
			return appError.notFound('No valid membership', appError.codes.membership.notFound)
		}

		order.external = external

		return Order.createOrder(order, { location })
			.catch(err => {
				console.error('❌ appCreateOrder %j', { order, err })
				throw err
			})
	}

	/**
	 * Get Order
	 * @param	{string} orderId
	 * @return	{Promise<object>}
	 */
	Order.appGetOrder = function(orderId) {
		console.error('[Sales]CallbackApi.Order.appGetOrder', orderId)
		return this.findById(orderId)
	}

	/**
	 * Get Payment Status for Order
	 * @param	{string} orderId
	 * @return	{Promise<object>} status: pending, paid, partiallypaid, refunded, cancelled
	 */
	Order.appPaymentStatus = async function(orderId) {
		const order = await this.findById(orderId)
		if (!order) return

		const status = order.paymentStatus()
		return { payment: { intent: { status } } }
	}

	// -----  Remote Methods  -----

	Order.remoteMethod('appCreateOrder', {
		description: 'Create order (Callback API)',
		http: { path: '/perkd/order', verb: 'post' },
		accepts: [
			{ arg: 'order', type: 'object', required: true },
			{ arg: 'options', type: 'object', description: '{ userId, cardId, activeMembership, campaignId, messageId, location }' },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('appGetOrder', {
		description: 'Get Order (Callback API)',
		http: { path: '/perkd/order/:id', verb: 'get' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('appPaymentStatus', {
		description: 'Get Payment Status for Order (Callback API)',
		http: { path: '/perkd/order/:id/status/payment', verb: 'get' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
		],
		returns: { type: 'object', root: true, description: '{ payment: { intent: { status } } }' },
	})
}
