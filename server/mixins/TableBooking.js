/**
 * @module Mixin:TableBooking
 * Mixin that adds table booking functionality to Order model.
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Touchpoints, Fulfillments } = require('@crm/types'),
	{ throughStaff } = require('@perkd/touchpoints')

const { STAFF_CARD } = Touchpoints.Format,
	{ DINEIN } = Fulfillments.Type

module.exports = function(Order) {

	/**
	 * Relocate (partial) fulfillments of DINEIN orders from source tables to destination tables
     * @param {Spot[]} from - list of source tables
	 *			{String} type
	 *			{String} name
	 *			{String} placeId
	 *			{String} resourceId
	 *			{Object[]} position - [{ key, value }]
     * @param {Spot[]} to - list of destination tables
     * @returns {Promise<Object>} { relocatedOrders: [ { id, from, to }] }
     */
	Order.relocateTableFulfillments = async function (from = [], to = []) {
		const { app } = Order
		const { Fulfillment } = app.models
		const { location, staff, card } = Context.appContext
		const through = throughStaff(location, staff, card, STAFF_CARD)
		const [ destinationTemplate ] = to

		// Validate inputs
		if (!from.length || !to.length) {
			throw {
				statusCode: 400,
				message: 'Source and destination tables are required'
			}
		}

		// Get open fulfillments for orders associated with source tables
		const fulfillments = await Fulfillment.findOpenForTables(from, true)

		if (!fulfillments.length) {
			return { relocatedOrders: [] }
		}

		// Store source table information before relocation
		const sourceTables = fulfillments.map(fulfillment => ({
			orderId: fulfillment.orderId,
			position: [ ...fulfillment.destination.position ],
			resourceId: fulfillment.destination.resourceId
		}))

		// Process each fulfillment
		for (const fulfillment of fulfillments) {
			const { destination: source = {} } = fulfillment
			const destination = {
				...destinationTemplate,
				position: [
					...destinationTemplate.position,
					{ key: 'sourceTable', value: source.resourceId }
				]
			}
			await fulfillment.relocate(destination, through)
		}

		// Filter dinein fulfillments and map to return data format
		const relocatedOrders = fulfillments
			.filter(fulfillment => fulfillment.type === DINEIN)
			.map(fulfillment => {
				// Find the source table information for this order
				const sourceTable = sourceTables.find(d => d.orderId === fulfillment.orderId)

				return {
					id: String(fulfillment.orderId),
					from: {
						position: sourceTable.position,
						resourceId: sourceTable.resourceId
					},
					to: {
						position: destinationTemplate.position,
						resourceId: destinationTemplate.resourceId
					}
				}
			})

		return { relocatedOrders }
	}
}
