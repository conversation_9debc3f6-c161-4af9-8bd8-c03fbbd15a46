/**
 *  @module Mixin:Availability
 */
const { Settings } = require('@crm/types')

const { FULFILLMENT } = Settings.Name,
	DURATION = 60,					// 1 hour, in minutes
	BUSY_WAIT_TIME = 15 * 60 * 1000		// 15 mins, in ms

module.exports = function(Fulfillment) {

	/**
	 * Availability of fulfillment service
	 * @param	{String} type - store, pickup, deliver
	 * @param	{String} placeId
	 * @param	{Object[]} [items]
	 * @param	{Date} [from]
	 * @param	{Number} [duration] in MINUTES
	 * @return	{Object} availability
	 *			{Object} first - earliest slot { startTime, endTime, duration }
	 *			{Number} preparationTime - in ms
	 *			{Boolean} busy - store is busy
	 *			{Boolean} closed - store is closed	// TODO for future use
	 */
	Fulfillment.availability = async function(type, placeId, items, from = new Date(), duration = DURATION) {
		const { app } = Fulfillment,
			{ Variant, Kitchen, Place } = app.models,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ busyWaitTime = BUSY_WAIT_TIME } = kitchen,
			to = new Date(from.getTime() + (duration * 60 * 1000)),
			[ order, occupied, store ] = await Promise.all([
				Variant.transformExternalOrder({ items }),
				Kitchen.occupied(placeId, from, to),
				Place.findById(placeId)
			]),
			{ preparationTime } = await Fulfillment.preparationTimeFor(order.items, store),
			available = Kitchen.availableSlots(occupied, from, to),
			first = available.find(slot => slot.duration >= preparationTime),
			startTime = first ? new Date(first.startTime.getTime() + preparationTime) : undefined,
			busy = (first === undefined) || (startTime.getTime() - Date.now() > busyWaitTime)

		// use adjusted times
		if (first) {
			first.startTime = startTime
			first.endTime = new Date(startTime.getTime() + first.duration)
		}

		return { first, preparationTime, busy, closed: false }
	}

	/**
	 * Estimate shipping rates for items
	 * @param	{Addr} origin
	 * @param	{Addr} destination
	 * @param	{Array} items
	 * @param	{String} currency
	 * @return	{Object} rates
	 */
	Fulfillment.shippingRates = function(origin, destination, items, currency) {
		const { easyship } = this.app.models,
			NOW = new Date(),
			rates = [ {
				courierId: '',
				courierName: 'SF Express',
				serviceName: 'display to customer',
				serviceCode: '',
				currency,
				totalPrice: 100.45,
				minTime: NOW,
				maxTime: NOW,
			} ],
			data = {
				origin_postal_code: '510122',
				origin_city: 'singapore',
				destination_country_alpha2: 'SG',
				destination_postal_code: '829704',
				items: [ {
					length: 20,
					width: 30,
					height: 30,
					actual_weight: 1.0,
					category: 'health_beauty',
					declared_currency: 'SGD',
				} ]
			}

		return easyship.api().then(api => api.rates.retrieve(data))
	}

	/**
	 * Available pickup locations (by address or geo)
	 * @param	{String} address
	 * @param	{Object} geo
	 * @param	{Number} radius in meters
	 * @return	{Object[]} list of places + min/max fulfill time
	 */
	Fulfillment.pickupLocations = async function(address, geo, radius = 100) {
		const locations = [ {
			placeId: '',		// used in order
			brand: {},			// { short, logo, ... }
			name: '7 Eleven Punggol',
			geo: {
				type: 'Point',
				coordinates: [ 100, 90 ],	// lng, lat
			},
			address: {
				short: '',
				formatted: '',
			},
			phones: [],
			pickup: {
				available: true,
				hours: [],		// see pickupTimes
				timeslot: 60,
				minTime: 120,	// minutes
				maxTime: 2880,	// 48 hours
			},
			globalize: {},
		} ]

		return { locations }
	}

	/**
	 * Available pickup times for items
	 * @param	{String} placeId
	 * @param	{Array} items
	 * @param	{Object} options
	 * @return	{Object} time - return earliest and latest time (with slots?)
	 *			{Object[]} hours - default, same structure as openingHours
	 *				{Object[]} periods
	 *					{Object} open
	 *						{Number} day - day of week, 1-7, where 7 represents Sunday
	 *						{String} time - 24hr ISO 8601 basic format (hhmm), 0000-2400, 2400: midnight at the end of the specified day field
	 *					{Object} close
	 *						{Number} day
	 *						{String} time
	 *				{Object[]} specific
	 *					{Object} date
	 *						{Number} year - 1-9999
	 *						{Number} month - 1-12
	 *						{Number} day - 1-31
	 *					{Period[]} periods
	 *			{Number} timeslot - in minutes
	 *			{Number} minTime - minimum fulfillment time in minutes
	 *			{Number} maxTime - maximum fulfillment time in minutes
	 */
	// Fulfillment.pickupTimes = async function(placeId, items, options) {
	// 	const time = {
	// 		hours: [], 		// same structure as openingHours
	// 		timeslot: 60,
	// 		minTime: 120,	// minutes
	// 		maxTime: 2880,	// 48 hours
	// 	}

	// 	return { time }
	// }
}
