/**
 *  @module Mixin:OrderFlow - Order model
 */

const { Orders } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ stepOf, preStepOf, isLastStep } = require('@perkd/utils')

const { FULFILLED } = Orders.Step,
	{ sales: Event } = REGISTRY

module.exports = function(Order) {

	/**
	 * Progress order to Step
	 * @param	{String} orderId
	 * @param	{String} step
	 * @param	{Date} [at]
	 * @return	{Order|void} updated instance
	 */
	Order.toStep = async function (orderId, step, at) {
		const whenStep = `when.${step}`,
			filter = {
				where: { id: orderId, [whenStep]: null }
			},
			order = await Order.findOne(filter)

		if (order) {
			return order.toStep(step, at)
		}
		appNotify(`[toStep] unable to progress to '${step}' - (order id: ${orderId})`, 'error')
	}

	/**
	 * Progress order to Step
	 * @param	{String} step
	 * @param	{Date} [at]
	 * @param	{Boolean} [canRepeat] - allow repeat of step, when updated to latest, eg. allocated (driver)
	 * @return	{Order} updated instance
	 */
	Order.prototype.toStep = async function (step, at = new Date(), canRepeat = false) {
		const STEP = step,
			{ id, flow = {}, when } = this.toJSON(),
			{ steps = [] } = flow,
			atStep = stepOf(STEP, flow),
			previousStep = steps[preStepOf(STEP, flow)],
			whenStep = `when.${STEP}`,
			whenPrev = `when.${previousStep}`,
			filter = {
				id,
				[whenStep]: null,
				[whenPrev]: { $ne: null }
			},
			updates = {
				$set: {
					'flow.at': atStep,
					[whenStep]: at
				}
			},
			fulfilled = isLastStep(flow, STEP)

		if (atStep === -1) return this		// ignore
		if (!when[previousStep]) throw new Error('not_ready')
		if (!canRepeat && when[STEP]) throw new Error(`already_${STEP}`)

		if (!canRepeat) {
			filter[whenStep] = null
		}

		// --- update flow & when
		if (fulfilled) updates.$set[`when.${FULFILLED}`] = at

		const order = await Order.findOneAndUpdate(filter, updates)
		if (!order) throw new Error(`already_${STEP}`)

		// --- events
		appEmit(Event.order.updated, order)

		if (fulfilled) {
			appEmit(Event.order.fulfilled, order)
		}

		return order
	}
}
