/**
 *  @module Mixin:PrintTicket	for fulfillments
 */

const { Fulfillments, Settings, Printers, Languages, Products } = require('@crm/types'),
	{ formatAddress, cloneDeep, Currencies } = require('@perkd/utils'),
	{ groupItemsByTag, Print, NONE } = require('@perkd/orders'),
	{ formatPrice, formatSerial, timeAndLabel } = Print,
	{ formatOptions, formatTitle } = Print.Kitchen

const { DELIVER, DINEIN } = Fulfillments.Type,
	{ LABEL, RECEIPT } = Printers.Type,
	{ KITCHEN } = Printers.Purpose,
	{ FULFILLMENT, LOCALE } = Settings.Name,
	{ en: EN } = Languages.Code,
	Q_PRINT = 'print'

module.exports = function(Fulfillment) {

	/**
	 * Construct content for fulfillment ticket
	 */
	Fulfillment.prototype.ticketContent = function () {
		const { type, itemList, currency: code, destination = {}, receipt = {}, note, createdAt } = this.toJSON(),
			{ app } = Fulfillment,
			settings = app.getSettings(FULFILLMENT),
			{ ticketHeader: header, ticketFooter: footer } = settings[type] ?? {},
			currency = Currencies.currency(code, true),
			{ name, position = [], postCode } = destination,
			{ short: address } = formatAddress(destination),
			[ first ] = position,
			{ queue } = receipt,
			queueStr = queue ? formatSerial(queue) : '',
			{ pickup, ticket } = this[type] ?? {},
			serial = type === DELIVER
				? ticket ? queueStr : ''
				: queueStr,
			spot = type === DELIVER
				? { name, address, postCode }
				: type === DINEIN ? { name, position: first } : undefined,
			at = type === DELIVER ? pickup : createdAt,
			{ time, timeLabel } = timeAndLabel(type, at),
			content = {
				type,
				ticket: ticket || serial || queueStr,
				serial,
				timeLabel,
				time,
				note,
				spot,
				items: [],
				header,
				footer
			}

		for (const item of itemList) {
			const { unitPrice = 0, quantity = 1, variant = {} } = item,
				{ title } = variant,
				price = formatPrice(unitPrice, currency)

			content.items.push({ title, quantity, price })
		}

		return content
	}

	/**
	 * Print Fulfillment Ticket for store
	 * @param {Place} store
	 * @param {Printer} [usePrinter] print to this
	 */
	Fulfillment.prototype.printTicket = async function(store, usePrinter) {
		const { type, recipient = {}, receipt = {} } = this,
			{ app } = Fulfillment,
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = EN ] = languages,
			settings = app.getSettings(FULFILLMENT),
			{ ticketTemplate } = settings[type] ?? {},
			{ fullName: to } = recipient,
			{ queue } = receipt,
			{ ticket = queue } = this[type] ?? {},
			TYPE = type.toUpperCase(),
			purpose = Printers.Purpose[TYPE],
			[ printer = usePrinter ] = store && !usePrinter ? await store.selectPrinter(purpose) : [],
			{ id: printerId } = printer ?? {},
			content = this.ticketContent()

		return this.queue(`${Q_PRINT}:${type}:${printerId}`, async () => {
			appNotify(`====== ${TYPE} #${ticket} =======\nrecipient: ${to}   printer: ${printerId}`, null, null, '-printer')

			if (printer) {
				await printer.print(content, ticketTemplate, { language })
					.catch(err => appNotify('[Fulfillment]printTicket', { err, ticketTemplate, content, language }, 'error'))
			}
			appNotify('', content, null, '-printer')
		})
	}

	/**
	 * Print Kitchen Order Ticket (sequential for each printer)
	 * @param {Place} store
	 * @param {Fulfillment} mainFulfill
	 * @param {String} [serial] - required for first print (ignored for re-prints)
	 */
	Fulfillment.prototype.printKitchenTicket = async function(store, mainFulfill, serial) {
		const { app } = Fulfillment,
			{ languages = [] } = app.getSettings(LOCALE),
			[ language = EN ] = languages,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ ticketTemplate, printTicket, printSubtickets } = kitchen

		if (!printTicket && !printSubtickets) return undefined

		const { itemList, currency: code, receipt = {} } = this,
			{ type, destination = {}, when = {}, pickup = {}, minTime, note } = mainFulfill.toJSON(),
			{ name, position = [] } = destination,
			[ first ] = position,
			{ ordered } = when,
			{ queue } = receipt,
			spot = type === DINEIN ? { name, position: first } : undefined,
			{ minTime: pickupTime } = pickup,
			at = type === DELIVER ? (pickupTime || minTime) : ordered,
			{ time, timeLabel } = timeAndLabel(type, at),
			{ totalQuantity, totalTime } = Fulfillment.prepareTimeAndQuantity(itemList),
			currency = Currencies.currency(code, true),
			content = {
				serial: formatSerial(queue || serial),
				type,
				spot,
				note
			},
			[ printer = {} ] = await store.selectPrinter(KITCHEN),
			{ id: printerId, type: printerType } = printer

		return this.queue(`${Q_PRINT}:kitchen:${printerId}`, async () => {
			appNotify(`====== ORDER #${serial}   prep time: ${totalTime} secs =======\nprinter: ${printerId}`, null, null, '-printer')

			if (printerType === RECEIPT) {
				content.timeLabel = timeLabel
				content.time = time

				await this.printReceiptKOT(store, printer, ticketTemplate, itemList, content, currency, language, totalQuantity)
			}
			else if (printerType === LABEL) {
				await this.printLabelKOT(store, printer, ticketTemplate, itemList, content, currency, language, totalQuantity)
			}
		})
	}

	/**
	 * Print Label type Kitchen Order Ticket
	 */
	Fulfillment.prototype.printLabelKOT = async function (store, printer, template, items, content, currency, language, total) {
		let count = 1

		for (const item of items) {
			const { unitPrice = 0, quantity = 1, variantOptions, variant = {} } = item,
				{ title } = variant

			content.title = formatTitle(title)
			content.price = formatPrice(unitPrice, currency)
			content.options = formatOptions(variantOptions)

			for (let i = 0; i < quantity; i++) {
				content.items = `${count++}/${total}`

				if (printer) {
					await printer.print(content, template, { language })	// must wait, otherwise tickets may be out of sequence
						.catch(err => appNotify('[Fulfillment]printLabelKOT', { err, template, content, language }, 'error'))
				}
				appNotify('', content, null, '-printer')
			}
		}
	}

	/**
	 * Print Receipt type Kitchen Order Ticket
	 */
	Fulfillment.prototype.printReceiptKOT = async function (store, printer, template, items, content, currency, language, total) {
		const { app } = Fulfillment,
			{ Kitchen } = app.models,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ printTicket, printSubtickets, subticketTemplate, itemSubticket } = kitchen

		content.items = formatKOTItems(items, currency)

		if (printer) {
			try {		// must await, otherwise tickets may be out of sequence
				if (printTicket) {
					await printer.print(content, template, { language })
				}
				if (printSubtickets) {
					const prioritized = Kitchen.prioritizeItems(items)

					content.items = formatKOTItems(prioritized, currency)
					await this.printSubKOT(store, printer, subticketTemplate, itemSubticket, content, language)
				}
			}
			catch (err) {
				appNotify('[Fulfillment]printReceiptKOT', { err, template, content, language }, 'error')
			}
		}
		appNotify('', content, null, '-printer')
	}

	/**
	 * Print Sub-tickets (receipt only)
	 */
	Fulfillment.prototype.printSubKOT = async function (store, mainPrinter, template, itemised = [], content, language) {
		const { app } = Fulfillment,
			{ items } = content,
			copy = cloneDeep(content),
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ printSubtickets = [] } = kitchen,
			grouped = groupItemsByTag(items, Products.TagType.KITCHEN),
			stations = Object.keys(grouped)

		for (const station of stations) {
			if (!printSubtickets.includes(station)) continue

			const stationItems = grouped[station],
				[ subPrinter ] = (station !== NONE)
					? await store.selectPrinter(KITCHEN, undefined, undefined, station)
					: [],
				printer = subPrinter || mainPrinter

			copy.station = (station !== NONE) ? station : undefined
			copy.items = stationItems

			// subticket
			if (printer) {
				await printer.print(copy, template, { language })
					.catch(err => appNotify('[Fulfillment]printSubKOT', { err, template, content: copy, language }, 'error'))
			}
			appNotify('', copy, null, '-printer')

			// itemised subtickets
			if (itemised.includes(station)) {
				for (const item of stationItems) {
					copy.items = [ item ]

					if (printer) {
						await printer.print(copy, template, { language })
							.catch(err => appNotify('[Fulfillment]printSubKOT', { err, template, content: copy, language }, 'error'))
					}
					appNotify('', copy, null, '-printer')
				}
			}
		}
	}

	// ----  Private Functions

	function formatKOTItems(items, currency) {
		const list = []

		for (const { unitPrice = 0, quantity = 1, variantOptions, product = {}, variant = {} } of items) {
			const { title } = variant,
				{ tags } = product,
				options = formatOptions(variantOptions),
				item = {
					title: title + (options ? ` *${options}` : ''),
					quantity,
					price: formatPrice(unitPrice, currency),
					product: { tags }
				}

			list.push(item)
		}

		return list
	}
}
