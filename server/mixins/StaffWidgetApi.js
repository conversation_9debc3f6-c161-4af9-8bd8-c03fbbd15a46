/**
 *  @module Model:StaffWidgetApi - used by staff widgets
 */
const { Context } = require('@perkd/multitenant-context')

module.exports = function(Order) {

	/*
	 * Sales summary by day for Store (Staff Widgets API)
	 */
	Order.orderStoreSummary = async function(storeId, start, end) {
		// FIXME
		return { hello: 'world', storeId, start, end }
	}

	/**
	 * Void current invoice & re-issue (Staff Widgets API)
	 * @param	{String} id of order
	 * @param	{String} [taxId]
	 * @param	{Boolean} [print]
	 * @return	{Object} invoice
	 */
	Order.reissueInvoice = async function (id, taxId, print) {
		const order = await Order.findById(id),
			{ invoice } = order?.receipt ?? {}

		if (!order) throw { statusCode: 404 }

		if (invoice) {
			await order.cancelInvoice()
		}
		if (taxId !== undefined) {
			const { receipt = {} } = order		// order.receipt updated in cancelInvoice()

			receipt.taxId = taxId
			await order.updateAttributes({ receipt })
		}

		return order.createInvoice(print)
	}

	/**
	 * Create Invoice for order, optionally print to checked-in location
	 * @param	{String} id of order
	 * @param	{Boolean} [print]
	 * @return	{Object} invoice
	 */
	Order.createPrintInvoice = async function (id, print) {
		const { Place } = Order.app.models,
			{ location = {} } = Context.appContext,
			{ placeId } = location ?? {},
			[ order, to ] = await Promise.all([
				Order.findById(id),
				Place.findById(placeId)
			]),
			{ receipt = {} } = order ?? {},
			{ invoice } = receipt,
			{ number } = invoice ?? {}

		if (!order) throw { statusCode: 404 }

		if (invoice) {
			if (!print) throw `Invoice already issued: ${number}`

			await order.printReceipt(to)
			return invoice
		}

		return order.createInvoice(print)
	}

	/**
	 * Print custom receipt (Staff Widgets API)
	 * @param	{String} id of order
	 * @return	{Object} receipt
	 */
	Order.printCustomReceipt = async function (id) {
		const { Place } = Order.app.models,
			{ location = {} } = Context.appContext,
			{ placeId } = location ?? {},
			[ order, to ] = await Promise.all([
				Order.findById(id),
				Place.findById(placeId)
			])

		if (!order) throw { statusCode: 404 }

		return order.printCustomReceipt(to)
	}

	// -----  Remote Methods  -----

	Order.remoteMethod('orderStoreSummary', {
		description: 'Sales summary by day for Store (Staff Widgets API)',
		http: { path: '/staff/orders/store/:id/summary', verb: 'get' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'start', type: 'date', required: true },
			{ arg: 'end', type: 'date', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('reissueInvoice', {
		description: 'Void current invoice & re-issue (Staff Widgets API)',
		http: { path: '/staff/orders/:id/invoice/reissue', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'taxId', type: 'string' },
			{ arg: 'print', type: 'boolean', default: true }
		],
		returns: { type: 'object', root: true, description: 'invoice' },
	})

	Order.remoteMethod('createPrintInvoice', {
		description: 'Create Invoice for order (Staff Widgets API)',
		http: { path: '/staff/orders/:id/invoice', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true },
			{ arg: 'print', type: 'boolean', default: true }
		],
		returns: { type: 'object', root: true, description: 'invoice' },
	})

	Order.remoteMethod('printCustomReceipt', {
		description: 'Print custom receipt (Staff Widgets API)',
		http: { path: '/staff/orders/:id/receipt', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true }
		],
		returns: { type: 'object', root: true, description: 'receipt' },
	})
}
