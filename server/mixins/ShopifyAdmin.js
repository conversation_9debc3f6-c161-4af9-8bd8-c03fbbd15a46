/**
 *  @module Mixin:ShopifyAdmin - endpoints for manual admin
 */
const { Providers, Fulfillments } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ buildThrough, getModule, isShopifyOrder } = require('@provider/shopify')

const { SHOPIFY } = Providers.PROVIDER,
	{ ORDERS } = Providers.Module,
	{ KITCHEN } = Fulfillments.Type,
	TRAP = 'trap',
	OPS = {}

module.exports = function(Shopify) {

	/**
	 *
	 * @param	{String[]} orderIds
	 * @return {Object[]} - orders
	 */
	Shopify.pushOrders = async function(shop, orderIds = []) {
		const { models } = this.app,
			{ Order, Fulfillment } = models,
			{ tenant } = Context,
			{ length } = orderIds,
			filter = {
				where: { id: { inq: orderIds } }
			},
			fulfillmentFilter = {
				where: { orderId: { inq: orderIds } }
			},
			R = { count: 0, orderIds: [] }

		// TODO: split & process by chunks
		if (!length) return R
		if (length > 250) return this.rejectErr({ statusCode: 413 })
		if (tenant === TRAP) return this.rejectErr({ statusCode: 401 })
		if (OPS[tenant]) return { statusCode: 102 }

		OPS[tenant] = true

		const api = await this.getProvider(SHOPIFY, ORDERS, shop)

		if (!api) {
			delete OPS[tenant]
			return R
		}
		const [ orders, fulfillments ] = await Promise.all([
				Order.find(filter),
				Fulfillment.find(fulfillmentFilter)
			]),
			fulfillmentsMap = {}

		for (const fulfillment of fulfillments) {
			const { type, orderId } = fulfillment

			if (type === KITCHEN) continue

			fulfillmentsMap[orderId] = fulfillmentsMap[orderId] || []
			fulfillmentsMap[orderId].push(fulfillment)
		}

		for (const order of orders) {
			const { id, external, when } = order

			try {
				// if (isShopifyOrder(order) || when.cancelled) continue

				const extOrder = await this.createOrder(order, fulfillmentsMap[order.id])
				if (!extOrder) continue

				const { customer } = extOrder,
					customerId = customer ? customer.id : undefined,
					shopify = { orderId: extOrder.id, customerId },
					data = {
						external: { ...external, shopify },
					}

				R.orderIds.push(id)
				await order.updateAttributes(data)
			}
			catch (error) {
				appLog('shopify_pushOrders_fail', { orderId: id, error })
			}
		}

		R.count = R.orderIds.length
		delete OPS[tenant]
		return R
	}

	Shopify.pullOrders = async function(orderIds = []) {
		const { tenant } = Context,
			{ length } = orderIds,
			R = { orders: [] }

		if (!length) return R
		if (length > 250) return this.rejectErr({ statusCode: 413 })
		if (OPS[tenant]) return { statusCode: 102 }

		OPS[tenant] = true

		// TODO: metafields

		const api = await this.provider()
		if (!api) {
			delete OPS[tenant]
			return R
		}

		const { orders: list } = await api.orders.list({ ids: orderIds, status: 'any' }),
			orders = []

		for (const order of list) {
			const created = await this.handleExtOrderCreated(order)
				.catch(err => {
					appLog('shopify.pullOrders_create_err', { err, order })
				})

			orders.push(created)
		}

		R.orders = orders
		delete OPS[tenant]
		return R
	}

	Shopify.pullOrdersInvite = async function(orderIds = []) {
		const { Order } = Shopify.app.models,
			{ tenant } = Context,
			{ length } = orderIds,
			filter = {
				where: {
					'external.shopify.orderId': { inq: orderIds },
					or: [
						{ personId: { exists: false } },
						{ personId: null }
					],
				}
			},
			R = { count: 0, orders: [] }

		if (!length) return R
		if (length > 250) return this.rejectErr({ statusCode: 413 })
		if (OPS[tenant]) return { statusCode: 102 }

		OPS[tenant] = true

		const api = await this.provider()
		if (!api) {
			delete OPS[tenant]
			return R
		}

		const { noInvite, nonMemberSale } = getModule(api).options || {}
		if (noInvite) {
			delete OPS[tenant]
			return R
		}

		const orders = await Order.find(filter)
		if (!orders.length) {
			delete OPS[tenant]
			return R
		}

		const { orders: extOrders } = await api.orders.list({ ids: orderIds, status: 'any' }),
			invites = []

		for (const extOrder of extOrders) {
			const { id, billing_address, customer, source_name, app_id, processed_at } = extOrder,
				orderId = id.toString(),
				order = orders.find(o => o.external.shopify.orderId === orderId),
				{ cardNumber } = order.program,
				{ phone: recipientPhone, country_code: recipientCountry } = billing_address || {},
				options = {
					through: buildThrough(source_name, app_id, processed_at),
					recipientCountry,
					recipientPhone,
					noInvite,
					nonMemberSale,
				},
				{ person, membership } = await Shopify.findOrJoinMembership(SHOPIFY, cardNumber, customer, options)
					.catch(err => {
						appLog('shopify.pullOrdersInvite_err', err)
					}),
				changes = {}
			if (membership && !nonMemberSale) {
				const { id: membershipId, tierLevel, cardNumber, programId, memberId } = membership

				changes.program = { id: programId, tierLevel, cardNumber }
				changes.memberId = memberId
				changes.membershipId = membershipId
			}
			if (person) {
				changes.personId = person.id
				const updated = await order.updateAttributes(changes)
				invites.push(updated)
			}
		}

		R.orders = invites.map(i => i.id)
		R.count = invites.length
		delete OPS[tenant]
		return R
	}

	// -----  Remote Methods  -----

	Shopify.remoteMethod('pushOrders', {
		description: 'Push Orders to Shopify (manual admin)',
		http: { path: '/orders/push', verb: 'post' },
		accepts: [
			{ arg: 'shop', type: 'string' },
			{ arg: 'orderIds', type: 'array', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Shopify.remoteMethod('pullOrders', {
		description: 'Pull Orders from Shopify (manual admin)',
		http: { path: '/orders/pull', verb: 'post' },
		accepts: [
			{ arg: 'orderIds', type: 'array', required: true },
		],
		returns: { type: 'object', root: true },
	})

	Shopify.remoteMethod('pullOrdersInvite', {
		description: 'Invite Customer to join as Member (manual admin)',
		http: { path: '/orders/invite', verb: 'post' },
		accepts: [
			{ arg: 'orderIds', type: 'array', required: true },
		],
		returns: { type: 'object', root: true },
	})
}
