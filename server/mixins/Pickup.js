/**
 *  @module Mixin:Pickup
 */

const { Fulfillments, Orders } = require('@crm/types'),
	{ preStepOf, hasStep } = require('@perkd/utils')

const { QUEUED, PREPARE, PACKED, COLLECTED } = Fulfillments.Step,
	{ OPEN, ERROR, FAILURE } = Fulfillments.Status,
	{ PICKUP, STORE, DELIVER, KITCHEN } = Fulfillments.Type,
	LIST_FIELDS = [ 'id', 'type', 'priority', 'minTime', 'maxTime', 'itemList', 'receipt', 'recipient', 'note', 'orderId' ],
	EXCEED_FACTOR = 1.5			// multiple of preparation time to consider exceeded preparation

module.exports = function(Order) {

	/**
	 * Get pickup orders ready for collection (PACKED) at store
	 * @param	{String} placeId - store
	 * @return	{Order[]|Object} orders ready for collection (sorted by collection time), OR
	 * 				when none, return ETA for next collection { nextEta: <date> } OR [] when no next
	 */
	Order.packedList = async function(placeId) {
		const { Fulfillment } = Order.app.models,
			whenCollected = `when.${COLLECTED}`,
			whenPacked = `when.${PACKED}`,
			toCollectFilter = {
				where: {
					placeId,							// fulfillFrom
					status: OPEN,
					type: { inq: [ PICKUP, STORE, DELIVER ] },
					[whenCollected]: null,				// not collected
					[whenPacked]: { neq: null },		// packed
					'flow.steps': COLLECTED,			// to be collected
				},
				order: 'minTime ASC',
				fields: LIST_FIELDS
			},
			fulfillments = await Fulfillment.find(toCollectFilter)

		if (!fulfillments.length) {
			// ETA for next collection
			const NOW = new Date(),
				filter = {
					where: {
						placeId,
						status: OPEN,
						[whenCollected]: null,
						minTime: { gt: NOW },
						or: [
							{ type: DELIVER },
							{
								type: { inq: [ PICKUP, STORE ] },
								[whenPacked]: null
							}
						]
					},
					order: 'minTime ASC'
				},
				next = await Fulfillment.findOne(filter)

			return next ? { nextEta: next.minTime } : []
		}

		// Ready for collection
		const orderIds = fulfillments.map(f => f.orderId),
			orderFilter = {
				where: {
					id: { inq: orderIds },
				},
				fields: [ 'id', 'receipt', 'note' ]
			},
			orders = await Order.find(orderFilter),
			list = []

		for (const order of orders) {
			const { id, note } = order.toJSON(),
				orderId = id.toString(),
				fulfilled = fulfillments.find(f => f.orderId.toString() === orderId)

			if (fulfilled) {
				const { receipt, itemList = [] } = fulfilled,
					quantity = itemList.reduce((total, i) => total + i.quantity || 0, 0)

				list.push({ orderId, quantity, fulfilled, receipt, note })
			}
		}

		return list.sort((a, b) => a.startTime - b.startTime)
	}

	/**
	 * Set Order and fulfillments as COLLECTED
	 * @param	{Date} [at] - occurred at
	 * @param	{String} [staffId] - not used for now (for through? event?)
	 */
	Order.prototype.collected = async function(at = new Date(), staffId) {
		const fulfillments = await this.fulfillments.find()

		// --- set child (main) fulfillments to collected
		for (const fulfillment of fulfillments) {
			const { flow, when, mainFulfillmentId } = fulfillment.toJSON(),
				{ steps = [] } = flow,
				previousStep = steps[preStepOf(COLLECTED, flow)]

			if (mainFulfillmentId) continue		// skip kitchen

			if (hasStep(COLLECTED, flow) && when[COLLECTED] === null && !!when[previousStep]) {
				await fulfillment.collected(at, undefined, staffId)
			}
		}

		await this.toStep(Orders.Step.COLLECTED, at)
	}

	/**
	 * Set Order and fulfillments as DELIVERED		// for StaffCard API
	 * @param	{Date} [at] - occurred at
	 * @param	{String} [staffId] - not used for now (for through? event?)
	 */
	Order.prototype.delivered = async function(at = new Date(), staffId) {
		// TODO
	}

	/**
	 * Get Orders requiring ATTENTION at store
	 * @param	{String} placeId - store
	 * @return	{Object} { prepareExceeded, failed }
	 */
	Order.attentionList = async function(placeId) {
		const [ prepareExceeded, failed ] = await Promise.all([
			this.prepareExceededList(placeId),
			this.errorStatusList(placeId)
		])

		return { prepareExceeded, failed }
	}

	/**
	 * List of Kitchen fulfillments exceeded Prepare startTime allowance
	 * @param {String} placeId
	 * @return {Object[]}
	 */
	Order.prepareExceededList = async function(placeId) {
		const { Fulfillment } = Order.app.models,
			NOW = new Date(),
			whenQueued = `when.${QUEUED}`,
			whenPrepare = `when.${PREPARE}`,
			whenPacked = `when.${PACKED}`,
			// 1. Preparation time exceeded
			pastMinTimeFilter = {
				where: {
					placeId,
					type: KITCHEN,
					status: OPEN,
					[whenQueued]: { neq: null },
					[whenPrepare]: { neq: null },
					[whenPacked]: null,
					minTime: { lt: NOW }
				},
				order: 'minTime ASC',
				fields: [ ...LIST_FIELDS, 'prepare', 'when' ]
			},
			pastMinTime = await Fulfillment.find(pastMinTimeFilter),
			prepareExceeded = pastMinTime.filter(fulfill => {
				const { prepare = {}, when = {} } = fulfill,
					{ startTime, endTime } = prepare,
					duration = endTime.getTime() - startTime.getTime(),
					allowance = duration * EXCEED_FACTOR

				return when.prepare < new Date(NOW.getTime() - allowance)
			})

		return prepareExceeded
	}

	/**
	 * List of Fulfillments in Error or Failure status
	 * @param {String} placeId
	 * @return {Object[]}
	 */
	Order.errorStatusList = async function(placeId) {
		const { Fulfillment } = Order.app.models,
			filter = {
				where: {
					placeId,
					type: KITCHEN,
					status: { inq: [ ERROR, FAILURE ] }
				}
			},
			list = await Fulfillment.find(filter)

		return list
	}

	/**
	 * List of OPEN Orders BEFORE Prepare for Store
	 * @param {String} placeId
	 * @return {Object[]}
	 */
	Order.openBeforePrepareList = async function(placeId) {
		const { Fulfillment } = Order.app.models,
			NOW = new Date(),
			whenPrepare = `when.${PREPARE}`,
			whenPacked = `when.${PACKED}`,
			filter = {
				where: {
					placeId,
					type: KITCHEN,
					status: OPEN,
					[whenPrepare]: null,
					[whenPacked]: null,
					'prepare.startTime': { gt: NOW }
				},
				order: 'prepare.startTime ASC'
			},
			list = await Fulfillment.find(filter)

		return list
	}
}
