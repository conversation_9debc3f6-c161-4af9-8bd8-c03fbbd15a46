/**
 *  @module Model:FulfillmentApi	used by Fulfillment/Kitchen widgets (Staff)
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Fulfillments, Settings } = require('@crm/types'),
	{ openFrom, openUntil } = require('@perkd/utils'),
	{ REGISTRY } = require('@perkd/event-registry-crm')

const { KITCHEN } = Fulfillments.Type,
	{ LOCALE } = Settings.Name,
	{ sales: Event } = REGISTRY,
	Q_FULFILL_ITEM = 'fulfill:items'

module.exports = function(Fulfillment) {

	// ---  Queue

	/**
	 * Set as (priority) QUEUED manually (Kitchen only)
	 * @param	{String} id of fulfillment
	 * @return	{Promise<Object[]>} preparing items - [{ id, product = {}, variant = {}, quantity }]
	 */
	Fulfillment.setQueued = async function (id) {
		const { kitchen } = Fulfillment.app.models,
			fulfillment = await Fulfillment.findById(id),
			{ type, when } = fulfillment ?? {}

		if (!fulfillment || type !== KITCHEN) throw new Error({ statusCode: 404 })
		if (when.queued !== null) throw new Error('already queued')

		return kitchen.toPrepare(fulfillment)
	}

	// ---  Prepare

	/**
	 * Get PREPARE list for Store (Kitchen Order Tickets)
	 * @param	{String} [type]
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 * @return	{Fulfillment[]|Object} fulfillments ready for preparation, OR
	 * 		when none, return ETA for next fulfillment { nextEta: <date> } OR [] when no next
	 */
	Fulfillment.getPrepareList = async function(type = KITCHEN, stations) {
		const { location = {} } = Context.appContext,
			{ id: placeId = null } = location,
			list = await Fulfillment.prepareList(placeId, type, stations)

		if (list.length) return list

		// ETA for next to prepare
		const next = await Fulfillment.nextToPrepare(placeId, type, stations),
			{ prepare = {} } = next ?? {},
			{ startTime: nextEta } = prepare

		return next ? { nextEta } : []
	}

	/**
	 * Set as PREPARE
	 * @param	{String} id
	 */
	Fulfillment.setPrepare = async function(id) {
		const { staff = {} } = Context.appContext,
			fulfillment = await Fulfillment.findById(id)

		if (!fulfillment) throw new Error({ statusCode: 404 })

		return fulfillment.preparing(new Date(), staff.id)
	}

	/**
	 * Cancel PREPARE
	 * @param	{String} id
	 */
	Fulfillment.cancelPrepare = async function(id) {
		const { staff = {} } = Context.appContext,
			fulfillment = await Fulfillment.findById(id)

		if (!fulfillment) throw new Error({ statusCode: 404 })

		return fulfillment.cancelPrepare(staff.id)
	}

	// ---  Pack

	/**
	 * Get list of fulfillments to PACK list for Store
	 * @param	{String} [type]
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 * @return	{Fulfillment[]|Object} fulfillments ready for packing, OR
	 * 		when none, return ETA for next fulfillment { nextEta: <date> } OR [] when no next
	 */
	Fulfillment.getPackList = async function(type = KITCHEN, stations) {
		const { location = {} } = Context.appContext,
			{ id: placeId = null } = location,
			list = await Fulfillment.packList(placeId, type, stations)

		if (list.length) return list

		// ETA for next to pack (end of next prepare)
		const next = await Fulfillment.nextToPrepare(placeId, type, stations),
			{ prepare = {} } = next ?? {},
			{ startTime: nextEta } = prepare

		return next ? { nextEta } : []
	}

	/**
	 * Set as PACKED
	 * @param	{String} id
	 */
	Fulfillment.setPacked = async function(id) {
		const { staff = {} } = Context.appContext,
			fulfillment = await Fulfillment.findById(id)

		if (!fulfillment) throw new Error({ statusCode: 404 })

		return fulfillment.packed(new Date(), staff.id)
	}

	/**
	 * Set Items as Fulfilled - single serve
	 * @param	{String} fulfillmentId
	 * @param	{Object[]} items list of { id, quantity }
	 */
	Fulfillment.itemsFulfilled = async function (fulfillmentId, items) {
		const NOW = new Date(),
			{ staff = {} } = Context.appContext,
			staffId = staff.id

		await this.queue(`${Q_FULFILL_ITEM}:${fulfillmentId}`, async () => {
			const fulfillment = await Fulfillment.findById(fulfillmentId),
				{ itemList, placeId } = fulfillment?.toJSON() ?? {}

			if (!fulfillment) throw new Error({ statusCode: 404 })

			for (const { id, quantity: qtyFulfilled = 1 } of items) {
				const item = itemList.find(i => i.id === id)
				if (!item) continue

				item.fulfilled ||= { quantity: 0 }
				item.fulfilled.quantity += qtyFulfilled

				if (item.fulfilled.quantity >= item.quantity) {
					item.fulfilledAt = NOW		// fully fulfilled
				}
				appEmit(Event.fulfillment.item.success, { id: fulfillmentId, item, placeId, staffId })
			}

			try {
				await fulfillment.updateAttributes({ itemList })

				// check if fully fulfilled
				const partial = itemList.some(({ fulfilledAt }) => !fulfilledAt)
				if (!partial) {
					await fulfillment.packed(new Date(), staffId)
				}
			}
			catch (err) {
				appNotify(`[itemsFulfilled] place: ${placeId}`, err, 'error')
			}
		})
	}

	/**
	 * Get Kitchen fulfillment by main fulfillmentId
	 * @param	{String} mainFulfillmentId
	 */
	Fulfillment.getKitchenFulfillment = async function (mainFulfillmentId) {
		const filter = {
			where: {
				type: KITCHEN,
				mainFulfillmentId
			}
		}

		return Fulfillment.findOne(filter)
	}

	// ---  Exceptions

	/**
	 * List of CANCELLED fulfillments for Store (today only)
	 * @param	{String} [type]
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 * @return	{Fulfillment[]} cancelled
	 */
	Fulfillment.getCancelledList = async function (type = KITCHEN, stations) {
		const { app } = Fulfillment,
			{ Place } = app.models,
			{ location = {} } = Context.appContext,
			{ id: placeId = null } = location,
			{ timeZone } = app.getSettings(LOCALE),
			{ openingHours } = await Place.findById(placeId) ?? {},
			today = new Date(),
			from = openFrom(openingHours, today, timeZone),
			to = openUntil(openingHours, today, timeZone)

		return Fulfillment.cancelledListForPeriod(placeId, type, stations, from, to)
	}

	// -----  Remote Methods  -----

	Fulfillment.remoteMethod('setQueued', {
		description: 'Immediately (priority) queue fulfillment (StaffCard API)',
		http: { path: '/staff/fulfillments/:id/queue', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Fulfillment id' },
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('getPrepareList', {
		description: 'Fulfillments to Prepare for Store  (StaffCard API)',
		http: { path: '/staff/fulfillments/prepare', verb: 'get' },
		accepts: [
			{ arg: 'type', type: 'string', description: "Type of fulfillment, default 'kitchen'" },
			{ arg: 'stations', type: [ 'string' ], http: { source: 'query' }, description: 'For these stations (name)' }
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('setPrepare', {
		description: 'Set fulfillment as PREPARE  (StaffCard API)',
		http: { path: '/staff/fulfillments/:id/prepare', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Fulfillment id' },
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('cancelPrepare', {
		description: 'Cancel PREPARE & revert to previous step  (StaffCard API)',
		http: { path: '/staff/fulfillments/:id/prepare/cancel', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Fulfillment id' },
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('getPackList', {
		description: 'Fulfillments to PACK list for Store  (StaffCard API)',
		http: { path: '/staff/fulfillments/pack', verb: 'get' },
		accepts: [
			{ arg: 'type', type: 'string', http: { source: 'query' }, description: "Type of fulfillment, default 'kitchen'" },
			{ arg: 'stations', type: [ 'string' ], http: { source: 'query' }, description: 'For these stations (name)' }
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('setPacked', {
		description: 'Set fulfillment as PACKED  (StaffCard API)',
		http: { path: '/staff/fulfillments/:id/packed', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Fulfillment id' },
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('itemsFulfilled', {
		description: 'Set Items as Fulfilled  (StaffCard API)',
		http: { path: '/staff/fulfillments/:id/fulfill/items', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Fulfillment id' },
			{ arg: 'items', type: [ 'object' ], required: true, description: '{ id, quantity } of fulfilled items' }
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('getCancelledList', {
		description: 'Cancelled list for Stations of Store, today only  (StaffCard API)',
		http: { path: '/staff/fulfillments/cancelled', verb: 'get' },
		accepts: [
			{ arg: 'type', type: 'string', http: { source: 'query' }, description: "Type of fulfillment, default 'kitchen'" },
			{ arg: 'stations', type: [ 'string' ], http: { source: 'query' }, description: 'For these stations (name)' }
		],
		returns: { type: 'object', root: true },
	})

	Fulfillment.remoteMethod('getKitchenFulfillment', {
		description: 'Get Kitchen fulfillment by main fulfillmentId  (StaffCard API)',
		http: { path: '/staff/fulfillments/kitchen', verb: 'get' },
		accepts: [
			{ arg: 'mainFulfillmentId', type: 'string', http: { source: 'query' }, required: true },
		],
		returns: { type: 'object', root: true },
	})

	// Callback API  (for app)

	Fulfillment.remoteMethod('availability', {
		description: 'Availability of fulfillment service  (Callback API)',
		http: { path: '/perkd/fulfillments/:type/availability', verb: 'post' },
		accepts: [
			{ arg: 'type', type: 'string', http: { source: 'path' }, required: true, enum: [ 'store', 'pickup', 'deliver' ] },
			{ arg: 'placeId', type: 'string', required: true, description: 'CRM store id' },
			{ arg: 'items', type: 'array', description: 'List of items to fulfill' },
			{ arg: 'from', type: 'date', description: 'Start time of fulfillment window, default: now' },
			{ arg: 'duration', type: 'number', description: 'Duration (minutes) of window' },
		],
		returns: { type: 'object', root: true },
	})
}
