/**
 *  @module Mixin:Offer
 */
const { Touchpoints } = require('@crm/types')

const { PERKD } = Touchpoints.Type,
	{ SHOP } = Touchpoints.Format

module.exports = function(Order) {

	/**
	 * Redeem Offers in Order.discountList
	 * @return	{Promise<Offer[]>}
	 */
	Order.prototype.redeemOffers = async function() {
		const { id: orderId, discountList, createdAt } = this,
			{ Offer } = Order.app.models,
			options = {
				through: { type: PERKD, format: SHOP, touchedAt: createdAt }
			},
			offerIds = discountList.map(({ offerId }) => String(offerId))

		if (!discountList.length) return []

		return Offer.redeem(offerIds, createdAt, options)
			.catch(err => {
				appLog('order_redeem_fail', { err, options, offerIds, orderId })
				return []
			})
	}

	/**
	 * Revert Offers in Order.discountList
	 * @param	{Object} [options]
	 * 				{Object} through
	 * @return	{Promise<Offer[]>}
	 */
	Order.prototype.revertOffers = async function(options) {
		const { id: orderId, discountList } = this,
			{ Offer } = Order.app.models,
			offerIds = discountList.map(({ offerId }) => offerId)

		if (!discountList.length) return []

		return Offer.revert(offerIds, options)
			.catch(err => {
				appLog('order_revert_fail', { err, options, orderId })
				return []
			})
	}

	/**
	 * Mark Offers in Order.discountList as authorized
	 * @return	{Promise<Offer[]>}
	 */
	Order.prototype.authorizeOffers = async function() {
		const { discountList } = this,
			{ Offer } = Order.app.models,
			offerIds = discountList.map(({ offerId }) => offerId)

		if (!discountList.length) return []

		const offers = await Offer.find({ where: { id: { inq: offerIds } } }),
			authorizOffers = offers.map(offer => offer.authorize())

		return Promise.all(authorizOffers)
			.catch(err => {
				appLog('order_autorize_offers_fail', { err, orderId: this.id })
				return []
			})
	}
}
