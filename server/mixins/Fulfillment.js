/**
 *  @module Mixin:Fulfillment  (Order model)
 */

const { Orders, Fulfillments, Touchpoints, Settings } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ countryCode, parsePhoneNumber, stepOf } = require('@perkd/utils'),
	{ isFulfilled, fulfillmentSteps, getBookingIds, spotName, Metrics } = require('@perkd/fulfillments'),
	{ isFromMachine } = require('@perkd/machines'),
	{ reservationsFrom } = require('@perkd/orders')

const { PACKED, ALLOCATED, COLLECTED, DELIVERED, FULFILLED, RETURNED } = Orders.Step,
	{ PICKUP, DELIVER, STORE, DIGITAL, DINEIN, VENDING_MACHINE } = Fulfillments.Type,
	{ KITCHEN } = Fulfillments.Service,
	{ SUCCESS, PENDING } = Fulfillments.Status,
	{ STEPS } = Fulfillments,
	{ FULFILLMENT } = Settings.Name,
	{ PERKD } = Touchpoints.Type,
	{ sales: Event } = REGISTRY,
	{ State } = Metrics

module.exports = function(Order) {

	/**
	 * Create fulfillments for order
	 * @param {Object[]} fulfillments - note 'when' values are strings, NOT dates
	 * @param {Date} fulfilledAt
	 */
	Order.prototype.createFulfillments = async function (fulfillments = [], fulfilledAt) {
		const { id, currency, itemList, acquired = {} } = this.toJSON(),
			{ Fulfillment } = Order.app.models,
			{ perkd = {} } = this.external,
			{ cardId } = perkd,
			{ location } = acquired,
			provider = isFromMachine(this) ? await Order.providerFromSpot(location) : undefined,
			created = []

		for (const fulfillment of fulfillments) {
			if (!fulfillment.type) continue

			const { recipient = {}, destination = {}, external = {}, when = {} } = fulfillment,
				{ phone } = recipient,
				{ country } = destination,
				code = countryCode(country),
				{ formattedNational = '', countryCode: cntryCode } = phone && code ? parsePhoneNumber(phone, code) : {},
				steps = fulfillmentSteps(fulfillment),
				[ fulfillFrom, fulfillProvider ] = await Promise.all([
					this.fulfillFromFor(fulfillment),
					isFromMachine(fulfillment) ? Order.providerFromSpot(destination) : provider
				])

			fulfillment.currency = currency
			fulfillment.itemList = itemList			// carry over all items for now
			fulfillment.orderId = id
			fulfillment.placeId ||= fulfillFrom
			fulfillment.status ||= PENDING
			fulfillment.provider = fulfillProvider
			fulfillment.recipient.phone = formattedNational
			fulfillment.recipient.countryCode = cntryCode || code

			if (steps) {
				const completed = Object.keys(when).filter(w => w !== null),
					step = steps.findLast(step => completed.includes(step)) || steps[0],
					at = stepOf(step, { at: 0, steps })

				when[step] ||= fulfilledAt
				fulfillment.when = when
				fulfillment.flow = { at, steps }
			}

			external[PERKD] = { cardId }
			fulfillment.external = external
			const instance = await Fulfillment.create(fulfillment)
			created.push(instance.toJSON())
		}

		return created
	}

	/**
	 * Create Kitchen fulfillment(s) for kitchen items
	 * @param {Object[]} items - type = kitchen only
	 * @param {Date} at
	 * @return {Fulfillment[]} fulfillments
	 */
	Order.prototype.createKitchenFulfillments = async function(items = [], at) {
		const { id: orderId, currency } = this,
			{ Metric, models } = Order.app,
			{ Fulfillment } = models,
			filter = {
				where: {
					orderId,
					type: { inq: [ PICKUP, DELIVER, STORE, DINEIN, VENDING_MACHINE ] }
				}
			},
			fulfillment = await Fulfillment.findOne(filter),
			{ id: mainFulfillmentId, type, scheduled, origin = {}, destination = {}, note } = fulfillment?.toJSON() ?? {},
			{ placeId } = (type === DELIVER) ? origin : destination,
			steps = STEPS[KITCHEN],
			flow = { at: 0, steps },
			when = { [steps[0]]: at },
			fulfill = {
				type: KITCHEN,
				status: PENDING,
				scheduled,
				flow,
				destination,
				note,
				currency,
				when,
				orderId,
				mainFulfillmentType: type,
				mainFulfillmentId
			},
			grouped = {},
			fulfillments = []

		// group items by kitchen
		for (const item of items) {
			const { variant = {} } = item,
				{ preparation = {} } = variant,
				{ placeId: kitchenId = placeId } = preparation,
				fulfilled = { quantity: 0 }

			grouped[kitchenId] ||= []
			grouped[kitchenId].push({ ...item, fulfilled })		// init for partial fulfillment
		}

		for (const [ kitchenId, items ] of Object.entries(grouped)) {
			fulfillments.push(
				Fulfillment.create({ ...fulfill, itemList: items, placeId: kitchenId })
			)
		}

		const instances = await Promise.all(fulfillments)

		// metric
		const tags = {
				orderId: String(orderId),
				type,
				table: spotName(destination)
			},
			opt = { child: Fulfillments.Type.KITCHEN, tags }

		for (const { id, placeId } of instances) {
			tags.id = String(id)
			tags.placeId = String(placeId)
			appMetric(Metric.fulfillment.state, State.ORDERED, opt)
		}

		return instances
	}

	/**
	 * Set status of ALL fulfillments of Order to fulfilled
	 * @param	{Object} [fulfillment] - immediate fulfill, mainly for digital fulfillments
	 * @return	{Fulfillment[]} fulfillments
	 */
	Order.prototype.fulfilled = async function(fulfillment) {
		const { id } = this,
			{ Fulfillment } = Order.app.models,
			[ updated, newFulfillment ] = await Promise.all([
				updateExistingFulfillments(id),
				createFulfillment(id, fulfillment)
			])

		if (newFulfillment) updated.push(newFulfillment)

		await this.updateAttributes({ 'when.fulfilled': new Date() })
		return updated

		async function updateExistingFulfillments(orderId) {
			const fulfillments = await Fulfillment.find({ where: { orderId } }),
				updates = []

			for (const existing of fulfillments) {
				if (existing.status !== SUCCESS) {
					updates.push(
						existing.updateAttributes({ status: SUCCESS })
					)
				}
			}
			return Promise.all(updates)
		}

		async function createFulfillment(orderId, fulfillment) {
			if (fulfillment) {
				fulfillment.orderId = orderId
				fulfillment.status = SUCCESS
				return Fulfillment.create(fulfillment)
			}
		}
	}

	/**
	 * Request fulfillment of this Order
	 * 		manual: no ops
	 * 		digital: emit event
	 * @return	{Fulfillment[]}
	 */
	Order.prototype.requestFulfillment = async function() {
		const { id, when, acquired } = this.toJSON(),
			{ Fulfillment } = Order.app.models,
			NOW = new Date(),
			filter = {
				where: { orderId: id },
				order: 'mainFulfillmentId DESC'		// ensure main fulfill requested AFTER dependents (which updates min/max time of main)
			},
			fulfillments = await Fulfillment.find(filter),
			fulfilled = []

		if (!fulfillments.length) return []		// 3rd party order (POS...), no fulfillment

		if (isFulfilled(fulfillments)) {
			when[FULFILLED] = NOW
			this.updateAttributes({ when })
			return []
		}

		if (!this.isAutoFulfill(fulfillments)) return []

		for (const fulfillment of fulfillments) {
			if (fulfillment.status !== PENDING) continue

			const requested = await fulfillment.request(NOW, acquired)

			if (requested) fulfilled.push(requested)
		}

		return fulfilled
	}

	/**
	 * Cancel fulfillments of order
	 * @param	{Object[]} [items] to cancel (partial, NOT supported yet)
	 * @return	{Fulfillment[]}
	 */
	Order.prototype.cancelFulfillment = async function(items = []) {
		const { id, itemList, when } = this,
			{ Booking } = Order.app.models,
			orderId = String(id),
			{ returned, fulfilled } = when,
			NOW = new Date()

		if (returned) {
			console.error(`Unable to cancel, order ${RETURNED}`)
			return
		}

		const bookingIds = getBookingIds(itemList),
			fulfillments = await this.fulfillments.find(),
			cancelledBookings = []

		// cancel TICKETS (bookings)
		for (const bookingId of bookingIds) {
			const cancelled = await Booking.cancel(bookingId)
			cancelledBookings.push(cancelled.toJSON())
		}
		if (cancelledBookings.length) {
			const reservations = reservationsFrom(cancelledBookings)

			// Emit cancelled event for each reservation
			for (const reservationId in reservations) {
				appEmit(Event.booking.reservation.cancelled, {
					reservationId,
					bookings: reservations[reservationId]
				})
			}
		}

		for (const fulfillment of fulfillments) {
			await fulfillment.cancel(itemList)
		}

		const changes = { 'when.cancelled': NOW }
		if (fulfilled) changes['when.returned'] = NOW
		await this.updateAttributes(changes)

		return fulfillments
	}

	/**
	 * Determine where to fulfill from
	 * @param	{Object} fulfillment
	 * @return	{String} placeId
	 */
	Order.prototype.fulfillFromFor = async function(fulfillment = {}) {
		const { storeId } = this,
			{ app } = Order,
			{ type, destination = {} } = fulfillment,
			{ placeId } = destination

		if ([ STORE, DINEIN, PICKUP ].includes(type)) {
			return placeId
		}

		// deliver
		const { deliver = {} } = app.getSettings(FULFILLMENT),
			{ fulfillFrom } = deliver

		return storeId || fulfillFrom
	}

	// ---  Fulfillment event handers  ---

	/**
	 * Packed
	 * @param	{Object} fulfillment - event
	 */
	Order.fulfillmentPacked = async function(fulfillment) {
		const { orderId, when } = fulfillment,
			at = new Date(when[PACKED])

		await Order.toStep(orderId, PACKED, at)
	}

	/**
	 * Allocated - may have multiple occurrances due to re-allocating drivers
	 */
	Order.fulfillmentAllocated = async function(fulfillment) {
		const { orderId, when } = fulfillment,
			at = new Date(when[ALLOCATED])

		await Order.toStep(orderId, ALLOCATED, at, true)	// allow repeat
	}

	Order.fulfillmentCollected = async function(fulfillment) {
		const { orderId, when } = fulfillment,
			at = new Date(when[COLLECTED])

		await Order.toStep(orderId, COLLECTED, at)
	}

	Order.fulfillmentDelivered = async function(fulfillment) {
		const { orderId, when } = fulfillment,
			at = new Date(when[DELIVERED])

		await Order.toStep(orderId, DELIVERED, at)
	}
}
