/**
 *  @module Mixin:StoredValueMetrics - metrics for analytics & reporting
 */
const { Orders } = require('@crm/types')

const { STOREDVALUE } = Orders.ItemKind

module.exports = function(Order) {

	// -----  Stored Value  -----

	/**
	 * Transaction metrics for time period
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Object} { revenue, activeCustomers, averageDiscount, currency }
	 */
	Order.storedValuesMetricsTransactions = async function(from, to) {
		const [ first = {} ] = await Order.collection().aggregate([
				{
					$match: {
						'when.paid': { $ne: null },
						'when.cancelled': null,
						createdAt: { $gte: from, $lte: to }
					}
				},
				{
					$unwind: '$itemList'
				},
				{
					$match: {
						'itemList.variant.kind': STOREDVALUE
					}
				},
				{
					$group: {
						_id: null,
						revenue: { $sum: '$itemList.price' },
						transactions: { $addToSet: '$_id' },
						activeCustomers: { $addToSet: '$personId' },
						totalBalance: { $sum: '$itemList.variant.storedValue.balance' },
						totalDiscount: { $sum: '$itemList.discountAmount' },
						currency: { $first: '$currency' }
					}
				},
				{
					$addFields: {
						transactions: { $size: '$transactions' },
						activeCustomers: { $size: '$activeCustomers' },
						averageDiscount: { $divide: [ '$totalDiscount', '$revenue' ] }
					}
				}
			]).toArray(),
			{ revenue = 0, activeCustomers = 0, averageDiscount = 0, currency } = first

		return { revenue, activeCustomers, averageDiscount, currency }
	}

	/**
	 * Transaction history for time period
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Object} { transactionId, type, transactionDate, balance, discount, expiryDate, currency, customerId, orderId }
	 */
	Order.storedValuesMetricsHistory = async function(from, to) {
		const list = await Order.collection().aggregate([
			{ $match: {
				'when.paid': { $ne: null },
				'when.cancelled': null,
				createdAt: { $gte: from, $lte: to },
				'billingList.paymentMethod.method': 'storedvalue'
			} },
			{ $unwind: '$billingList' },
			{ $unwind: '$billingList.paymentMethod.transactions' },
			{ $project: {
				_id: 0,
				transactionId: '$_id',
				type: 'redeem',
				transactionDate: '$when.paid',
				amount: { $multiply: [ '$billingList.paymentMethod.amount', -1 ] },
				balance: '$billingList.paymentMethod.transactions.balance',
				discount: '$billingList.paymentMethod.transactions.details.discount',
				expiryDate: new Date('2024-07-31T23:59:59+0800'), // TODO: place holder
				currency: '$billingList.currency.userCurrency',
				customerId: '$external.shopify.customerId',
				orderId: '$external.shopify.orderId',
			} }
		]).toArray()

		return list
	}
}
