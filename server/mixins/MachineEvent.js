/**
 *  @module Mixin:MachineEvent - handle machine events for Orders & Fulfillments
 */

const { Fulfillments } = require('@crm/types'),
	{ machineIdFromSpot } = require('@perkd/machines')

const { VENDING_MACHINE } = Fulfillments.Type

module.exports = function(Model) {

	/**
	 * Get provider from acquired of Order
	 * @param	{Object} acquired
	 * @return	{String} provider
	 */
	Model.providerFromSpot = async function(spot = {}) {
		const { app } = Model,
			{ Vending, Kiosk } = app.models,
			{ type } = spot,
			machineId = machineIdFromSpot(spot),
			Machine = (type === VENDING_MACHINE) ? Vending : Kiosk,		// model
			filter = {
				where: { machineId }
			},
			machine = await Machine.findOne(filter),
			{ provider } = machine ?? {}

		return provider
	}

	/**
	 * Handle Vending Machine or Kiosk event, after injecting provider into context
	 * @param	{String} eventName to emit
	 * @param	{Through} acquired
	 *				{Object} location - { type, position... }
	 *				{Object} context - { spot, referenceId }
	 */
	Model.prototype.machineEvent = async function(eventName, acquired = {}) {
		const { app, name: ModelName } = Model

		try {
			const self = this.toJSON(),
				{ location } = acquired,
				provider = self.provider || await Model.providerFromSpot(location)

			acquired.context ||= {}
			acquired.context.provider = provider
			app.emit(eventName, { ...self, acquired })
		}
		catch (err) {
			console.error(`[${ModelName}]machineEvent`, { err, eventName, acquired })
			appNotify(`[${ModelName}]machineEvent`, { err, eventName, acquired }, 'error')
		}
	}
}
