{"restApiRoot": "/api", "host": "0.0.0.0", "port": 3102, "remoting": {"cors": false, "rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "100kb"}, "urlencoded": {"extended": true, "limit": "100kb"}}, "service": {"name": "Sales", "domain": "sales", "version": "1.0.0", "description": "", "multitenancy": true, "tenantCode": "", "tenants": {}, "appPath": "lib/", "settings": ["settings"], "autoStart": true, "canTerminate": true, "dependencies": {}, "state": {"now": 0, "text": "", "since": ""}}, "modules": {"metrics": {"enabled": true}, "eventbus": {"enabled": true}, "perkd": {"enabled": true}, "watchdog": {"enabled": true}, "provider": {"enabled": true}, "i18n": {"enabled": true, "namespaces": ["receipt"], "filepath": "../../lib/crm/i18n/"}, "mcp": {"enabled": true}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 10000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": true}}