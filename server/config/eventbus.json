{"definitions": "@perkd/event-registry-crm", "tenant": {"subscribe": ["person.visit.arrive", "sales.fulfillment.requested.store", "sales.fulfillment.requested.pickup", "sales.fulfillment.requested.deliver", "sales.fulfillment.requested.dinein", "sales.fulfillment.prepare.store", "sales.fulfillment.prepare.pickup", "sales.fulfillment.prepare.deliver", "sales.fulfillment.prepare.dinein", "sales.fulfillment.packed.store", "sales.fulfillment.packed.pickup", "sales.fulfillment.packed.deliver", "sales.fulfillment.packed.dinein", "sales.fulfillment.allocated.deliver", "sales.fulfillment.collected.deliver", "sales.fulfillment.delivered.deliver", "sales.fulfillment.collected.machine", "sales.fulfillment.collected.pickup", "product.queuing.processed"], "publish": ["sales.*", "watchdog.*"]}, "mapping": [{"from": "sales.fulfillment.requested.store", "to": "fulfillment.requested"}, {"from": "sales.fulfillment.requested.pickup", "to": "fulfillment.requested"}, {"from": "sales.fulfillment.requested.deliver", "to": "fulfillment.requested"}, {"from": "sales.fulfillment.requested.dinein", "to": "fulfillment.requested"}, {"from": "sales.fulfillment.prepare.store", "to": "fulfillment.prepare"}, {"from": "sales.fulfillment.prepare.pickup", "to": "fulfillment.prepare"}, {"from": "sales.fulfillment.prepare.deliver", "to": "fulfillment.prepare"}, {"from": "sales.fulfillment.prepare.dinein", "to": "fulfillment.prepare"}, {"from": "sales.fulfillment.packed.store", "to": "fulfillment.packed"}, {"from": "sales.fulfillment.packed.pickup", "to": "fulfillment.packed"}, {"from": "sales.fulfillment.packed.deliver", "to": "fulfillment.packed"}, {"from": "sales.fulfillment.packed.dinein", "to": "fulfillment.packed"}, {"from": "sales.fulfillment.allocated.deliver", "to": "fulfillment.allocated"}, {"from": "sales.fulfillment.collected.deliver", "to": "fulfillment.collected"}, {"from": "sales.fulfillment.delivered.deliver", "to": "fulfillment.delivered"}, {"from": "sales.fulfillment.collected.machine", "to": "fulfillment.collected"}, {"from": "sales.fulfillment.collected.pickup", "to": "fulfillment.collected"}], "MAXLEN": 5000, "LIMIT": 500}