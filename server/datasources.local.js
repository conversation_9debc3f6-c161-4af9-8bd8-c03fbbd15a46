const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
		REDIS_HOST = LOCALHOST,
		REDIS_PORT = 6379,
		REDIS_PASSWORD = '',
		SERVICE_HOST = LOCALHOST,
		ACCOUNT_HOST = SERVICE_HOST,
		PERSON_HOST = SERVICE_HOST,
		BUSINESS_HOST = SERVICE_HOST,
		MEMBERSHIP_HOST = SERVICE_HOST,
		PRODUCT_HOST = SERVICE_HOST,
		PLACE_HOST = SERVICE_HOST,
		MESSAGING_HOST = SERVICE_HOST,
		OFFER_HOST = SERVICE_HOST,
	} = process.env

module.exports = {
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=20&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true
	},
	accountRemote: {
		name: 'accountRemote',
		connector: 'remote',
		// url: `http://${ACCOUNT_HOST}:3060/api`,
		url: 'https://account.test.crm.waveo.com/api'
	},
	personRemote: {
		name: 'personRemote',
		connector: 'remote',
		// url: `http://${PERSON_HOST}:3101/api`,
		url: 'https://person.test.crm.waveo.com/api'
	},
	businessRemote: {
		name: 'businessRemote',
		connector: 'remote',
		// url: `http://${BUSINESS_HOST}:3120/api`,
		url: 'https://business.test.crm.waveo.com/api'
	},
	membershipRemote: {
		name: 'membershipRemote',
		connector: 'remote',
		// url: `http://${MEMBERSHIP_HOST}:3103/api`,
		url: 'https://membership.test.crm.waveo.com/api'
	},
	productRemote: {
		name: 'productRemote',
		connector: 'remote',
		url: `http://${PRODUCT_HOST}:3122/api`,
		// url: 'https://product.test.crm.waveo.com/api'
	},
	placeRemote: {
		name: 'placeRemote',
		connector: 'remote',
		// url: `http://${PLACE_HOST}:3115/api`,
		url: 'https://place.test.crm.waveo.com/api'
	},
	messagingRemote: {
		name: 'messagingRemote',
		connector: 'remote',
		// url: `http://${MESSAGING_HOST}:3011/api`,
		url: 'https://messaging.test.crm.waveo.com/api'
	},
	offerRemote: {
		name: 'offerRemote',
		connector: 'remote',
		// url: `http://${OFFER_HOST}:3125/api`,
		url: 'https://offer.test.crm.waveo.com/api'
	},
}
