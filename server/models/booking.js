/**
 *  @module Model:Booking
 */
const { Products, Bookings, Settings, Notify } = require('@crm/types'),
	{ ObjectId, shortId, dayjs, startOf, endOf } = require('@perkd/utils'),
	{ PRODUCT: PRODUCT_ERR } = require('@perkd/errors/dist/service')

const { LOCALE, BOOKING } = Settings.Name,
	{ TABLE } = Products.ResourceKind,
	{ PENDING, OPEN, SUCCESS, RESERVED, ENDED, NOSHOW } = Bookings.Status,
	{ Templates } = Notify,
	{ DEFAULT } = Settings,
	{ STAFF } = Settings.Name,
	{ BOOKING_UNAVAILABLE } = PRODUCT_ERR,
	Q_BOOKING = 'booking',
	CLEANUP_INTERVAL = 10000		// minimum ms between cleanups of expired and no-show bookings

module.exports = function(Booking) {

	/**
	 * Secure use of a Resource (pending) for time period, returns list of bookings
	 * @param	{Object[]} products { id, resourceId, priceRange }
	 * @param	{Date} from - must be in correct timezone
	 * @param	{Date} to - must be in correct timezone
	 * @param	{Number} quantity
	 * @param	{Number} [price] - must match price
	 * --- below are for table booking only
	 * @param	{Number} [partySize] - number of person of reservation
	 * @param	{Object} [preferences]
	 * 				{Boolean} [allowCombined] - allow combine multiple bookings into one
	 * 				{Boolean} [adjacentOnly] - allow only adjacent bookings
	 * @param	{Object} [customer]
	 *				{String} [personId] - associated person
	 *				{String} [membershipId] - associated membership
	 *				{Object} [cardId] - associated digital card
	 * @param	{Object} [options]
	 *				{String} [note] - customer instructions/comments
	 *				{Number} [ttl] - auto-cancel after ttl (milliseconds)
	 *				{String} [source] - queuing|website
	 *				{Boolean} [deposit] - has deposit or not
	 * @return	{Booking[]} pending bookings
	 */
	Booking.request = async function(products, from, to, quantity, price, partySize, preferences, customer, options) {
		const NOW = new Date(),
			{ models, Metric } = Booking.app,
			{ Resource } = models,
			resourceIds = products.map(p => p.resourceId),
			reservationId = shortId()

		// validations & enforce (any) policies
		if (to < NOW) throw BOOKING_UNAVAILABLE

		return this.queue(Q_BOOKING, async () => {
			const resources = await Resource.availableBy(resourceIds, from, to, quantity, price),
				requested = []

			let quantityRemain = quantity

			for (const resource of resources) {
				const { productId } = resource,
					product = products.find(p => p.id === productId),
					bookings = product
						? await Booking.createPendings(product, from, to, quantityRemain, partySize,
							preferences, resource, reservationId, customer, options)
						: [],
					quantityBooked = bookings.reduce((tot, { quantity }) => tot += quantity, 0)

				requested.push(...bookings)
				if ((quantityRemain -= quantityBooked) === 0) break
			}

			appMetric(Metric.booking.request)
			appMetric(Metric.booking.latency.request, NOW)

			if (quantityRemain > 0) {
				const tags = { resourceIds: JSON.stringify(resourceIds) }
				appMetric(Metric.booking.error.request, null, { tags })
			}

			return requested
		})
	}

	/**
	 * Create pending Booking for product (single for non-shared, maybe multiple for shared resource)
	 * 	- shared resource: split quantity into multiple bookings with quantity=1 each
	 * 	- private to Booking.request()
	 * @param	{Object} product - { id, resourceId }
	 * @param	{Date} from - must be in correct timezone
	 * @param	{Date} to - must be in correct timezone
	 * @param	{Number} quantity
	 * @param	{Number} [partySize] - number of person of reservation
	 * @param	{Object} [preferences] - { allowCombined, adjacentOnly }
	 * @param	{Object} resource - { kind, capacity, shared, placeId }
	 * @param	{String} reservationId - unique id shared across bookings under same reservation
	 * @param	{Object} [customer]
	 *				{String} [personId] - associated person
	 *				{String} [membershipId] - associated membership
	 *				{Object} [cardId] - associated digital card
	 * @param	{Object} [options] - { note, ttl, deposit, source }
	 * @return	{Booking[]} list of bookings
	 */
	Booking.createPendings = async function (product, from, to, quantity, partySize = 1, preferences, resource, reservationId, customer, options) {
		const { id: productId, resourceId } = product ?? {},
			{ kind, capacity, shared, placeId } = resource ?? {},
			{ personId, membershipId, cardId } = customer ?? {},
			{ note, ttl, deposit, source } = options ?? {},
			bookingData = {
				kind,
				status: PENDING,
				startTime: from,
				endTime: to,
				quantity: 1,		// fixed at 1
				capacity: shared ? 1 : capacity,
				partySize,
				preferences,
				note,
				expiresAt: ttl ? new Date(Date.now() + ttl) : undefined,
				resourceId,
				productId,
				placeId,
				reservationId,
				personId,
				membershipId,
				digitalCard: { id: cardId },
				deposit,
				source
			}

		if (shared) {
			const sold = await Booking.soldForDay(resourceId, from),
				qty = Math.min(quantity, capacity - sold),
				creates = []

			for (let i = 0; i < qty; i++) {
				creates.push(
					Booking.create(bookingData)
				)
			}

			const bookings = await Promise.all(creates)
			return bookings
		}

		// non-share resource (only 1 booking at a time)
		const query = {
				where: {
					resourceId,
					status: { nin: [ ENDED, NOSHOW ] },  // Exclude both ended and no-show
					startTime: { lte: to },
					endTime: { gte: from }
				}
			},
			[ booking, isNew ] = await Booking.findOrCreate(query, bookingData)

		if (!isNew) {
			console.error(`Already booked (by orderId: ${booking.orderId})`)		// FIXME
		}

		return isNew ? [ booking ] : []
	}

	/**
	 * Confirm (pending) Booking
	 * @param	{String} id of booking
	 * @param	{String} [orderId] associated order
	 * @param	{Object} [customer]
	 *			{String} [personId] - associated person
	 *			{String} [membershipId] - associated membership
	 *			{Object} [cardId] - associated digital card
	 */
	Booking.confirm = async function(id, orderId, customer) {
		const { Event, Metric, models } = Booking.app,
			{ Membership, Person } = models,
			{ personId, membershipId, cardId } = customer ?? {},
			filter = { id, status: PENDING },
			changes = {
				$set: {
					status: OPEN,
					expiresAt: undefined,
					orderId: orderId ? ObjectId(orderId) : undefined,
					personId: personId ? ObjectId(personId) : undefined,
					membershipId: membershipId ? ObjectId(membershipId) : undefined,
					digitalCard: { id: cardId }
				}
			}

		try {
			const booking = await Booking.findOneAndUpdate(filter, changes)

			appEmit(Event.booking.confirmed, booking)
			emitConfirmMetric(booking, personId, membershipId)
				.catch(err => {
					console.error('Booking.confirm %j', { err, booking, personId, membershipId, cardId })
				})
			return booking
		}
		catch (error) {
			appMetric(Metric.booking.error.confirm, null, { tags: { id }, error })
			return null
		}

		// metric used by Attendance applet
		async function emitConfirmMetric(booking = {}, personId, membershipId) {
			const [ membership, person ] = await Promise.all([
					membershipId ? Membership.findById(membershipId) : undefined,
					personId ? Person.findById(personId) : undefined
				]),
				{ resourceId } = booking,
				{ cardNumber } = membership ?? {},
				{ fullName } = person ?? {},
				tags = { id, resourceId, cardNumber, fullName }

			appMetric(Metric.booking.confirm, 1, { tags })
		}
	}

	/**
	 * Request and confirm bookings
	 * @param	{Object[]} products { id, resourceId, priceRange }
	 * @param	{Date} from - must be in correct timezone
	 * @param	{Date} to - must be in correct timezone
	 * @param	{Number} quantity
	 * @param	{Number} [price] - must match price
	 * --- below are for table booking only
	 * @param	{Number} [partySize] - number of person of reservation
	 * @param	{Object} [preferences]
	 * 				{Boolean} [allowCombined] - allow combine multiple bookings into one
	 * 				{Boolean} [adjacentOnly] - allow only adjacent bookings
	 * @param	{Object} [customer]
	 *				{String} [personId] - associated person
	 *				{String} [membershipId] - associated membership
	 *				{Object} [cardId] - associated digital card
	 * @param	{Object} [options]
	 *				{String} [note] - customer instructions/comments
	 *				{Number} [ttl] - auto-cancel after ttl (milliseconds)
	 *				{String} [source] - queuing|website
	 *				{Boolean} [deposit] - has deposit or not
	 *				{String} [orderId] - associated order
	 * @return	{Booking[]} bookings
	 */
	Booking.requestAndConfirm = async function (products, from, to, quantity, price, partySize, preferences, customer, options) {
		const requested = await Booking.request(products, from, to, quantity, price, partySize, preferences, customer, options),
			{ orderId } = options ?? {},
			confirmed = []

		try {
			for (const { id: bookingId } of requested) {
				const booked = await Booking.confirm(bookingId, orderId, customer)
				confirmed.push(booked)
			}
			return confirmed
		}
		catch (error) {
			// Rollback confirmed bookings if any failed
			await Promise.all(confirmed.map(b => Booking.cancel(b.bookingId)))
			throw error
		}
	}

	/**
	 * Cancel (confirmed) Booking
	 * @param	{String} id of booking
	 * @return	{Booking} booking (cancelled)
	 */
	Booking.cancel = async function(id) {
		const { Event, Metric } = Booking.app,
			booking = await Booking.findById(id),
			{ status } = booking,
			tags = { id }

		if (![ PENDING, OPEN, SUCCESS, RESERVED ].includes(status)) {
			appMetric(Metric.booking.error.cancel, null, { tags })
			throw new Error(`Unable to cancel '${status}' booking`)
		}

		await booking.delete()

		appMetric(Metric.booking.cancel, null, { tags })
		appEmit(Event.booking.cancelled, booking)
		return booking
	}

	/**
	 * Get Occupied (busy) time slots within period for given resources
	 * @param	{String[]} resourceIds
	 * @param	{Date} from
	 * @param	{Date} to
	 * @param	{Object} [options] - Additional options
	 * @param	{Boolean} [options.includeOverstay=false] - Whether to include overstaying bookings
	 * @return	{Object[]} occupied slots - { resourceId, startTime, endTime, quantity (taken) }
	 */
	Booking.occupied = async function(resourceIds = [], from, to, options = {}) {
		const { includeOverstay = false } = options

		// Standard occupancy filter
		const standardFilter = {
			where: {
				resourceId: { inq: resourceIds },
				status: { inq: [ OPEN, PENDING, SUCCESS, RESERVED ] },
				// Comprehensive overlap check: booking starts before 'to' AND booking ends after 'from'
				startTime: { $lt: to },
				endTime: { $gt: from }
			},
			fields: [ 'reservationId', 'resourceId', 'startTime', 'endTime', 'quantity' ],
			order: [ 'resourceId ASC', 'startTime ASC' ]
		}

		if (!includeOverstay) {
			return Booking.find(standardFilter)
		}

		// For the includeOverstay = true case, the overstayingFilter remains specific
		const overstayingFilter = {
			where: {
				resourceId: { inq: resourceIds },
				or: [ // Overstaying bookings are SUCCESS with arrival or RESERVED
					{ status: SUCCESS, arrivedAt: { exists: true } },
					{ status: RESERVED }
				],
				endTime: { $lt: from }, // Ended before the query period started
				departedAt: { $exists: false } // But not yet departed
			},
			fields: [ 'reservationId', 'resourceId', 'startTime', 'endTime', 'quantity' ],
			order: [ 'resourceId ASC', 'startTime ASC' ]
		}

		// Get both types of bookings
		// Standard bookings will use the improved overlap logic
		const [ standardBookings, overstayingBookings ] = await Promise.all([
			Booking.find(standardFilter), // This now uses the improved overlap logic
			Booking.find(overstayingFilter)
		])

		return [ ...standardBookings, ...overstayingBookings ]
	}

	/**
	 * Get active orders, current and future bookings for a resource
	 * @param	{String} resourceId
	 * @param	{String} timeZone - resource timezone
	 * @param	{Date} [endTime] - optional end time to limit future bookings
	 * @return	{Object[]} bookings and orders with customer information
	 */
	Booking.bookingsAndOrders = async function (resourceId, timeZone, endTime) {
		const { app } = Booking,
			{ Order } = app.models,
			now = dayjs().tz(timeZone).toDate(),
			currentBookingsFilter = {
				where: {
					resourceId,
					startTime: { lte: now },
					status: { inq: [ SUCCESS, RESERVED, OPEN ] }
				},
				order: 'endTime DESC',
				include: [ 'person' ]
			},
			futureBookingsFilter = {
				where: {
					resourceId,
					startTime: { gt: now },
					status: { inq: [ SUCCESS, RESERVED, OPEN ] }
				},
				order: 'startTime ASC',
				include: [ 'person' ]
			}

		// Add endTime filter if provided
		if (endTime) {
			futureBookingsFilter.where.startTime = {
				...futureBookingsFilter.where.startTime,
				lte: endTime
			}
		}

		const [ currentOrders, currentBookings, futureBookings ] = await Promise.all([
			Order.find({
				where: {
					resourceId,
					estimatedEndsAt: { gt: now },
					'when.paid': { neq: null },
					'when.cancelled': null
				},
				order: 'estimatedEndsAt DESC'
			}),
			Booking.find(currentBookingsFilter),
			Booking.find(futureBookingsFilter)
		])

		// Add customer information and exceeded time to each booking
		const addCustomerInfo = bookings => bookings.map(booking => {
			const bookingData = booking.toJSON()
			const person = bookingData.person

			// Add customer information if person exists
			if (person) {
				const mobilePhone = person.phones?.find(phone => phone.type === 'mobile')

				bookingData.customer = {
					name: person.fullName || `${person.givenName || ''} ${person.familyName || ''}`.trim(),
					phone: mobilePhone?.fullNumber || ''
				}

				// Remove the full person object to keep response clean
				delete bookingData.person
			}
			else {
				bookingData.customer = { name: '', phone: '' }
			}

			// Add exceeded time information if booking has passed its endTime but hasn't been closed
			if (bookingData.endTime && bookingData.arrivedAt && !bookingData.departedAt) {
				const endTime = dayjs(bookingData.endTime).tz(timeZone).valueOf()
				const currentTime = dayjs().tz(timeZone).valueOf()

				if (endTime < currentTime) {
					bookingData.exceededTime = Math.floor((currentTime - endTime) / (60 * 1000)) // in minutes
				}
			}

			return bookingData
		})

		return {
			currentOrders,
			currentBookings: addCustomerInfo(currentBookings),
			futureBookings: addCustomerInfo(futureBookings)
		}
	}

	/**
	 * Get (confirmed) Booking(s) for resources at a given time
	 * @param	{String[]} resourceIds
	 * @param	{Date} at
	 * @param	{String} [personId] only for specific person
	 */
	Booking.forResources = async function(resourceIds, at = new Date(), personId) {
		const { app } = Booking,
			{ timeZone } = app.getSettings(LOCALE),
			{ gracePeriod } = app.getSettings(BOOKING)[TABLE],
			referenceTime = dayjs(at).tz(timeZone),
			filter = {
				where: {
					resourceId: { inq: resourceIds },
					personId,
					status: { inq: [ OPEN, SUCCESS, RESERVED ] },
					// Find bookings starting within a wider window around now
					and: [
						{ endTime: { gte: referenceTime.subtract(gracePeriod, 'minute').toDate() } },
						{ startTime: { lte: referenceTime.add(gracePeriod, 'minute').toDate() } }
					]
				},
			},
			bookings = await Booking.find(filter)

		return bookings
	}

	/**
	 * Get quantity sold for (shared) resource (for unitPriceMeasure = 'day' only)
	 * 	- start and end of day in the configured timezone
	 * @param	{String} resourceId
	 * @param	{Date} at
	 */
	Booking.soldForDay = async function(resourceId, at) {
		const { app } = Booking,
			{ timeZone } = app.getSettings(LOCALE),
			startOfDay = startOf('day', at, timeZone),
			endOfDay = endOf('day', at, timeZone),
			[ res = {} ] = await Booking.collection().aggregate([
				{
					$match: {
						resourceId: ObjectId(resourceId),
						startTime: { $gte: startOfDay, $lte: endOfDay },
						status: { $in: [ SUCCESS, RESERVED, OPEN, PENDING ] }
					}
				},
				{
					$group: {
						_id: null,
						sold: { $sum: '$quantity' }
					}
				}
			]).toArray(),
			{ sold = 0 } = res

		return sold
	}

	/**
	 * Get pending bookings for a membership
	 * @param	{String} membershipId
	 * @return	{Booking[]} pending bookings
	 */
	Booking.pendingForMembership = async function(membershipId) {
		const { app } = Booking,
			{ timeZone } = app.getSettings(LOCALE),
			now = dayjs().tz(timeZone).toDate(),
			filter = {
				where: {
					membershipId,
					kind: TABLE,
					status: PENDING,
					deposit: false,
					expiresAt: { gte: now },
					endTime: { gte: now }
				}
			}

		return Booking.find(filter)
	}

	/**
	 * Get active reservation (with bookings) for resource
	 * @param	{String} resourceId
	 * @return	{Object|void} { id, partySize, startTime, endTime, note, deposit, bookings[] }
	 */
	Booking.activeReservationOfResource = async function(resourceId) {
		const { app } = Booking
		const { timeZone } = app.getSettings(LOCALE)
		const { maxDuration } = app.getSettings(BOOKING)[TABLE]
		const earliest = dayjs().tz(timeZone).subtract(maxDuration, 'minutes').toDate()

		// Find active booking for this resource
		const booking = await Booking.findOne({
			where: {
				resourceId,
				status: { inq: [ SUCCESS, RESERVED, OPEN ] },
				startTime: { gte: earliest },	// safety, exclude zombie old open bookings
			},
			order: 'createdAt DESC'
		})

		if (!booking) return undefined

		const { reservationId, partySize, startTime, endTime, note, deposit } = booking

		// Find all bookings under the same reservation
		const bookings = await Booking.find({
			where: {
				reservationId,
				status: { inq: [ SUCCESS, RESERVED, OPEN ] }
			},
			include: [ 'resource' ]
		})

		return {
			id: reservationId,
			partySize,
			startTime,
			endTime,
			note,
			deposit,
			bookings
		}
	}

	/**
	 * Get active reservation (list of bookings) for a resource
	 * @param	{String} resourceId - ID of the resource
	 * @return	{Object} Reservation details and bookings
	 */
	Booking.activeReservationByResource = async function(resourceId) {
		// Find active booking for this resource
		const booking = await Booking.findOne({
			where: {
				resourceId,
				status: { inq: [ SUCCESS, RESERVED, OPEN ] }
			},
			order: 'createdAt DESC'
		})

		if (!booking) {
			const error = new Error('No active booking found for this resource')
			error.statusCode = 404
			error.code = 'NO_ACTIVE_BOOKING'
			throw error
		}

		const { reservationId, partySize, startTime, endTime, note, deposit } = booking

		// Find all bookings under the same reservation
		const bookings = await Booking.find({
			where: {
				reservationId,
				status: { inq: [ SUCCESS, RESERVED, OPEN ] }
			},
			include: [ 'resource' ]
		})

		// Format the response
		return {
			reservation: {
				id: reservationId,
				partySize,
				startTime,
				endTime,
				note,
				deposit
			},
			bookings
		}
	}

	// -----  Event Handlers  -----

	/**
	 * Update arrivedAt time for booking when customer arrives (first time)
	 * 	- always one booking per check-in
	 * 	- only update if booking is still active (not yet ended)
	 * @param	{Object} data
	 *			{String} id - of person
	 *			{Object} context - {
	 *				spot: { placeId, resourceId },
	 *				booking: { id, resourceId, kind, capacity, placeId, reservationId },
	 *			}
	 */
	Booking.customerArrived = async function(data) {
		const { Metric } = Booking.app,
			{ context = {} } = data,
			{ booking } = context,
			{ id, kind, capacity, placeId, reservationId } = booking ?? {},
			NOW = new Date(),
			tags = { placeId, kind }

		if (!booking) return

		const count = await Booking.updateMany(
			{
				reservationId,
				arrivedAt: { $exists: false },
				endTime: { $gt: NOW }
			},
			{ $set: { arrivedAt: NOW } }
		)
			.catch(error => {
				console.error(`[Booking]customerArrived - update bookings for reservationId: ${reservationId} failed`, error)
			})

		if (count > 0) {
			appMetric(Metric.queuing.capacity, capacity, { tags })
		}
	}

	/**
	 * Notify staff for overstaying bookings
	 * 	- send push notification to staff card for bookings is overstaying 2 times the original duration (not yet ended)
	 * @param	{Object} data
	 *			{String} id - of person
	 *			{Object} context - {
	 *				spot: { placeId, resourceId },
	 *				booking: { reservationId, resourceId, kind, partySize, placeId, startTime, endTime },
	 *			}
	 */
	Booking.queuingProcessed = async function(data) {
		const { models } = Booking.app,
			{ Place } = models,
			{ context = {} } = data,
			{ booking } = context,
			{ reservationId, placeId, startTime, endTime } = booking ?? {},
			{ Booking: BOOKING_T } = Templates.Staff,
			{ TEMPLATES = {} } = DEFAULT.STAFF,
			{ templates = TEMPLATES } = Booking.app.getSettings(STAFF),
			NOW = new Date()

		if (!booking) return

		const place = await Place.findById(placeId)

		if (!place) return

		const bookings = await Booking.find({
			where: {
				reservationId,
				or: [
					{ status: SUCCESS, arrivedAt: { $exists: true } },
					{ status: RESERVED }
				],
				endTime: { $lt: new Date(endTime.getTime() + (endTime.getTime() - startTime.getTime()) * 0.5) }
			},
			include: [ 'resource' ]
		})

		if (bookings.length > 0) {
			const template = templates[BOOKING_T.OVERSTAY],
				position = bookings.map(b => b.position?.map(p => p.value).join(',')).filter(Boolean).join(','),
				duration = Math.round((NOW.getTime() - endTime.getTime()) / 60000)

			place.notifyStaff(template, { position, duration })
		}
	}

	// -----  Operation Hooks  -----

	let lastCleanup = 0

	/**
	 * Access hook to remove expired pending bookings
	 * IMPORTANT: MUST NOT use Booking.cancel() or any loopback methods to avoid infinite loops (ie trigger access hook again)
	 */
	Booking.observe('access', async ctx => {
		const { app } = Booking,
			{ Event } = app,
			{ gracePeriod } = app.getSettings(BOOKING)[TABLE],
			{ timeZone } = app.getSettings(LOCALE),
			now = dayjs().tz(timeZone),
			graceEnd = now
				.subtract(gracePeriod, 'minutes')
				.toDate()

		// Debounce cleanup operations
		if (now - lastCleanup < CLEANUP_INTERVAL) return

		try {
			lastCleanup = now.toDate()

			// --- update status of no-show bookings ---
			{
				const where = {
					status: OPEN,
					arrivedAt: { $exists: false },	// Not checked in
					startTime: { $lt: graceEnd }
				}

				// Bulk bookings that are past grace period without check-in
				await Booking.collection().updateMany(
					where,
					{
						$set: { status: NOSHOW, modifiedAt: now.toDate() }
					}
				)

				// Emit the "booking.noshow" event for each no-show booking
				Booking.collection().find({ status: NOSHOW, modifiedAt: now.toDate() }).toArray()
					.then(noShowBookings => {
						for (const booking of noShowBookings) {
							appEmit(Event.booking.noshow, booking)
						}
					})
					.catch(error => {
						console.error('Failed to emit "booking.noshow" events:', { error: error.message })
					})
			}

			// --- delete expired bookings ---
			{
				const where = {
					status: PENDING,
					expiresAt: { $lt: now.toDate() }
				}

				// Get the list of expired pending bookings before deletion
				const expiredBookings = await Booking.collection().find(where).toArray()

				// Remove expired pending bookings from the collection
				await Booking.collection().deleteMany(where)

				// Emit the "booking.deleted" event for each cleaned-up booking
				expiredBookings.forEach(booking => {
					appEmit(Event.booking.deleted, booking)
				})
			}
		}
		catch (error) {
			console.error('Failed to cleanup expired bookings:', { error: error.message })
		}
	})

	Booking.observe('before save', async ({ data, hookState }) => {
		hookState.status = data?.status
	})

	// Emit the 'booking.ended' event when a booking has ended
	Booking.observe('after save', async ({ instance, hookState }) => {
		if (hookState.status !== ENDED) return

		const { Event, Metric } = Booking.app,
			{ kind, capacity = 1 } = instance,
			placeId = String(instance.placeId),
			tags = { placeId, kind }

		appEmit(Event.booking.ended, instance)
		appMetric(Metric.queuing.capacity, -capacity, { tags })
	})

	/**
	 * Before delete hook to capture instances to be deleted
	 */
	Booking.observe('before delete', async ({ instance, where, hookState }) => {
		let instances = []

		if (instance) {
			// Single instance deletion
			instances.push(instance)
		}
		else if (where) {
			// Bulk deletion – find all instances matching the criteria
			instances = await Booking.find({ where })
		}
		// Save the instances for later use in the after delete hook
		hookState.deletedInstances = instances
	})

	/**
	 * After delete hook to emit event for each deleted booking
	 */
	Booking.observe('after delete', async ({ hookState }) => {
		const { Event } = Booking.app,
			{ deletedInstances = [] } = hookState

		// Emit an event for each deleted booking instance
		deletedInstances.forEach(instance => {
			appEmit(Event.booking.deleted, instance)
		})
	})
}
