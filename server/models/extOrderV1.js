/**
 *  @module Model:ExtOrderV1
 */

const { currencies } = require('country-data');

module.exports = function(ExtOrderV1) {
	ExtOrderV1.validate('currency', currencyCode, { message: 'Invalid currency code (must be ISO 4217)' });
	function currencyCode(err) {
		if (this.currency) {
			this.currency = this.currency.toUpperCase();
			if (!currencies[this.currency]) err();
		}
	}

	ExtOrderV1.validate('items', itemList);
	function itemList(err) {
		for (const item of this.items) {
			item.isValid(valid => {
				if (!valid) err();
			});
		}
	}

	ExtOrderV1.validate('purchaseAt', purchaseAt);
	function purchaseAt(err) {
		if (this.purchaseAt > Date(Date.now() + 10000)) err(); // allow 10 seconds
	}
	ExtOrderV1.validatesAbsenceOf('cancelledAt', { if: 'isNewRecord' });
	ExtOrderV1.validatesAbsenceOf('reason', { if: 'isNewRecord' });
};
