/**
 *  @module Model:Item
 */

module.exports = function(Item) {

	/**
	 * Inject Product & Variant information into Item
	 * @param	{Object} item
	 * @return	{Promise<Item>}
	 */
	Item.injectProductAndVariant = async function(item = {}) {
		const [ product, variant ] = await Promise.all([
			getProduct(item),
			getVariant(item),
		])

		if (product) {
			const { id, title, brand, attributes, external, tags, behaviors, bundleList, variationList, businessId } = product

			item.product = { id, title, brand, attributes, external, tags, behaviors, bundleList, variationList, businessId }
			item instanceof Item
				? item.unsetAttribute('productId')
				: delete item.productId
		}
		if (variant) {
			const { id, title, gtin, sku, mpn, cost, weight, inventory, prices, taxable, variation, options, attributes,
				storedValue, fulfillmentService, preparation, digital, external, productId, businessId } = variant

			item.variant = {
				id, title, gtin, sku, mpn, cost, weight, inventory, prices, taxable, variation, options, attributes,
				storedValue, fulfillmentService, preparation, digital, external, productId, businessId
			}
			item instanceof Item
				? item.unsetAttribute('variantId')
				: delete item.variantId
		}

		return item
	}

	async function getProduct({ productId, product }) {
		const { Product } = Item.app.models

		return productId && !product ? Product.findById(productId) : null
	}

	async function getVariant({ variantId, variant }) {
		const { Variant } = Item.app.models

		return variantId && !variant ? Variant.findById(variantId) : null
	}
}
