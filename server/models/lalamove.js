/**
 *  @module Model:Lalamove	 (FulfillmentProvider)
 */
const { Providers } = require('@crm/types'),
	{ coordinates2Geo } = require('@provider/lalamove'),
	{ geo2Coordinates } = require('@provider/google')

const { LALAMOVE, GOOGLE } = Providers.PROVIDER

module.exports = function(Lalamove) {

	Lalamove.orderIdOf = function (data) {
		const { order = {} } = data,
			{ orderId = null } = order

		return orderId
	}

	Lalamove.handleExtOrderFulfillmentAccepted = async function(fulfillment, data) {
		// ignore
	}

	/**
	 * Driver allocated
	 * @param {Fulfillment} fulfillment instance
	 * @param {Object} data
	 *			{Object} driver - { driverId, name, phone, plateNumber, photo }
	 *			{Object} location - { lng, lat }
	 */
	Lalamove.handleExtOrderFulfillmentAllocated = async function(fulfillment, data) {
		const { driver = {}, location } = data,
			{ driverId, name, phone, plateNumber, photo } = driver,
			delivery = {
				id: driverId,
				location: coordinates2Geo(location),
				driver: { id: driverId, name, phone, plateNumber, photo }
			}

		await fulfillment.allocated(LALAMOVE, delivery)
	}

	Lalamove.handleExtOrderFulfillmentCollected = async function(fulfillment, data) {
		const { destination } = fulfillment,
			{ order, updatedAt } = data,
			{ orderId, driverId } = order,
			[ google, lalamove ] = await Promise.all([
				this.getProvider(GOOGLE),
				this.getProvider(LALAMOVE),
			]),
			NOW = new Date(),
			at = new Date(updatedAt.split('.')[0]),		// fix invalid local time format: 2023-08-14T14:06.00Z
			{ coordinates: from } = await lalamove.deliveries.retrieve(driverId, orderId),
			to = geo2Coordinates(destination.geo),
			{ duration } = await google.maps.distance(from, to, { departure: NOW }),
			eta = duration ? new Date(Date.now() + (duration * 60 * 1000)) : undefined

		await fulfillment.collected(at, eta)
	}

	Lalamove.handleExtOrderFulfillmentDelivered = async function(fulfillment, data) {
		await fulfillment.delivered()
	}

	Lalamove.handleExtOrderFulfillmentCancelled = async function(fulfillment, data) {
		appNotify('[lalamove]fulfillmentCancelled', { fulfillment })
	}

	Lalamove.handleExtOrderFulfillmentFailed = async function(fulfillment, data) {
		appNotify('[lalamove]fulfillmentFailed', { fulfillment })
	}
}
