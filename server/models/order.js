/**
 *  @module Model:Order
 *
 *	Ref:	https://developers.google.com/shopping-content/v2/reference/v2/orders/orders-guide
 * 			https://help.shopify.com/en/api/reference/orders
 */

const { Payments, Fulfillments, Touchpoints, Providers, Settings } = require('@crm/types'),
	{ adjustTaxForStoredValue, storedValuePayments } = require('@perkd/commerce'),
	{ groupItemsByService, hasPaymentMethod, reservationIn } = require('@perkd/orders'),
	{ fulfillmentType, orderSteps, resourceIdFor } = require('@perkd/fulfillments'),
	{ isFromMachine } = require('@perkd/machines'),
	{ isOpen } = require('@perkd/utils'),
	{ ORDER: ORDER_ERR } = require('@perkd/errors/dist/service')

const { STORE, PICKUP, DELIVER, VENDING_MACHINE, DINEIN } = Fulfillments.Type,
	{ KITCHEN, NONE } = Fulfillments.Service,
	{ MANUAL } = Payments.Method,
	{ PERKD } = Touchpoints.Type,
	{ INVENTORY } = Providers.Service,
	{ ORDERS } = Providers.Module,
	{ LOCALE, FULFILLMENT, ORDER } = Settings.Name,
	{ NOT_FOUND, ORDER_INVALID, ORDER_FULFILLED } = ORDER_ERR,
	AUTO_FULFILL = [ KITCHEN, VENDING_MACHINE ]

const CANCEL_REASON = 'customer'

module.exports = function(Order) {

	/**
	 * Should order be accepted (considering fulfillment)
	 * 	- only considers kitchen fulfillments for now
	 * @param	{Object} order
	 * @param	{Object[]} fulfillments
	 * @param	{Place|null} store
	 * @return	{Object}
	 *				{Boolean} accept
	 *				{Date} [readyAt] - accept only
	 */
	Order.toAccept = async function(order = {}, fulfillments = [], store) {
		const { app } = Order,
			{ Metric, models } = app,
			{ Kitchen } = models,
			{ Type } = Fulfillments,
			{ amount, items = [] } = order,
			{ openingHours } = store ?? {},
			{ timeZone } = app.getSettings(LOCALE),
			{ deliver = {} } = app.getSettings(FULFILLMENT),
			{ pickupBufferTime = 0 } = deliver,
			grouped = groupItemsByService(items),
			services = Object.keys(grouped),
			accept = { accept: true },
			reject = { accept: false }

		if (!services.includes(KITCHEN)) return accept	// non kitchen

		for (const fulfillment of fulfillments) {
			const { type, scheduled, provider } = fulfillment,
				{ minTime, maxTime } = scheduled,
				{ available = true, hours = openingHours } = store?.[type] ?? {}

			if (!available || !isOpen(hours, minTime, undefined, timeZone)) return reject
			if (![ PICKUP, DELIVER ].includes(type)) continue

			const fulfill = await Kitchen.injectPrepTime(fulfillment),		// prep time for items
				allocated = await Kitchen.schedule(fulfill),
				tags = { provider, minTime, maxTime }

			if (allocated) {
				const { startTime, endTime, preparationTime } = allocated,
					earliest = new Date(startTime.getTime() + preparationTime),
					readyAt = new Date(earliest.getTime() + pickupBufferTime)

				appNotify(`[toAccept] ${provider}`, { type, earliest, pickupBufferTime, scheduled, endTime, allocated, amount }, 'good', '-kitchen')

				tags.readyAt = readyAt

				if (earliest <= new Date(maxTime)) {
					accept.readyAt = readyAt
					return accept
				}
			}

			appMetric(Metric.order.reject, 1, { child: Type.KITCHEN, tags })
			appNotify(`[toAccept] ${provider} - reject`, { type, scheduled, amount }, 'bad', '-kitchen')

			return reject
		}

		return accept	// no pickup/deliver
	}

	/**
	 * Create an Order (Used by Product & Membership service)
	 * @param	{Object} data
	 * @param	{Object} options
	 * 			{Object} location - { type: 'place', longitude: 99, latitude: 99 }
	 * @return	{Order} new Order
	 */
	Order.createOrder = async function(data, options = {}) {
		const NOW = new Date(),
			{ app } = this,
			{ Event, Metric, models } = app,
			{ Membership, Variant } = models,
			{ sendReceipt = false, printReceiptOnPay } = app.getSettings(ORDER),
			{ id, receipt = {}, quantity, currency, discountAmount, subtotalPrice, shippingPrice, amount } = data,
			{ taxIncluded, taxes, taxAmount, acquired, external = {}, sourceType, program } = data,
			{ itemList = [], discountList = [], billingList = [], fulfillmentList = [] } = data,
			{ when = {}, createdAt, expiresAt, personId, memberId, membershipId, storeId } = data, 	// totalPrice, items, discounts
			{ location } = options,
			order = {
				id, receipt, quantity, currency, discountAmount, subtotalPrice, shippingPrice, taxIncluded, taxes, taxAmount, amount,
				program, acquired, external, sourceType, discountList, billingList, personId, memberId, membershipId, storeId,
				when, expiresAt
			},
			type = fulfillmentType(fulfillmentList),
			steps = orderSteps(fulfillmentList),	// when type = DIGITAL, steps will be 'undefined'
			svPayments = storedValuePayments(billingList),
			tags = { source: PERKD, storeId }

		if (steps) {
			order.flow = { at: 0, steps }
			order.when[steps[0]] = NOW
		}
		// digital receipt
		if (receipt.send === undefined) {
			order.receipt.send = sendReceipt
		}
		// touchpoint
		if (!order.acquired) {
			order.acquired = {
				type: PERKD,
				location,
				touchedAt: createdAt ? new Date(createdAt) : NOW,
			}
		}
		if (svPayments.length) {
			adjustTaxForStoredValue(order, svPayments)		// mutates order.taxAmount
		}

		await Order.validateOrder(order, itemList, tags)

		// TODO: handle order that needs manual acceptance?
		// Order.setAccept(order)

		try {
			const { attributedTo = {} } = acquired,
				{ name: shop } = attributedTo,
				[ items, provider ] = await Promise.all([
					Variant.completeItems(itemList),
					this.getProviderByService(INVENTORY, ORDERS, shop),
				]),
				grouped = groupItemsByService(items),
				services = Object.keys(grouped),
				perkd = external[PERKD] || {}

			order.itemList = items

			// ----  Membership  ----
			if (membershipId && !perkd.cardId) {		// inject cardId
				const membership = await Membership.findById(membershipId),
					{ digitalCard = {} } = membership

				perkd.cardId = digitalCard.id
				order.external[PERKD] = perkd
			}

			// ----  Dine-in: inject resourceId (table)  ----
			if (type === DINEIN) {
				order.resourceId = resourceIdFor(fulfillmentList)
			}

			// ----  Create Order  ----

			// Provider: create order and deduct inventory
			if (provider) {		// deduct inventory from orderProvider first
				const { name } = provider,
					providerModel = models[name]
				console.log('debug [createShopifyOrder] %j', { order, fulfillmentList })
				const externalOrder = await providerModel.createOrder(order, fulfillmentList),
					{ id: extOrderId, fulfillmentOrderId, customer } = externalOrder ?? {}
				console.log('debug [createShopifyOrder-result] %j', externalOrder)
				if (extOrderId) {
					order.external[name] = {
						customerId: customer?.id,	// customer may be null
						orderId: extOrderId,
						fulfillmentOrderId,
						shop
					}
				}
			}

			const instance = await this.create(order),
				{ paid } = instance.when

			if (!paid) await instance.authorizeOffers() // hold the discount offers until payment is completed or order is cancelled

			// ----  Fulfillment  ----		// TODO handle split fulfillments for delivery
			const fulfillments = await instance.createFulfillments(fulfillmentList, NOW)

			// ----  Emit Events  ----
			await instance.emitEvent('created', { fulfillments })	// make sure 'created' emitted BEFORE 'paid'
			if (hasPaymentMethod(instance, MANUAL)) {
				appEmit(Event.order.manual.created, instance)
			}

			// Kitchen: need to generate separate fulfillment for kitchen preparation
			for (const service of services) {
				if (service === KITCHEN) {
					const kitchenItems = grouped[service]
					await instance.createKitchenFulfillments(kitchenItems, NOW)
				}
			}

			// ---- Dine-in: refresh dining end time  ----
			if (type === DINEIN) {
				await instance.refreshDiningEndTime()
			}

			// ----  Payment  ----
			if (paid) {
				await instance.markPaid(null, true)
			}
			else if (!billingList.length && services.includes(KITCHEN)) {	// pay-on-delivery
				instance.requestFulfillment()
			}

			// ----  Print in-store Receipt  ----
			if (printReceiptOnPay && paid && type === STORE) {
				const { receipt = {} } = instance

				receipt.print = true
				instance.updateAttributes({ receipt })
			}

			appMetric(Metric.order.create, 1, { tags })
			appMetric(Metric.order.latency.create, NOW, { tags })

			return instance
		}
		catch (error) {
			tags.id = id
			appMetric(Metric.order.error.create, 1, { tags, error })
			appLog('order_create_fail', { error, data, options })
			throw error
		}
	}

	/**
	 * Mark an Order as Paid (Used by Product & Membership service)
	 * @param	{Object} data - updates, usually bilingList, itemList & membershipId
	 * @return	{Order}
	 */
	Order.markPaid = async function(data) {
		const { id } = data,
			order = await this.findById(id)

		return order ? order.markPaid(data) : this.rejectErr(NOT_FOUND, data)
	}

	/**
	 * Cancel an Order
	 * @param	{String} id
	 * @param	{Object} [options]
	 * 				{Date} cancelledAt
	 * 				{String} reason
	 * @return	{Order|null}
	 */
	Order.cancel = async function(id, options) {
		const order = await Order.findById(id)
		return order ? order.cancel(options) : null
	}

	/**
	 * Cancel Order associated with transaction id
	 * @param	{String} transactionId
	 * @param	{Object} [options]
	 * 				{Date} cancelledAt
	 * 				{String} reason
	 * @return	{Order|null}
	 */
	Order.cancelByTransaction = async function(transactionId, options) {
		const order = await Order.findByTransaction(transactionId)

		return order ? order.cancel(options) : null
	}

	/**
	 * Cancel Order by (external) order id of provider
	 * @param	{String} provider
	 * @param	{String} orderId of provider
	 * @param	{Object} [options]
	 * 				{Date} cancelledAt
	 * 				{String} reason
	 * @return	{Order|null}
	 */
	Order.cancelByProviderOrderId = async function(provider, orderId, options) {
		const order = await Order.findOneByProviderOrderId(provider, orderId),
			{ cancelled } = order?.when || {}

		return (order && !cancelled) ? order.cancel(options) : null
	}

	/**
	 * Validate order before committing
	 * 	1. Quantities tally (skip bundled items)
	 * 	- note: amount not necessarily equal to item total amount, have different standards
	 * @param	{Object} order data
	 * @param	{Object[]} itemList
	 * @param	{Object} tags - for metrics
	 * @return	{Promise<void>} throw if error
	 */
	Order.validateOrder = async function(order = {}, itemList = [], tags) {
		const { id, quantity } = order,
			{ Metric } = Order.app,
			totalQuantity = itemList.reduce((total, item) => item.bundleId ? total : total + item.quantity, 0)

		if (totalQuantity !== quantity) {
			tags.id = id
			tags.error = ORDER_INVALID
			tags.at = new Date()
			appMetric(Metric.order.error.create, 1, { tags })
			return this.rejectErr(ORDER_INVALID, { order, itemList })
		}
	}

	// -----  Instance Methods  -----

	/**
	 * Payment status of order - consistent with commerceLib.paymentStatus()
	 * @return {String} status - pending, paid, partiallypaid, refunded, cancelled
	 */
	Order.prototype.paymentStatus = function() {
		const { totalPrice, billingList = [] } = this,
			{ PAID, PARTIALLY_PAID, PENDING, REFUNDED, PARTIALLY_REFUNDED, CANCELLED } = Payments.Status,
			{ length } = billingList,
			paid = billingList.reduce((count, p) => (p.paymentMethod.status === PAID ? count + 1 : count), 0),
			cancelled = billingList.reduce((count, p) => (p.paymentMethod.status === CANCELLED ? count + 1 : count), 0),
			refunded = billingList.reduce((count, p) => (p.paymentMethod.status === REFUNDED ? count + 1 : count), 0)

		if (totalPrice === 0 || paid === length) return PAID
		if (cancelled === length) return CANCELLED
		if (refunded === length) return REFUNDED
		if (paid === 0) return PENDING

		return refunded > 0 ? PARTIALLY_REFUNDED : PARTIALLY_PAID
	}

	// /**
	//  * Clone Order
	//  * @return	{Order}
	//  */
	// Order.prototype.clone = function() {
	// 	const self = this;

	// 	return Promise.reject('To be implemented');
	// }

	/**
	 * Cancel Order
	 * @param {Object} options
	 *			{String} reason
	 *			{Date} at
	 * 			{Object} through
	 * @return {Order}
	 */
	Order.prototype.cancel = async function(options = {}) {
		const { id, when } = this,
			{ Event } = Order.app,
			{ returned } = when,
			{ reason = '', at = new Date(), through } = options,
			filter = {
				id,
				'when.cancelled': null,
				$or: [
					{ 'when.fulfilled': null },
					{ 'when.returned': { $ne: null } }
				]
			},
			changes = {
				$set: {
					'when.cancelled': at,
					'reasons.cancelled': reason
				}
			},
			updated = await Order.findOneAndUpdate(filter, changes)

		if (!updated) {
			if (!returned) throw ORDER_FULFILLED
			throw ORDER_INVALID
		}

		// cancel/refund payments
		const paymentsProcessed = await updated.cancelOrRefundPayments(options),
			evtData = { ...updated.toJSON(), through }

		if (paymentsProcessed) {
			const { transactions, errors } = paymentsProcessed

			if (errors.length) {
				appNotify('[orderCancel]', { errors })		// TODO
			}
		}

		// revert offers & cancel (pending) fulfillment requests
		await updated.revertPostPayment(options)

		appEmit(Event.order.cancelled, evtData)

		// prevent 'remoting' errors due to extraneous properties
		updated['when.cancelled'] = undefined
		updated['reasons.cancelled'] = undefined

		return updated
	}

	// -----  Private Methods  -----

	/**
	 * Mark Order as Paid with optional updates
	 * @param	{Object} [data] - updates, usually bilingList, itemList & membershipId
	 * @param	{Boolean} [forced] - execute even if when.paid already
	 * @param	{Object} [through]
	 * @return	{Promise<Order>}
	 */
	Order.prototype.markPaid = async function(data, forced, through) {
		const { receipt, when: WHEN, acquired } = this.toJSON(),
			{ Event } = Order.app,
			{ paid: PAID } = WHEN

		if (PAID && !forced) {
			console.error('[markPaid] already paid', this.toJSON())
			return
		}

		if (data || !PAID) {
			const { receipt: rept = {}, program, tags, when = {}, itemList, billingList, personId, memberId, membershipId } = data ?? {},
				{ number } = rept,
				updates = { program, itemList, billingList, personId, memberId, membershipId, tags }

			if (!PAID || data.when) {
				const paid = when.paid || PAID || new Date()
				updates.when = { ...WHEN, ...when, paid }
			}
			if (number) {
				updates.receipt = { ...receipt, number }
			}

			await this.updateAttributes(updates)
		}

		await this.postPayment()

		appEmit(Event.order.paid, this, { through })

		if (reservationIn(this)) {
			appEmit(Event.order.reservation.paid, this, { through })
		}
		if (isFromMachine(this)) {
			this.machineEvent(Event.order.machine.paid, acquired)
		}

		return this
	}

	/**
	 * Post payment actions
	 */
	Order.prototype.postPayment = async function() {
		const { id, receipt } = this,
			{ skipInvoice } = receipt

		if (!skipInvoice) {
			// For store orders, create invoice with print=true directly
			const mainFulfill = await Order.findMainFulfillment(id)
			const isStoreOrder = mainFulfill && mainFulfill.toJSON().type === STORE

			// Create invoice with print=true for store orders, false otherwise
			await this.createInvoice(isStoreOrder)
		}

		await this.redeemOffers()
		this.requestFulfillment()	// do not wait
	}

	/**
	 * Revert post payment actions
	 * @param {Object} options
	 *			{String} reason
	 *			{Date} at
	 * 			{Object} through
	 */
	Order.prototype.revertPostPayment = async function(options) {
		const { reason, at, through } = options,
			{ type } = through ?? {},
			cancelReason = type === PERKD ? reason : CANCEL_REASON

		return Promise.all([
			this.cancelInvoice(cancelReason, at),
			this.revertOffers({ through }),
			this.cancelFulfillment(),
		])
	}

	/**
	 * Refresh dining end time for (dine-in) order
	 * 	- simplified (for now): 30 mins after the order is fully served
	 */
	Order.prototype.refreshDiningEndTime = async function () {
		const fulfillments = await this.fulfillments.find(),
			mainFulfill = fulfillments.find(f => f.type === DINEIN),
			{ minTime = new Date(), itemList = [] } = mainFulfill ?? {},
			totalDuration = 30 * 60 * 1000,		// FIXME 30 minutes
			estimatedEndsAt = new Date(minTime.getTime() + totalDuration)

		if (!mainFulfill) return

		return this.updateAttributes({ estimatedEndsAt })
	}

	/**
	 * Get product category ???
	 * @return {Promise<Product[]>}
	 */
	Order.prototype.productCategory = async function() {
		const { itemList = [] } = this,
			categories = itemList.reduce((uniqueCategories, item) => {
				const { category } = item.product?.tags || {}
				if (category?.length) {
					for (const cat of category) {
						if (cat) uniqueCategories.add(cat)
					}
				}
				return uniqueCategories
			}, new Set())

		return Array.from(categories)
	}

	/**
	 * Get store for order
	 * @return {Promise<Place>}
	 */
	Order.prototype.getStore = async function() {
		const { id: orderId } = this,
			{ Place } = Order.app.models,
			storeId = this.acquiredAtStore()

		if (storeId) {
			return Place.findById(storeId, { fields: { id: true, name: true } })
				.catch(err => {
					appLog('getStore', { orderId, storeId, err })
				})
		}
	}

	/**
	 * Get membership (of customer) for order
	 * @return {Promise<Membership>}
	 */
	Order.prototype.getMembership = async function() {
		const { id: orderId, membershipId } = this,
			{ Membership } = Order.app.models

		return !membershipId ? null : Membership.findById(membershipId)
			.catch(err => {
				appLog('getMembership', { orderId, membershipId, err })
			})
	}

	/**
	 * Get store id for order
	 * @return {String}
	 */
	Order.prototype.acquiredAtStore = function() {
		const { acquired = {} } = this,
			{ location } = acquired,
			{ id, type } = location ?? {},
			{ Type } = Touchpoints

		return (type === Type.STORE) ? id : undefined
	}

	Order.prototype.isAutoFulfill = function(fulfillments = []) {
		const { itemList } = this

		if (fulfillments.some(fulfill => AUTO_FULFILL.includes(fulfill.type))) {
			return true
		}

		if (fulfillments.some(fulfill => fulfill.type === DELIVER && !!fulfill.provider)) {
			return true
		}

		return (itemList.some(item => {
			const { fulfillmentService = NONE } = item.variant || {}
			return AUTO_FULFILL.includes(fulfillmentService)
		}))
	}

	// emitEvent do not throw error to avoid interupting orderPay process, log the error instead
	Order.prototype.emitEvent = async function(evtKey, data = {}) {
		const order = this.toJSON(),
			{ Event } = Order.app,
			event = Event.order[evtKey]

		try {
			const [ membership, place, categories ] = await Promise.all([
				this.getMembership(),
				this.getStore(),
				this.productCategory()
			])

			if (membership) {
				order.membership = membership.toJSON()
				order.digitalCard = order.membership.digitalCard
			}
			if (place) order.place = place.toJSON()
			if (categories.length) order.productCategory = categories

			appEmit(event, Object.assign(order, data))
		}
		catch (error) {
			appLog('emitEvent', { error, event, data })
		}
	}

	// -----  Operation Hooks  -----

	Order.observe('before save', async ({ instance, data, hookState, currentInstance }) => {
		const { Item } = Order.app.models,
			updates = instance || data,
			{ itemList } = updates,
			{ receipt = {} } = data ?? {},
			{ invoice, print } = receipt,
			complete = !!itemList?.find(i => i.product?.id || i.variant?.id),
			injects = []

		// usually the products and variants are injected already, include mcom, shopify, grab, ubereats
		if (itemList && !complete) {
			for (const item of itemList) {
				injects.push(
					Item.injectProductAndVariant(item)
				)
			}

			await Promise.all(injects)
		}

		// receipt.invoice updated (first time only)
		hookState.printReceipt = invoice && print && !currentInstance?.receipt?.invoice
	})

	Order.observe('after save', async ({ instance, isNewInstance, hookState }) => {
		const { printReceipt } = hookState

		if (printReceipt) {
			instance.printReceipt()
		}

		if (!isNewInstance) {
			instance.emitEvent('updated') // FIXME: findOrCreate 'find' triggers 'updated' event
		}
	})
}
