/**
 *  @module Model:UberEats	 OrderProvider
 * 		https://developer.uber.com/docs/eats/guides/order-integration
 */
const { Fulfillments, Settings } = require('@crm/types'),
	{ shortId, delay } = require('@perkd/utils'),
	{ placeToSpot, recipientFrom } = require('@perkd/fulfillments')

const { PICKUP } = Fulfillments.Type,
	{ ORDER } = Settings.Name,
	WAIT_TIME = 15000,		// wait 15 seconds after fulfilled, before retrieving customer data
	CANCEL_REASON = 'customer'

module.exports = function(UberEats) {

	UberEats.orderIdOf = function (data) {
		const { id, order_id: orderId = id } = data
		return orderId
	}

	UberEats.toAppOrder = async function (data) {
		const { modelName: UBEREATS, app } = this,
			{ Place } = app.models,
			{ id: merchantId } = data.store,
			[ ubereats, store ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, merchantId)
			]),
			storeId = store ? String(store.id) : undefined,
			order = ubereats.orders.toAppOrder({ ...data, storeId })

		return order
	}

	// ---  External event handlers

	/**
	 * Scheduled Order
	 *	1. always accept (tentative) order, we may reject actual request (later)
	 * @param {Object} data - order details
	 */
	UberEats.handleExtOrderScheduled = async function(data) {
		const { modelName: UBEREATS, app } = this,
			{ Place } = app.models,
			{ id: merchantId } = data.store,
			orderId = this.orderIdOf(data),
			[ ubereats, store ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, merchantId)
			]),
			storeId = store ? String(store.id) : undefined

		appNotify('[ubereats]scheduledOrder', { orderId, merchantId, storeId, data })

		if (store) {
			await ubereats.orders.accept(orderId)
		}
		else {
			await ubereats.orders.reject(orderId)
		}
	}

	/**
	 * Request Order
	 *	1. place order
	 *	2. accept (or reject) UberEats order, mark UberEats fulfillment as requested
	 *	3. request fulfillment of order (initiate rest of fulfillments)
	 * @param {Object} data - order details
	 */
	UberEats.handleExtOrderRequest = async function(data) {
		const { modelName: UBEREATS, app } = this,
			{ Order, Variant, Place } = app.models,
			{ id: merchantId } = data.store,
			orderId = this.orderIdOf(data),
			[ ubereats, store ] = await Promise.all([
				this.getProvider(UBEREATS),
				Place.findOneByProviderStoreId(UBEREATS, merchantId)
			]),
			storeId = store ? String(store.id) : undefined,
			order = ubereats.orders.toAppOrder({ ...data, storeId }, { sendReceipt: false }),
			{ fulfillment, createdAt } = order,
			{ type } = fulfillment

		if (type === PICKUP && store) {
			fulfillment.destination = placeToSpot(store)
		}

		try {
			const { items } = order,
				missingSKU = items.some(i => i.sku === undefined)
			if (missingSKU) {
				await ubereats.orders.reject(orderId)
				appNotify('[ubereats]rejectOrderForMissingSKU', { orderId, merchantId, data }, null, '-order')
				return
			}

			const { accept, readyAt } = await Order.toAccept(order, [ fulfillment ], store)

			appNotify('[ubereats]requestOrder', { orderId, merchantId, accept, data }, null, '-order')

			if (!accept) {
				await ubereats.orders.reject(orderId)
				appNotify('[ubereats]rejectOrderForNotAllocated', { orderId, merchantId, data }, null, '-order')
				return
			}

			const userId = shortId(),
				pricings = [],
				through = {
					type: UBEREATS,
					attributedTo: { type: UBEREATS },
					touchedAt: createdAt
				},
				options = { through, noNotify: true },
				{ orderId: id } = await Variant.order(userId, order, pricings, options),
				[ newOrder ] = await Promise.all([
					Order.findById(id),
					ubereats.orders.accept(orderId, readyAt, id)
				])

			await newOrder.createInvoice(false)		// TW only, do not auto-print, print when packed
		}
		catch (err) {
			console.error(`[${UBEREATS}]requestOrder`, { orderId, err, data })
			appNotify(`[${UBEREATS}]requestOrder`, { orderId, err, data }, 'error')
			await ubereats.orders.reject(orderId)
			appNotify('[ubereats]rejectOrderForErrors', { orderId, merchantId, data, error: err }, null, '-order')
		}
	}

	UberEats.handleExtOrderFulfillmentAccepted = async function(fulfillment, data) {
		// ignore
	}

	/**
	 * Driver allocated
	 * @param {Fulfillment} fulfillment instance
	 * @param {Object} data
	 *			{String} order_id
	 *			{String} estimated_pick_up_time
	 */
	UberEats.handleExtOrderFulfillmentAllocated = async function(fulfillment, data) {
		const { modelName: UBEREATS } = this,
			{ order_id: orderId, estimated_pick_up_time: pickup } = data,
			delivery = {
				id: orderId,
				pickup: new Date(pickup)
			}

		await fulfillment.allocated(UBEREATS, delivery)
	}

	UberEats.handleExtOrderFulfillmentNearby = async function(fulfillment, data) {
		// ignore
	}

	UberEats.handleExtOrderFulfillmentCollected = async function(fulfillment, data) {
		await fulfillment.collected()
	}

	UberEats.handleExtOrderFulfillmentDelivered = async function(fulfillment, data) {
		await fulfillment.delivered()
	}

	UberEats.handleExtOrderFulfillmentCancelled = async function(fulfillment, data) {
		const { modelName: UBEREATS, app } = this,
			{ Order } = app.models,
			{ order_id: orderId, code = '', message = '' } = data,
			reason = `${message || CANCEL_REASON}${code ? ` (${code})` : ''}`,
			through = {
				type: UBEREATS,
				attributedTo: { type: UBEREATS },
				touchedAt: new Date()
			}

		return Order.cancelByProviderOrderId(UBEREATS, orderId, { reason, through })
	}

	UberEats.handleExtOrderFulfillmentFailed = async function(fulfillment, data) {
		appNotify('[ubereats]fulfillmentFailed', { fulfillment })
	}

	// ---  Internal (CRM) event handlers  (reminder: only handle OURS)

	UberEats.handleFulfillmentDeliverPacked = async function(fulfillment) {
		const { modelName: UBEREATS } = this,
			{ provider, external = {} } = fulfillment

		if (provider !== UBEREATS) return	// not ours, ignore

		const { orderId } = external[UBEREATS],
			ubereats = await this.getProvider(UBEREATS)

		await ubereats.fulfillments.ready(orderId)
	}

	/**
	 * 1. Get Recipient info from UberEats
	 * 2. Invite to join membership (unless suppressed in settings)
	 * 3. Set billing and mark order as paid
	 * @param {Object} orderData
	 */
	UberEats.handleOrderFulfilled = async function (orderData) {
		const { modelName: UBEREATS, app } = this,
			{ Order, Fulfillment } = app.models,
			{ id: orderId, billingList = [], external, acquired: through } = orderData,
			{ attributedTo = {} } = through ?? {},
			{ type } = attributedTo

		if (type !== UBEREATS) return	// not ours, ignore

		const { noInvite, nonMemberFirstSale = true } = app.getSettings(ORDER),
			opt = { orderId, noInvite, nonMemberFirstSale, through },
			{ orderId: order_id } = external[UBEREATS],
			filter = {
				where: {
					orderId,
					[`external.${UBEREATS}.orderId`]: order_id
				}
			}

		delay(WAIT_TIME).then(async () => {
			const [ crmOrder, fulfillment, ubereats ] = await Promise.all([
					Order.findById(orderId),
					Fulfillment.findOne(filter),
					this.getProvider(UBEREATS)
				]),
				order = await ubereats.orders.get(order_id),
				customer = ubereats.orders.customer(order),
				phones = ubereats.orders.phones(order),
				billing = ubereats.orders.billing(order),
				recipient = recipientFrom(customer),
				[ { person, membership } ] = await Promise.all([
					this.findOrJoinMembership(UBEREATS, undefined, customer, phones, opt),
					customer && fulfillment.updateAttributes({ recipient })
				]),
				{ id: personId, memberId } = person ?? {},
				{ id: membershipId } = membership ?? {}

			billingList.unshift(billing)
			await crmOrder.markPaid({ billingList, personId, memberId, membershipId })
		})
			.catch (err => appNotify('[UberEats]handleOrderFulfilled', err, 'error'))
	}
}