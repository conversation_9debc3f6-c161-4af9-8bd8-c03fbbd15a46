{"name": "<PERSON><PERSON>", "plural": "Items", "base": "Model", "idInjection": false, "strict": true, "mixins": {}, "properties": {"id": {"type": "string", "id": true, "defaultFn": "nanoid"}, "kind": {"type": "string", "enum": ["product", "booking", "membership", "storedvalue"]}, "product": {"type": "Product"}, "variant": {"type": "<PERSON><PERSON><PERSON>"}, "variantOptions": {"type": [{"type": "Option"}], "description": "Selected options of variant (adds to price of variant)"}, "quantity": {"type": "number", "default": 0}, "units": {"type": "number", "description": "For units-based pricing, eg. by time.  units x quantity x unitPrice = amount"}, "discountAmount": {"type": "number", "default": 0}, "tax": {"type": "number", "description": "Tax applicable to this Item"}, "price": {"type": "number", "default": 0}, "unitPrice": {"type": "number", "description": "Price of the item before discount"}, "unitPriceMeasure": {"type": {"unit": {"type": "string", "enum": ["day", "hour", "minute", "kg", "g", "ml", "l"], "description": "The unit of the measure. eg. 'ml', 'kg'"}, "value": {"type": "number", "default": 1, "description": "Increments of unit sold"}}}, "unitCostMeasure": {"type": {"unit": {"type": "string", "enum": ["day", "hour", "minute", "kg", "g", "ml", "l"], "description": "The unit of the measure. eg. 'ml', 'kg'"}, "value": {"type": "number", "default": 1, "description": "Increments of unit sold"}}}, "grossMargin": {"type": "number"}, "discount": {"type": "Discount"}, "bundled": {"type": ["string"], "description": "(item) ids of bundled items"}, "bundleId": {"type": "string", "description": "(Item) Id of parent item that this (bundled) Item is part of"}, "shippingMethodId": {"type": "string", "description": "reference shipping method embedded in Order"}, "fulfillmentType": {"type": "string", "enum": ["digital", "kitchen", "store", "pickup", "deliver"]}, "images": {"type": [{"type": "string"}], "default": [], "description": "Variant image urls, for display in OrderTracking"}, "properties": {"type": [{}], "description": "Custom data associated with item, eg. card number"}, "custom": {"type": "object", "description": "reserved for booking, reservation, membership (tenure)"}, "admit": {"type": "number", "description": "For booking orders only"}}, "methods": []}