{"name": "Billing", "plural": "Billing", "base": "Model", "idInjection": false, "strict": true, "mixins": {"Timestamp": true}, "properties": {"id": {"type": "string", "id": true, "defaultFn": "nanoid"}, "paymentMethod": {"method": {"type": "string", "max": 16, "enum": ["card", "bank", "alipay", "linepay", "cash", "storedvalue"], "description": "Method name"}, "processor": {"type": "string", "max": 32, "enum": ["stripe", "mypay", "tappay", "storedvalue"], "description": "Payment service provider name (key)"}, "brand": {"type": "string", "enum": ["visa", "mastercard", "amex", "jcb", "diners", "unionpay", "discover", "unknown"]}, "wallet": {"type": "string", "max": 32, "enum": ["applepay", "googlepay", "samsungpay", "masterpass", "visacheckout", "amexexpresscheckout"], "description": "Card wallet name"}, "paymentPackageId": {"type": "string"}, "paymentSettingsKey": {"type": "string"}, "transactionId": {"type": "string"}, "amount": {"type": "number"}, "status": {"type": "string", "enum": ["pending", "authorized", "partially_paid", "paid", "partially_refunded", "refunded", "voided"], "description": "Status of payment for this order"}, "mode": {"type": "string", "enum": ["capture"]}, "riskLevel": {"type": "string", "enum": ["normal", "elevated", "highest"]}, "transactions": {"type": ["object"], "default": [], "description": "Reverse chronological history of transactions from payment provider, ie. <PERSON>e"}, "redirectUrl": {"type": "string", "description": "URL given by provider to redirect user to complete payment in payment app"}, "items": {"type": [{"id": {"type": "number"}, "productId": {"type": "string"}, "variantId": {"type": "string"}, "quantity": {"type": "string"}}]}, "metadata": {"type": "object"}, "createdAt": {"type": "Date"}}, "invoice": {"shipping": {"type": "number", "description": "Amount for shipping charges"}, "subtotal": {"type": "number"}, "taxes": {"type": "number", "description": "Amount for taxes"}, "discounts": {"type": "number", "description": "Amount for discounts"}, "total": {"type": "number"}}, "currency": {"userCurrency": {"type": "String", "max": 3}, "exchangeRate": {"type": "number"}}, "address": {"fullName": {"type": "String"}, "country": {"type": "String", "length": 2, "description": "ISO 3166-1 alpha-2"}, "state": {"type": "String"}, "city": {"type": "String"}, "postCode": {"type": "String"}, "formatted": {"type": "String"}, "phoneNumber": {"type": "String", "max": 15, "description": "see E.164 standard"}, "isShippingDefault": {"type": "boolean"}, "isBillingDefault": {"type": "boolean"}, "isCommercial": {"type": "boolean"}, "failedValidation": {"type": "boolean"}}}, "acls": [], "scopes": {}, "methods": {}}