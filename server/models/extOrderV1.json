{"name": "ExtOrderV1", "base": "Model", "strict": true, "properties": {"orderId": {"type": "String", "description": "Unique external identifier for each order.", "required": true}, "customerId": {"type": "String", "description": "Unique external identifier for each customer.", "required": true}, "cardNumber": {"type": "String", "description": "Membership card number."}, "receiptNumber": {"type": "String", "description": "A unique identifier receipt number for the order.", "required": true}, "quantity": {"type": "Number", "description": "Number of products of this order", "required": true}, "currency": {"type": "String", "max": 3, "required": true}, "cost": {"type": "number"}, "discountAmount": {"type": "number", "description": "The sum of all the discounts applied to the order (must be positive)."}, "taxAmount": {"type": "number", "description": "The sum of all the taxes applied to the order (must be positive)."}, "amount": {"type": "number", "description": "Total amount (net sales)", "required": true}, "purchaseAt": {"type": "Date", "description": "The date and time this order was made.", "required": true}, "cancelledAt": {"type": "date"}, "reason": {"type": "String", "description": "The reason why the order was cancelled, allowed values: customer, fraud, inventory, other."}, "items": {"type": [{"type": {"productId": {"type": "string", "required": true}, "quantity": {"type": "number", "required": true}, "amount": {"type": "number", "required": true}, "discountAmount": {"type": "number"}, "grossMargin": {"type": "number"}}}], "description": "A list of item objects, each one containing information about an item in the order.", "required": true}, "storeId": {"type": "String", "description": "The store ID where this order created.", "required": true}}}