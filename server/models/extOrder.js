/**
 *  @module Model:ExtOrder
 */

const { currencies } = require('country-data');

module.exports = function(ExtOrder) {
	ExtOrder.validate('currency', currencyCode, { message: 'Invalid currency code (must be ISO 4217)' });
	function currencyCode(err) {
		const { currency } = this;

		if (currency) {
			this.currency = currency.toUpperCase();
			if (!currencies[currency]) err();
		}
	}
	ExtOrder.validate('store', store);
	function store(err) {
		const { store } = this;
		if (store) store.isValid(valid => (valid && store.name !== undefined) ? undefined : err());
	}
	ExtOrder.validate('staff', staff);
	function staff(err) {
		const { staff } = this;
		if (staff) staff.isValid(valid => (valid && staff.name !== undefined) ? undefined : err());
	}
	ExtOrder.validate('items', itemList);
	function itemList(err) {
		const { items } = this;
		if (items) {
			for (const item of items) {
				item.isValid(valid => {
					if (!valid) err();
					if (item.productTitle === undefined) err();
					if (item.variantTitle === undefined) err();
					if (item.gtin === undefined) err();
					if (item.sku === undefined) err();
					if (item.discount) item.discount.isValid(valid => valid ? undefined : err());
				});
			}
		}
	}
	ExtOrder.validate('discounts', discountList);
	function discountList(err) {
		const { discounts } = this;
		if (!Array.isArray(discounts)) err();
		else {
			for (let i = 0; i < discounts.length; i++) {
				discounts[i].isValid(valid => valid ? undefined : err());
			}
		}
	}
	ExtOrder.validate('payments', payments);
	function payments(err) {
		const { payments } = this;
		if (!Array.isArray(payments)) err();
		else {
			for (let i = 0; i < payments.length; i++) {
				payments[i].isValid(valid => valid ? undefined : err());
			}
		}
	}

	ExtOrder.validate('purchaseAt', purchaseAt, { message: 'is not a past date' });
	function purchaseAt(err) {
		const { purchaseAt, orderId } = this,
			NOW = new Date(),
			maxPurchaseAt = new Date(NOW.getTime() + 60000);

		if (purchaseAt > maxPurchaseAt) { appNotify('extOrderFuturePurchaseAt', { orderId, purchaseAt, maxPurchaseAt }); }
	}
	ExtOrder.validatesAbsenceOf('cancelledAt', { if: 'isNewRecord' });
	ExtOrder.validatesAbsenceOf('reason', { if: 'isNewRecord' });
};
