{"name": "Fulfillment", "plural": "Fulfillments", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Common": true, "Timestamp": true, "FindOneAndUpdate": true, "FindFulfillment": true, "Provider": true, "FulfillmentFlow": true, "Queue": true, "Availability": true, "Deliver": true, "PrintTicket": true, "FulfillmentApi": true, "MachineEvent": true, "DisableAllRemotes": {"create": true, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "deleteById": true, "confirm": true, "count": true, "exists": true}}, "properties": {"type": {"type": "string", "required": true, "enum": ["digital", "kitchen", "store", "pickup", "deliver", "<PERSON><PERSON>", "vending"]}, "priority": {"type": "string", "default": "normal", "enum": ["normal", "high", "highest"], "description": "Priority of order"}, "recipient": {"type": {"membershipId": {"type": "string", "description": "Membership id of recipient (for digital)"}, "fullName": {"type": "String", "description": "Name of addressee"}, "phone": {"type": "string", "description": "Formatted phone number to contact recipient"}, "email": {"type": "string", "description": "Email used by carriers to contact recipient"}}, "description": "Recipient information for delivery / shipping use"}, "destination": {"type": "Spot", "description": "Delivery address + placeId + position"}, "origin": {"type": "Spot", "description": "Spot to fulfill from (address of fulfillFrom)"}, "scheduled": {"type": {"minTime": {"type": "date"}, "maxTime": {"type": "date"}, "firstEta": {"type": "date", "description": "ETA of first item"}}, "default": {}, "description": "Scheduled time to deliver/pickup order"}, "minTime": {"type": "date", "description": "Estimated earliest fulfillment time"}, "maxTime": {"type": "date", "description": "Estimated latest fulfillment time"}, "discountAmount": {"type": "number", "default": 0, "description": "Fulfillment fee discount"}, "tax": {"type": "number", "description": "Tax applicable to fulfillment"}, "price": {"type": "number", "description": "Shipping price after discount"}, "unitPrice": {"type": "number", "description": "Price of fulfillment"}, "currency": {"type": "String", "max": 3}, "note": {"type": "string", "max": 180, "description": "Customer instructions/comments"}, "status": {"type": "string", "enum": ["null", "pending", "open", "success", "cancelled", "failure", "error", "partial"], "default": "null"}, "flow": {"at": {"type": "number", "default": 0, "description": "Current step (index)"}, "steps": [{"type": "String", "enum": ["ordered", "requested", "allocated", "queued", "prepare", "packed", "collected", "delivered"], "description": "Fulfillment progress"}]}, "provider": {"type": "string", "max": 24, "description": "Name of provider"}, "prepare": {"type": {"startTime": {"type": "date"}, "endTime": {"type": "date"}}, "description": "Scheduled time to prepare items (kitchen)"}, "pickup": {"type": {"minTime": {"type": "date"}, "maxTime": {"type": "date"}}, "description": "Scheduled time to pickup items for Delivery"}, "deliver": {"type": {"cost": {"type": "number"}, "currency": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "pickingup", "delivering", "delivered", "cancelled", "failed", "returned"]}, "driver": {"type": {"id": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "plateNumber": {"type": "string"}, "photo": {"type": "string"}}}, "location": {"type": "Geometry"}, "pickup": {"type": "date", "description": "Scheduled pickup time"}, "duration": {"type": "number", "description": "Time to deliver after pickup"}, "distance": {"type": "number", "description": "Between origin & destination in meters"}, "eta": {"type": "date", "description": "Estimated deliver time"}, "ticket": {"type": "string", "description": "Identifier from provider for driver, eg. orderId"}, "trackingUrl": {"type": "string", "description": "Url of tracking page"}, "trackingNumber": {"type": "string"}, "carrierId": {"type": "string"}, "carrierName": {"type": "string"}, "serviceName": {"type": "string"}, "serviceCode": {"type": "string"}}, "description": "Delivery/shipping details"}, "receipt": {"type": {"queue": {"type": "string", "description": "Queue number to pickup order"}, "authorisation": {"type": "string", "description": "Authorisation code, to present when receiving/collecting"}}}, "mainFulfillmentType": {"type": "string", "enum": ["pickup", "deliver", "<PERSON><PERSON>"], "description": "Type of main fulfillment, type=kitchen only"}, "external": {"type": {"perkd": {"type": {"userId": {"type": "String"}, "cardId": {"type": "String"}, "messageId": {"type": "String"}}}, "shopify": {"type": {"customerId": {"type": "String"}, "orderId": {"type": "String"}}}, "lalamove": {"type": {"orderId": {"type": "String"}, "deliveryId": {"type": "String"}}}, "grab": {"type": {"orderId": {"type": "String"}, "deliveryId": {"type": "String"}}}}, "default": {}}, "when": {"type": {"ordered": {"type": "date", "default": null, "description": "Time fulfillment ordered"}, "requested": {"type": "date", "default": null, "description": "Time fulfillment requested"}, "allocated": {"type": "date", "default": null, "description": "Time delivery/driver assigned"}, "queued": {"type": "date", "default": null, "description": "Time fulfillment queued for preparation"}, "prepare": {"type": "date", "default": null, "description": "Time preparation of items started"}, "packed": {"type": "date", "default": null, "description": "Time items packed and ready"}, "collected": {"type": "date", "default": null, "description": "Time items collected at store"}, "delivered": {"type": "date", "default": null, "description": "Time items delivered to customer"}}, "default": {}}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}}, "validations": [], "relations": {"items": {"type": "embeds<PERSON><PERSON>", "model": "FulfillItem", "property": "itemList", "options": {"validate": true}}, "order": {"type": "belongsTo", "model": "Order", "foreignKey": "orderId"}, "fulfillFrom": {"type": "belongsTo", "model": "Place", "foreignKey": "placeId", "description": "Place/store to fulfill & deduct inventory"}, "staff": {"type": "belongsTo", "model": "Staff", "foreignKey": "staffId"}, "mainFulfill": {"type": "belongsTo", "model": "Fulfillment", "foreignKey": "mainFulfillmentId", "description": "Main fulfillment of this fulfillment"}, "events": {"type": "embeds<PERSON><PERSON>", "model": "Activity", "property": "eventList", "options": {"validate": true}}}, "acls": [], "indexes": {"orderId_index": {"keys": {"orderId": 1}}, "placeId_index": {"keys": {"placeId": 1}}}, "scopes": {}, "methods": {"shippingRates": {"description": "Get shipping rates", "http": {"path": "/shipping/rates", "verb": "post"}, "accepts": [{"arg": "origin", "type": "object", "required": true}, {"arg": "destination", "type": "object", "required": true}, {"arg": "items", "type": "array", "required": true}, {"arg": "currency", "type": "string", "required": true}], "returns": {"type": "Object", "root": true}}, "pickupLocations": {"description": "Search for pickup locations by address or geo", "http": {"path": "/pickup/locations", "verb": "post"}, "accepts": [{"arg": "address", "type": "string", "required": false}, {"arg": "geo", "type": "object", "required": false}, {"arg": "radius", "type": "number", "description": "in meters"}], "returns": {"type": "Object", "root": true}}, "pickupTimes": {"description": "Estimate pickup times for items at a place", "http": {"path": "/pickup/times", "verb": "post"}, "accepts": [{"arg": "placeId", "type": "string", "required": true}, {"arg": "items", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Object", "root": true}}, "findByProviderOrderId": {"description": "Find fulfillment(s) of provider by (external) order id", "http": {"path": "/provider/orderId", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "orderId", "type": "string", "required": true}], "returns": {"type": ["Fulfillment"], "root": true}}, "findOpenForTables": {"description": "Get Open fulfillments for tables", "http": {"path": "/tables/open", "verb": "post"}, "accepts": [{"arg": "tables", "type": [{"types": "object"}], "required": true, "description": "List of tables (Spot)"}, {"arg": "kitchen", "type": "boolean", "default": false, "description": "true => include kitchen fulfillments"}], "returns": {"type": ["Fulfillment"], "root": true}}, "requested": {"description": "Mark fulfillments of provider as Requested for order", "http": {"path": "/requested", "verb": "post"}, "accepts": [{"arg": "orderId", "type": "string", "required": true}, {"arg": "provider", "type": "string"}, {"arg": "at", "type": "date"}], "returns": {"type": ["Fulfillment"], "root": true}}, "prototype.request": {"description": "Send fulfillment request to provider", "http": {"path": "/request", "verb": "post"}, "accepts": [{"arg": "acquired", "type": "object", "description": "Order touchpoint"}], "returns": {"type": "Fulfillment", "root": true}}, "prototype.preparing": {"description": "<PERSON> as Preparing", "http": {"path": "/prepare", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "staffId", "type": "string", "description": "Staff preparing items"}], "returns": {"type": "Object", "root": true}}, "prototype.packed": {"description": "<PERSON> as Packed", "http": {"path": "/packed", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}], "returns": {"type": "Object", "root": true}}, "prototype.delivered": {"description": "<PERSON> as Delivered", "http": {"path": "/delivered", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}], "returns": {"type": "Object", "root": true}}, "prototype.reprintKitchenTicket": {"description": "(Re)Print Kitchen Order Ticket", "http": {"path": "/print/kitchen", "verb": "post"}, "accepts": [], "returns": {"type": "Object", "root": true}}, "prototype.cancel": {"description": "Cancel fulfillment of items", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "items", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}}}