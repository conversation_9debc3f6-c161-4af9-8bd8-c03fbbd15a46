/**
 *  @module Model:machine (OrderProvider)
 */

module.exports = function(machine) {

	// ---  External event handlers

	/**
	 * Handle fulfillment collected event from machine
	 * @param {Fulfillment} fulfillment - instance
	 * @param {Object} data - { items, machineId, orderId, referenceId }
	 */
	machine.handleExtOrderFulfillmentCollected = async function(fulfillment, data = {}) {
		await fulfillment.collected()
	}

	/**
	 * Handle fulfillment failed event from machine
	 * @param {Fulfillment} fulfillment - instance
	 * @param {Object} data - { error, items, machineId, orderId, referenceId }
	 */
	machine.handleExtOrderFulfillmentFailed = async function(fulfillment, data = {}) {
		await fulfillment.failed()
	}

	/**
	 * Handle successful order reservation in machine
	 * @param {Object} data - { items, machineId, orderId, referenceId }
	 */
	machine.handleExtOrderReserveSuccess = async function(data = {}) {
		// TODO: Implement reserve success handling if needed
	}

	/**
	 * <PERSON>le failed order reservation in machine
	 * @param {Object} data - { error, items, machineId, orderId, referenceId }
	 */
	machine.handleExtOrderReserveFailed = async function(data = {}) {
		// TODO: Implement reserve failure handling if needed
	}
}