{"name": "sales", "description": "Sales service for CRM platform", "version": "1.0.0", "private": true, "engines": {"node": ">=22"}, "main": "server/server.js", "scripts": {"lint": "eslint .", "start": "node .", "test": "node --test --import dotenv/config tests/**/*.test.js", "test-one": "node --test --import dotenv/config", "sub": "git submodule foreach 'git pull origin master && git checkout master && git pull'", "docker": "docker container kill crm-sales || true && docker container rm crm-sales || true && docker build --tag crm-sales:dev . && docker run -it --name crm-sales -p 3102:3102 --network dev crm-sales:dev", "update": "ncu -u -x i18n-js", "reinstall": "(rm -rf node_modules/ || true) && (rm yarn.lock || true) && yarn install"}, "repository": {"type": "git", "url": "git+https://github.com/perkd/sales.git"}, "dependencies": {"@crm/loopback": "github:perkd/crm-loopback#semver:^0.8.2", "@crm/types": "github:perkd/crm-types#semver:^1.11.19", "@perkd/commerce": "github:perkd/commerce#semver:^1.7.3", "@perkd/errors": "github:perkd/errors#semver:^0.5.1", "@perkd/event-registry-crm": "github:perkd/event-registry-crm#semver:^1.5.4", "@perkd/eventbus": "github:perkd/eventbus#semver:^4.9.5", "@perkd/forms": "github:perkd/forms#semver:^0.4.0", "@perkd/fulfillments": "github:perkd/fulfillments#semver:^0.5.2", "@perkd/machines": "github:perkd/machines#semver:^0.4.0", "@perkd/mcp-core": "github:perkd/mcp-core#semver:^3.1.1", "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0", "@perkd/metrics-push": "github:perkd/metrics-push#semver:^1.5.0", "@perkd/multitenant-context": "github:perkd/multitenant-context#semver:^0.7.0", "@perkd/orders": "github:perkd/orders#semver:^0.5.7", "@perkd/sdk": "github:perkd/wallet-perkd#semver:^1.6.0", "@perkd/settings": "github:perkd/settings#semver:^1.6.0", "@perkd/sync": "github:perkd/sync#semver:^1.1.5", "@perkd/tenants": "github:perkd/tenants#semver:^4.9.0", "@perkd/touchpoints": "github:perkd/touchpoints#semver:^0.2.4", "@perkd/utils": "github:perkd/utils#semver:^2.0.5", "@perkd/wallet": "github:perkd/wallet#semver:^0.5.0", "@provider/ezreceipt": "github:perkd/invoice-ezreceipt#semver:^0.5.6", "@provider/google": "github:perkd/google#semver:^1.3.0", "@provider/grabfood": "github:perkd/sales-grabfood#semver:^1.1.0", "@provider/grabmart": "github:perkd/sales-grabmart#semver:^1.0.17", "@provider/lalamove": "github:perkd/fulfillment-lalamove#semver:^0.7.5", "@provider/mypay": "github:perkd/payment-mypay#semver:^2.2.0", "@provider/providers": "github:perkd/provider-providers#semver:^1.7.4", "@provider/shopify": "github:perkd/sales-shopify#semver:^1.5.3", "@provider/ubereats": "github:perkd/sales-ubereats#semver:^0.8.1", "async-lock": "^1.4.1", "colors": "^1.4.0", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "deep-diff": "^1.0.2", "dotenv": "^17.0.1", "geolib": "^3.3.4", "i18n-js": "3.9.2", "i18next": "^25.3.0", "loopback": "github:perkd/loopback#semver:^3.34.3", "loopback-boot": "^3.3.1", "loopback-connector-mongodb": "github:perkd/loopback-connector-mongodb#semver:^6.3.1", "loopback-filters": "^1.1.1", "nocache": "^4.0.0", "node-cache": "^5.1.2", "redlock": "5.0.0-beta.2"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@slack/web-api": "^7.9.3", "@stylistic/eslint-plugin-js": "^4.4.1", "debug": "^4.4.1", "eslint": "^9.30.1", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-n": "^17.20.0", "eslint-plugin-security": "^3.0.1", "loopback-component-explorer": "^6.5.1"}, "resolutions": {"loopback-datasource-juggler": "github:perkd/loopback-datasource-juggler#semver:^5.2.1", "strong-remoting": "github:perkd/strong-remoting#semver:^3.20.2", "strong-globalize": "6.0.6"}, "packageManager": "yarn@4.9.2"}