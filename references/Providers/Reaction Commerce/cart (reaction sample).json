{"_id": "H2eSqc9ALx3xEHAzb", "sessionId": "cRaSg7QagPkde7DGG", "userId": "ojkknAnQXNtQh4R7n", "shopId": "J8Bhq3uTtdgwZx3rz", "createdAt": "2018-03-08T09:47:08.892Z", "workflow": {"status": "new"}, "billing": [{"currency": {"userCurrency": "SGD"}, "_id": "FtXJoYgDeJeCYXK6u"}], "updatedAt": "2018-03-09T09:31:03.925Z", "tax": 0, "discount": 0, "items": [{"_id": "yH9crjbHe2M2EZfKa", "shopId": "J8Bhq3uTtdgwZx3rz", "productId": "eccbvimed7b4ShWv3", "quantity": 1, "product": {"_id": "eccbvimed7b4ShWv3", "title": "三米飯團", "shopId": "J8Bhq3uTtdgwZx3rz", "ancestors": [], "description": "這是解說喔～", "hashtags": ["FhyTT3bpQcYgcPhs5", "RmwHNutFqSaDtDRXF", "yaistLrm7qf94fDEr", "YDfov9tNYBrtvavNC"], "price": {"range": "188 - 330", "min": 188, "max": 330}, "isVisible": true, "isLowQuantity": false, "isSoldOut": false, "isBackorder": false, "metafields": [], "pageTitle": "基本會員", "type": "simple", "vendor": "三米飯團", "originCountry": "HK", "requiresShipping": true, "isDeleted": false, "template": "productDetailSimple", "workflow": {"status": "new"}, "handle": "sammi-fan-club-2018", "createdAt": "2018-03-05T16:48:07.011Z", "updatedAt": "2018-03-09T09:31:03.595Z", "positions": {"reaction": {"position": 1, "updatedAt": "2018-03-05T17:02:26.691Z"}, "shop": {"weight": 0, "updatedAt": "2018-03-06T16:07:53.887Z"}}}, "variants": {"_id": "YQEzYRR8Egxyw9C6h", "title": "一年", "ancestors": ["eccbvimed7b4ShWv3"], "price": 188, "inventoryManagement": true, "inventoryPolicy": true, "inventoryQuantity": 99, "isVisible": true, "weight": 0, "metafields": [{"key": null, "value": null}], "shopId": "J8Bhq3uTtdgwZx3rz", "taxable": false, "type": "variant", "isDeleted": false, "compareAtPrice": 0, "length": 0, "width": 0, "height": 0, "lowInventoryWarningThreshold": 0, "taxCode": "0000", "optionTitle": "Untitled Option", "originCountry": "HK", "workflow": {"status": "new"}, "index": 0}, "title": "三米飯團", "type": "simple", "parcel": null}], "shipping": [{"shopId": "J8Bhq3uTtdgwZx3rz", "shipmentQuotes": [], "shipmentQuotesQueryStatus": {"requestStatus": "error", "shippingProvider": "all", "message": "All requests for shipping methods failed."}, "_id": "9uEkq8SgZmaurX7dT"}]}