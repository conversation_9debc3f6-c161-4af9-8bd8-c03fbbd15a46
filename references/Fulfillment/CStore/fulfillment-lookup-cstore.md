### Convenience Store Lookup API V1

## ChangeLog

|修訂日期|修訂內容|
|-|-|
|20201021|1. 新增支援 `storeId` 查詢條件<br>2. `external` 中刪除 `storeCode`, `serviceCode` 重命名為 `storeId`<br>3. response中的 `results` 重命名為 `cstores`|


## Summary

該 API 根據提供的品牌及地點等信息，搜尋附近的便利點取貨點列表

## Limit

地點信息可為四種情況中的一種

- storeId 店铺的 `storeId`
- lat,lng 傳入目標地點的經緯度
- placeId 通過google map api獲取目標地點的google maps placeId，通過placeId參數傳入 (WIP)
- address 傳入目標地點的地址信息 (WIP)

其中三種情況的優先級為 storeId > lat,lng > placeId > address，當同時傳入以上多於一種時，以優先級高的參數为准

## Authorization

此 API 调用需要授权，授权方式为在 http-headers 中带入 key 为 x-api-key 的值，授权信息联系管理员。

## Request

```
https://gw.perkd.me/staging5/fulfillments/pickup/cstores?brands=["okmart"]&lat=25.154883&lng=121.765517&radius=2000&limit=20&language=zh-Hant
```

> Params

- brands 支援的品牌清单，必填，全集为: [ "okmart", "lifemart", "7eleven", "familymart" ]，eg. ["okmart","lifemart","7eleven"]
- storeId 店铺ID，應為返回的store的external中的storeId
- lat 緯度，與lng成对出现 eg. 25.154883
- lng 經度，與lat成对出现 eg. 121.765517
- placeId 谷歌PlaceId，通過 Google Maps AutoComplete 功能獲取，參考 [PlacID](https://developers.google.com/places/web-service/place-id)  [AutoComplete](https://developers.google.com/places/web-service/autocomplete#place_autocomplete_results)
- address 用户输入的地址，用于反查精确坐标
- radius 搜索範圍半徑，可选，单位为 meter，缺省值為 2000，最大值 5000
- limit 結果最大數量，可选, 缺省值為 20，最大值 100
- language 語言類型 可选，eg. zh-Hant

其中 storeId、lat,lng、placeId、address 需要提供其中一種，多於一種，僅使用優先級高的參數。

> Samples

simplest
```
https://gw.perkd.me/staging5/fulfillments/pickup/cstores?brands=["7eleven","familymart"]&lat=25.154883&lng=121.765517
```

with placeId
```
https://gw.perkd.me/staging5/fulfillments/pickup/cstores?brands=["7eleven","familymart"]&placeId=a place id value
```

with address
```
https://gw.perkd.me/staging5/fulfillments/pickup/cstores?brands=["7eleven","familymart"]&address=基隆市中正區和一路125號127號
```

with storeId
```
https://gw.perkd.me/staging5/fulfillments/pickup/cstores?brands=["7eleven","familymart"]&storeId=140597
```


## Response

```json
{
    "results": [], // deprecated
    "cstores": [ // Store list，结果按 distance 由近到远排列
        {
            "id": "5f6c041d15fee46b3ad44265", 
            "type": "cstore",       
            "brand": {
                "key" : "7eleven",
                "short": "7eleven",
                "long": "7eleven",
                "logo": "https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/7-eleven_logo.svg/1920px-7-eleven_logo.svg.png"
            },
            "name": "和平島",
            "geo": {
                "type": "Point",
                "coordinates": [
                    25.154883,
                    121.765517
                ]
            },
            "distance": 0, // 单位为 meter
            "minTime": 2880,
            "maxTime": 5760,
            "external": {  // external info, such as storeCode, used for all service provider
                "serviceCode": "140597", // deprecated
                "storeCode": "140597", // deprecated
                "storeId": "140597"
            },
            "addressList": [
                {
                    "id": "kfgm6zz4",
                    "type": "store",
                    "house": "125號",
                    "level": "",
                    "unit": "",
                    "street": "和一路",
                    "district": "中正區",
                    "city": "基隆市",
                    "state": "",
                    "postCode": "",
                    "country": "TW",
                    "formatted": "基隆市中正區和一路125號127號",
                    "short": "中正區和一路125號127號",
                    "geo": {
                        "type": "Point",
                        "coordinates": [
                            25.154883,
                            121.765517
                        ]
                    },
                }
            ],
            "phoneList": [
                {
                    "id": "kfgm6zz3",
                    "fullNumber": "8860224629596",
                    "type": "tel",
                    "countryCode": "886",
                    "number": "0224629596",
                }
            ],
            "createdAt": "2020-09-24T09:31:34.104Z",
            "modifiedAt": null
        }
    ]
}
```