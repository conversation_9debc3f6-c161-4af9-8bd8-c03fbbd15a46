{"data": {"order": {"id": "gid://shopify/Order/2661515853909", "fulfillmentsV2": {"edges": [], "pageInfo": {"hasNextPage": false, "__typename": "PageInfo"}, "__typename": "FulfillmentConnection"}, "fulfillmentOrders": {"edges": [{"cursor": "eyJsYXN0X2lkIjoxODg2Njg2NTExMTg5LCJsYXN0X3ZhbHVlIjowfQ==", "node": {"id": "gid://shopify/FulfillmentOrder/1886686511189", "alert": null, "deliveryMethod": {"id": "gid://shopify/DeliveryMethod/8849555541", "expectedDeliveryBy": "2020-09-01T14:49:21Z", "methodType": "PICK_UP", "sourceType": null, "additionalInformation": {"failedCarriers": [], "instructions": null, "phone": null, "__typename": "DeliveryMethodAdditionalInformation"}, "deliveryProfile": null, "pickupPoint": null, "__typename": "DeliveryMethod"}, "displayLocationName": "宅配", "displayStatus": {"status": "FULFILLMENT_IN_PROGRESS", "title": "Fulfillment in progress", "totalLineItemRemainingQuantity": 2, "__typename": "FulfillmentOrderDisplayStatus"}, "fulfillAt": null, "fulfillBy": null, "fulfillmentService": {"id": "gid://shopify/FulfillmentService/manual", "type": "MANUAL", "__typename": "FulfillmentService"}, "fulfillmentHolds": [], "requiresShipping": true, "requestStatus": "UNSUBMITTED", "status": "IN_PROGRESS", "totalLineItemRemainingQuantity": 2, "assignedLocation": {"name": "111 Somerset Road #11-20", "location": {"id": "gid://shopify/Location/34516435029", "name": "宅配", "__typename": "Location"}, "__typename": "FulfillmentOrderAssignedLocation"}, "locationsForMove": {"edges": [{"cursor": "eyJsYXN0X2lkIjozNDUxNjQzNTAyOSwibGFzdF92YWx1ZSI6IuWuhemFjSJ9", "node": {"locationName": "宅配", "movable": false, "message": "Current location.", "location": {"id": "gid://shopify/Location/34516435029", "__typename": "Location"}, "__typename": "FulfillmentOrderLocationForMove"}, "__typename": "FulfillmentOrderLocationForMoveEdge"}], "pageInfo": {"hasNextPage": false, "__typename": "PageInfo"}, "__typename": "FulfillmentOrderLocationForMoveConnection"}, "firstThirdPartyRequest": {"edges": [], "pageInfo": {"hasNextPage": false, "__typename": "PageInfo"}, "__typename": "FulfillmentOrderThirdPartyRequestConnection"}, "supportedActions": [{"title": "<PERSON> as picked up", "action": "MARK_AS_PICKED_UP", "priority": "FIRST", "externalUrl": null, "__typename": "FulfillmentOrderSupportedAction"}, {"title": "Print packing slip", "action": "PRINT_PACKING_SLIP", "priority": "SECOND", "externalUrl": null, "__typename": "FulfillmentOrderSupportedAction"}], "lineItems": {"edges": [{"node": {"id": "gid://shopify/FulfillmentOrderLineItem/4196629610581", "remainingQuantity": 1, "unfulfilledWithCodeDiscountedTotalPriceSet": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "lineItem": {"id": "gid://shopify/LineItem/5735129186389", "components": null, "__typename": "LineItem", "product": {"id": "gid://shopify/Product/4326167052373", "__typename": "Product"}, "variant": {"id": "gid://shopify/ProductVariant/31041374158933", "__typename": "ProductVariant"}, "title": "TRI-TECH", "variantTitle": "55 cm", "sku": "GN4*74004-1", "image": {"id": "gid://shopify/ImageSource/13341285318741", "thumbnailSrc": "https://cdn.shopify.com/s/files/1/0255/0661/1285/products/tritech1_160x160.jpg?v=1572297902", "altText": null, "__typename": "Image"}, "currentDiscountedTotalSet": {"shopMoney": {"amount": "0.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "discountAllocations": [{"discountApplication": {"title": "BUY CHEAP GET EXP FREE", "isRedactedForShopifyEmployee": null, "__typename": "DiscountCodeApplication", "allocationMethod": "EACH", "index": 0, "targetSelection": "ENTITLED", "targetType": "LINE_ITEM", "value": {"__typename": "PricingPercentageValue"}}, "approximateAllocatedAmountPerItem": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "__typename": "DiscountAllocation"}], "discountedTotalSet": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "originalUnitPriceSet": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "discountedUnitPriceSet": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "withCodeDiscountedUnitPriceSet": {"shopMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "630.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "customAttributesV2": [], "contract": null, "sellingPlan": null}, "warnings": [], "__typename": "FulfillmentOrderLineItem"}, "__typename": "FulfillmentOrderLineItemEdge"}, {"node": {"id": "gid://shopify/FulfillmentOrderLineItem/4196629643349", "remainingQuantity": 1, "unfulfilledWithCodeDiscountedTotalPriceSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "lineItem": {"id": "gid://shopify/LineItem/5735129219157", "components": null, "__typename": "LineItem", "product": {"id": "gid://shopify/Product/4750386659413", "__typename": "Product"}, "variant": {"id": "gid://shopify/ProductVariant/32660089471061", "__typename": "ProductVariant"}, "title": "Product A", "variantTitle": "", "sku": "", "image": null, "currentDiscountedTotalSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "discountAllocations": [], "discountedTotalSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "originalUnitPriceSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "discountedUnitPriceSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "withCodeDiscountedUnitPriceSet": {"shopMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "presentmentMoney": {"amount": "150.0", "currencyCode": "SGD", "__typename": "MoneyV2"}, "__typename": "MoneyBag"}, "customAttributesV2": [], "contract": null, "sellingPlan": null}, "warnings": [], "__typename": "FulfillmentOrderLineItem"}, "__typename": "FulfillmentOrderLineItemEdge"}], "pageInfo": {"hasNextPage": false, "__typename": "PageInfo"}, "__typename": "FulfillmentOrderLineItemConnection"}, "workflows": [], "__typename": "FulfillmentOrder"}, "__typename": "FulfillmentOrderEdge"}], "pageInfo": {"hasNextPage": false, "__typename": "PageInfo"}, "__typename": "FulfillmentOrderConnection"}, "removedCardPresenter": {"edges": [], "__typename": "OrderRemovedCardPresenterConnection"}, "returns": {"edges": [], "__typename": "ReturnConnection"}, "__typename": "Order"}}, "extensions": {"cost": {"requestedQueryCost": 69259, "actualQueryCost": 67, "throttleStatus": {"maximumAvailable": 10000, "currentlyAvailable": 9248, "restoreRate": 500}}}}