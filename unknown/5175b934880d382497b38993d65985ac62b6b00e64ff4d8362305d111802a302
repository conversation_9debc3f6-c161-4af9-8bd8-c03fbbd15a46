/**
 *  @module Mixin:FulfillmentFlow - Fulfillment model
 */

const { Fulfillments } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ stepOf, preStepOf, nextStepOf, isLastStep } = require('@perkd/utils'),
	{ isFromMachine } = require('@perkd/machines')

const { OPEN, SUCCESS } = Fulfillments.Status,
	{ sales: Event } = REGISTRY

module.exports = function(Fulfillment) {

	/**
	 * Progress to Step
	 * @param	{String} step to proceed to
	 * @param	{Date} [at]
	 * @param	{String} [by] staffId
	 * @param	{Object} [changes]
	 * @param	{Boolean} [canRepeat] - allow repeat of step, when updated to latest, eg. allocated (driver)
	 * @return	{Fulfillment} updated instance
	 */
	Fulfillment.prototype.toStep = async function(step, at = new Date(), by, changes = {}, canRepeat = false) {
		const STEP = step,
			{ id, type, flow = {}, when = {} } = this.toJSON(),
			{ steps = [] } = flow,
			atStep = stepOf(STEP, flow),
			previousStep = steps[preStepOf(STEP, flow)],
			lastStep = isLastStep(flow, STEP),
			whenStep = `when.${STEP}`,
			whenPrev = `when.${previousStep}`,
			filter = {
				id,
				[whenPrev]: { $ne: null }
			},
			updates = {
				$set: {
					...changes,
					'flow.at': atStep,
					[whenStep]: at,
					staffId: by
				}
			}

		if (atStep === -1) return this		// ignore
		if (!when[previousStep]) throw new Error('not_ready')
		if (!canRepeat && when[STEP]) throw new Error(`already_${STEP}`)

		if (!canRepeat) {
			filter[whenStep] = null
		}
		if (lastStep) {
			updates.$set.status = SUCCESS
		}

		const instance = await Fulfillment.findOneAndUpdate(filter, updates),
			event = Event.fulfillment[type]?.[STEP]

		if (instance) {
			appEmit(Event.fulfillment.updated, instance)

			if (event && !isFromMachine(instance)) {	// machine fulfillment events MUST be handled separately
				appEmit(event, instance)
			}
		}
		return instance
	}

	/**
	 * Revert back to Step
	 * @param	{String} step to proceed to
	 * @param	{Object} [changes]
	 * @return	{Fulfillment} updated instance
	 */
	Fulfillment.prototype.revertToStep = async function(step, changes = {}) {
		const STEP = step,
			{ id, flow = {}, when = {} } = this.toJSON(),
			{ steps = [] } = flow,
			previousStep = steps[preStepOf(STEP, flow)],
			atStep = stepOf(previousStep, flow),
			nextStep = steps[nextStepOf(STEP, flow)],
			whenStep = `when.${STEP}`,
			whenNext = `when.${nextStep}`,
			filter = {
				id,
				[whenStep]: { $ne: null },
				[whenNext]: null,
			},
			updates = {
				$set: {
					...changes,
					'flow.at': atStep,
					[whenStep]: null
				}
			}

		if (atStep === -1) return this		// ignore
		if (when[nextStep]) throw new Error('already_next_step')

		if (isLastStep(flow, STEP)) updates.$set.status = OPEN

		return Fulfillment.findOneAndUpdate(filter, updates)
	}
}
