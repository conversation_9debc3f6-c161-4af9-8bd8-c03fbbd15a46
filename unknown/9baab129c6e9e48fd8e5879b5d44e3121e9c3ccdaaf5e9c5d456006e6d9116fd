/**
 *  @module Model:nxiot - OrderProvider
 */

const { Touchpoints } = require('@crm/types')

const { STORE } = Touchpoints.Type,
	{ TERMINAL } = Touchpoints.Format,
	{ PARTNER } = Touchpoints.Attributed

module.exports = function(Nxiot) {

	const { name: NXIOT } = Nxiot

	//  ---  Handle Nxiot Order event

	Nxiot.handleExtOrderCreated = async function (extOrder) {
		const { Order } = this.app.models,
			data = await this.toOrder(extOrder),
			order = await Order.create(data)

		return order.markPaid(null, true)
	}

	Nxiot.toOrder = async function (data = {}) {
		const { Membership } = this.app.models,
			{ orderId, quantity, currency, taxIncluded, totalDiscount, tax, subtotal, total, note } = data,
			{ items, customer = {}, discounts = [], payments = [], receipt = {}, at = new Date(), merchantCode } = data,
			{ id: customerId, cardNumber, taxId } = customer,
			itemList = toItemList(items),
			discountList = toDiscountList(discounts),
			billingList = toBillingList(payments),
			external = {
				[ NXIOT ]: { orderId, customerId }
			},
			through = {
				type: STORE,
				format: TERMINAL,
				attributedTo: {
					type: PARTNER,
					id: merchantCode,
				},
				touchedAt: at
			},
			order = {
				receipt,
				currency,
				quantity: Number(quantity),
				subtotalPrice: Number(subtotal),
				discountAmount: Number(totalDiscount),
				taxIncluded,
				taxAmount: Number(tax),
				amount: Number(total),
				itemList,
				discountList,
				billingList,
				note,
				when: { paid: at },
				external,
				acquired: through,
				through
			}

		if (cardNumber) {
			const { personId, membership } = await Membership.findActiveAndPersonIdByCardNumber(cardNumber) ?? {},
				{ id: membershipId, programId, tierLevel, memberId } = membership ?? {}

			if (personId) {
				order.personId = personId
			}
			if (membership) {
				order.program = { id: programId, tierLevel, cardNumber }
				order.memberId = memberId
				order.membershipId = membershipId
			}
		}

		return order
	}

	// ----  Private functions

	function toItemList(items = []) {
		return items.map(({ sku, title, description, ...item }) => (
			{
				...item,
				variant: { sku, title, description } }
		))
	}

	function toBillingList(payments = []) {
		return payments		// FIXME
	}

	function toDiscountList(discounts = []) {
		return discounts		// FIXME
	}
}
