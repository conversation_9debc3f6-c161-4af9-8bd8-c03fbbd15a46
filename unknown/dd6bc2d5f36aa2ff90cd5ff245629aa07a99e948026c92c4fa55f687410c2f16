/**
 *  @module Model:DashboardAppApi  (used by shopify-sales-dashboard app)
 */

const { Settings, Providers } = require('@crm/types'),
	{ startOf, endOf, openFrom, openUntil, growthRate, nativeAmount, ObjectId } = require('@perkd/utils')

const { LOCALE } = Settings.Name,
	{ SHOPIFY, UBEREATS, GRABFOOD, GRABMART } = Providers.PROVIDER

module.exports = function(Order) {

	/**
	 * Sales Summary for location, channel within (time) range
	 * @param	{String[]} locationIds
	 * @param	{Date} start
	 * @param	{Date} end
	 * @param	{String} [channel] - omit for all
	 * @param	{Boolean} [useOpeningHours] - true => align to hours of each location, otherise use day boundaries\
	 * @returns	{Object} { summary, paymentMethods }
	 */
	Order.shopifySalesSummary = async function (locationIds, start, end, channel, useOpeningHours) {
		const { app } = Order,
			{ timeZone, currency } = app.getSettings(LOCALE),
			storeIds = await storeIdsFrom(locationIds),
			fromTos = await fromToFor(storeIds, start, end, timeZone, useOpeningHours),
			[ summaries, payments ] = await Promise.all([
				summaryFor(fromTos, channel, useOpeningHours),
				paymentMethodsFor(fromTos, channel, useOpeningHours)
			]),
			summary = totalAndGrowth(summaries, currency),
			methods = payments.reduce((acc, { current }) => {
				for (const { method } of current) {
					acc.add(method)
				}
				return acc
			}, new Set()),
			paymentMethods = []

		for (const { current, previous } of payments) {
			for (const method of methods) {
				const { sales, orders, aov } = current.find(m => m.method === method),
					prev = previous.find(m => m.method === method) || { sales: 0, orders: 0, aov: 0 },
					results = [{
						current: { sales, orders, aov },
						previous: { sales: prev.sales, orders: prev.orders, aov: prev.aov }
					}],
					res = totalAndGrowth(results, currency)

				res.method = method
				paymentMethods.push(res)
			}
		}

		return { summary, paymentMethods }
	}

	// -----  Private functions

	/**
	 *
	 * @param {String[]} storeIds
	 * @param {Date} start
	 * @param {Date} end
	 * @param {Boolean} useOpeningHours
	 * @returns {Promise<Object[]>} - list of { storeId, current, previous }
	 */
	async function fromToFor(storeIds, start, end, timeZone, useOpeningHours) {
		const { Place } = Order.app.models,
			finds = [],
			fromTos = []

		// use day boundaries
		if (!useOpeningHours) {
			const from = startOf('day', start, timeZone),
				to = endOf('day', end, timeZone),
				current = { from, to }

			for (const storeId of storeIds) {
				const previous = getPrevious(current, timeZone)

				fromTos.push({ storeId, current, previous })
			}

			return fromTos
		}

		// use opening hours
		for (const id of storeIds) {
			finds.push(
				Place.findById(id).then(p => p ? ({ storeId: p.id, openingHours: p.openingHours }) : undefined),
			)
		}

		const openings = await Promise.all(finds)

		for (const { storeId, openingHours } of openings) {
			const current = getCurrent(openingHours, start, end, timeZone),
				previous = getPrevious(current, timeZone)

			fromTos.push({ storeId, current, previous })
		}

		return fromTos
	}

	function totalAndGrowth(results, currency) {
		const previousTotal = { sales: 0, orders: 0 },
			res = {
				sales: { value: 0 },
				orders: { value: 0 },
				aov: { value: 0 },
			}

		for (const { current, previous } of results) {
			const { sales, orders } = current

			res.sales.value += sales
			res.orders.value += orders

			previousTotal.sales += previous.sales
			previousTotal.orders += previous.orders
		}

		res.sales.growth = growthRate(res.sales.value, previousTotal.sales)
		res.orders.growth = growthRate(res.orders.value, previousTotal.orders)

		res.aov.value = nativeAmount(res.sales.value / res.orders.value, currency)
		res.aov.growth = growthRate(res.aov.value, previousTotal.sales / previousTotal.orders)

		return res
	}

	/**
	 * Get summary for stores, both current & previous periods
	 * @param {Object[]} fromTos - list of { storeId, current, previous }
	 * @param {String} channel
	 * @param {Boolean} useOpeningHours
	 * @returns {Promise<Object[]>} list of { current: { sales, orders, aov }, previous: { sales, orders, aov } }
	 */
	async function summaryFor(fromTos, channel, useOpeningHours) {
		const INIT = { sales: 0, orders: 0, aov: 0 },
			summaries = []

		if (!fromTos.length) return []

		// use day boundaries
		if (!useOpeningHours) {
			const [ first ] = fromTos,
				storeIds = fromTos.map(f => ObjectId(f.storeId)),
				[ current = INIT, previous = INIT ] = await Promise.all([
					getSummary(storeIds, channel, first.current.from, first.current.to),
					getSummary(storeIds, channel, first.previous.from, first.previous.to),
				])

			return [ { current, previous } ]
		}

		// use opening hours
		for (const { storeId, current, previous } of fromTos) {
			const { from, to } = current,
				storeIds = [ ObjectId(storeId) ]

			summaries.push(
				Promise.all([
					getSummary(storeIds, channel, from, to),
					getSummary(storeIds, channel, previous.from, previous.to),
				])
					.then(([ current = INIT, previous = INIT ]) => ({ current, previous }))
			)
		}

		return Promise.all(summaries)
	}

	/**
	 * Get Payment Methods breakdown for stores, both current & previous periods
	 * @param {Object[]} fromTos - list of { storeId, current, previous }
	 * @param {String} channel
	 * @param {Boolean} useOpeningHours
	 * @returns {Promise<Object[]>} list of { current: { sales, orders }, previous: { sales, orders } }
	 */
	async function paymentMethodsFor(fromTos, channel, useOpeningHours) {
		const methods = []

		if (!fromTos.length) return []

		// use day boundaries
		if (!useOpeningHours) {
			const [ first ] = fromTos,
				storeIds = fromTos.map(f => ObjectId(f.storeId)),
				[ current, previous ] = await Promise.all([
					getPaymentMethods(storeIds, channel, first.current.from, first.current.to),
					getPaymentMethods(storeIds, channel, first.previous.from, first.previous.to),
				])

			return [ { current, previous } ]
		}

		// use opening hours
		for (const { storeId, current, previous } of fromTos) {
			const { from, to } = current,
				storeIds = [ ObjectId(storeId) ]

			methods.push(
				Promise.all([
					getPaymentMethods(storeIds, channel, from, to),
					getPaymentMethods(storeIds, channel, previous.from, previous.to),
				])
					.then(([ current, previous ]) => ({ current, previous }))
			)
		}

		return Promise.all(methods)
	}

	function getCurrent(openingHours, start, end, timeZone) {
		let now = new Date(start)

		return {
			from: openFrom(openingHours, now, timeZone),
			to: openUntil(openingHours, now, timeZone)
		}
	}

	function getPrevious(current, timeZone) {
		const { from } = current,
			yesterday = new Date(from),
			x = yesterday.setDate(yesterday.getDate() - 1)

		return {
			from: startOf('day', yesterday, timeZone),
			to: endOf('day', yesterday, timeZone)
		}
	}

	async function storeIdsFrom(locationIds = []) {
		const { Place } = Order.app.models,
			filter = {
				where: {
					[ `external.${SHOPIFY}.storeId` ]: { inq: locationIds }
				}
			},
			places = await Place.find(filter),
			storeIds = places.map(p => ObjectId(p.id))

		return storeIds
	}

	/**
	 * @param	{ObjectId[]} storeIds
	 * @param	{String} channel
	 * @param	{Date} from
	 * @param	{Date} to
	 * @returns	{Promise<Object|void>} - { sales, orders, aov }
	 */
	async function getSummary(storeIds = [], channel, from, to) {
		const filter = [
			{
				$match: {
					'when.cancelled': null,
					createdAt: { $gte: from, $lte: to },
					storeId: { $in: storeIds },
					$or: [
						{
							'when.paid': { $ne: null }
						},
						{
							'acquired.type': { $in: [ UBEREATS, GRABMART, GRABFOOD ] }
						}
					]
				}
			},
			{
				$group: {
					_id: null,
					sales: { $sum: '$amount' },
					orders: { $sum: 1 }
				}
			},
			{
				$addFields: {
					aov: { $divide: [ '$sales', '$orders' ] }
				}
			}
		]

		if (channel) {
			filter[0].$match['acquired.type'] = channel
		}

		const [ res ] = await Order.collection().aggregate(filter).toArray(),
			{ sales, orders, aov } = res ?? {}

		return res ? { sales, orders, aov } : undefined
	}

	/**
	 * @param	{ObjectId[]} storeIds
	 * @param	{String} channel
	 * @param	{Date} from
	 * @param	{Date} to
	 * @returns	{Promise<Object[]>} - [{ method, sales, orders }]
	 */
	async function getPaymentMethods(storeIds = [], channel, from, to) {
		const filter = [
			{
				$match: {
					'when.cancelled': null,
					createdAt: { $gte: from, $lte: to },
					storeId: { $in: storeIds },
					$or: [
						{
							'when.paid': { $ne: null }
						},
						{
							'acquired.type': { $in: [ UBEREATS, GRABMART, GRABFOOD ] }
						}
					]
				}
			},
			{
				$unwind: '$billingList'
			},
			{
				$group: {
					_id: '$billingList.paymentMethod.method',
					sales: { $sum: '$billingList.paymentMethod.amount' },
					orders: { $sum: 1 }
				}
			}
		]

		if (channel) {
			filter[0].$match['acquired.type'] = channel
		}

		const res = await Order.collection().aggregate(filter).toArray()

		return res.map(({ _id: method, sales, orders }) => ({ method, sales, orders }))
	}

	// -----  Remote Methods  -----

	Order.remoteMethod('shopifySalesSummary', {
		description: 'Daily Sales Summary (Shopify Dashboard app)',
		http: { path: '/shopify/dashboard/summary', verb: 'get' },
		accepts: [
			{ arg: 'locationIds', type: [ 'string' ], http: { source: 'query' }, required: true, description: 'List of location ids' },
			{ arg: 'start', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'end', type: 'date', http: { source: 'query' }, required: true },
			{ arg: 'channel', type: 'string', http: { source: 'query' }, description: 'omit for all' },
			{ arg: 'openingHours', type: 'boolean', http: { source: 'query' }, description: 'use opening hours of locations' }
		],
		returns: { type: 'object', root: true },
	})
}
