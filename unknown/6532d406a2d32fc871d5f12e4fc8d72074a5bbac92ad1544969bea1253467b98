/**
 *  @module Model:kitchen (FulfillProvider)
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Fulfillments, Settings } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ stepOf, delay } = require('@perkd/utils'),
	{ spotName, Metrics } = require('@perkd/fulfillments')

const { KITCHEN, STORE, DINEIN, PICKUP, DELIVER } = Fulfillments.Type,
	{ OPEN, ERROR, FAILURE } = Fulfillments.Status,
	{ REQUESTED, QUEUED, PREPARE } = Fulfillments.Step,
	{ STEPS } = Fulfillments,
	{ FULFILLMENT } = Settings.Name,
	{ DEFAULT } = Settings,
	{ maxScheduleWindow: MAX_WINDOW,			// Maximum time window (1 hour) to schedule a fulfillment after its requested time
		prepareLookahead: PREPARE_LOOKAHEAD,	// How far ahead to look for orders that need preparation (allows kitchen to prepare in advance)
		maxShelfLife: MAX_SHELFLIFE,			// Maximum time items can remain on the shelf after preparation
		minFirstEtaTime: MIN_FIRST_ETA } = DEFAULT.KITCHEN,		// Minimum duration used to calculate the ETA for the first item in an order
	{ State, Schedule } = Metrics,
	{ sales: Event } = REGISTRY,
	REQUEST_Q = 'kitchen:request:',
	TIMERS = {}			// { [tenantCode]: timeoutID }

module.exports = function(Kitchen) {

	/**
	 * Schedule production of Food items from placeId (fulfillFrom)  (must be serialized per store)
	 * @param	{Fulfillment} fulfillment requested
	 * @param	{Object} [acquired] - touchpoint
	 */
	Kitchen.request = async function (fulfillment, acquired) {
		const { Metric, models } = Kitchen.app,
			{ Fulfillment } = models,
			fulfill = fulfillment.toJSON(),
			{ id, itemList = [], destination, mainFulfillmentType: type, when, mainFulfillmentId, orderId, placeId } = fulfill

		await this.queue(`${REQUEST_Q}${placeId}`, async () => {
			const allocated = await Kitchen.schedule(fulfill)

			if (!allocated) {
				const error = 'unable to schedule',
					tags = { id, placeId, orderId }

				fulfillment.updateAttributes({ status: FAILURE })
				appMetric(Metric.fulfillment.error.schedule, 1, { child: KITCHEN, tags, error })
				appNotify('[Kitchen]request', { error, fulfillment }, 'error', '-kitchen')
				throw new Error(error)
			}

			const { startTime, firstEta, preparationTime, shelfLife } = allocated,
				endTime = new Date(startTime.getTime() + preparationTime),
				prepare = { startTime, endTime },
				minTime = endTime,
				maxTime = new Date(minTime.getTime() + shelfLife),
				targetTime = fulfillment.scheduled.minTime || new Date(Date.now() + preparationTime),
				START = (minTime > targetTime) ? Schedule.LATE : Schedule.ONTIME,
				{ minTime: min = minTime, maxTime: max = maxTime } = fulfillment.scheduled,		// default schedule when omitted (in store)
				scheduled = { minTime: min, maxTime: max, firstEta },
				units = itemList.reduce((tot, i) => tot + i.quantity, 0),
				tags = {
					startTime,
					duration: preparationTime,
					units,
					placeId,
					orderId,
				},
				opt = { child: KITCHEN, tags }

			// metrics
			appMetric(Metric.fulfillment.schedule, 1, opt)

			appMetric(Metric.fulfillment.wait.schedule, START, opt)
			tags.startTime = endTime
			delay(500).then(() => appMetric(Metric.fulfillment.wait.schedule, Schedule.END, opt))

			tags.state = State.PREPARE
			tags.startTime = startTime
			tags.endTime = endTime
			tags.type = type
			tags.table = spotName(destination)
			delete tags.duration
			appMetric(Metric.schedule.fulfillment, preparationTime, opt)

			// update kitchen & main fulfillment
			const [ mainFulfill ] = await Promise.all([
					Fulfillment.findById(mainFulfillmentId),
					fulfillment.updateAttributes({ scheduled, prepare, minTime, maxTime })
				]),
				{ minTime: mainMinTime, maxTime: mainMaxTime } = mainFulfill,
				changes = {
					minTime: mainMinTime ? Math.max(minTime, mainMinTime) : minTime,
					maxTime: mainMaxTime ? Math.max(maxTime, mainMaxTime) : maxTime
				}

			appNotify('[Kitchen]request', { when: fulfillment.when, fulfillment }, null, '-kitchen')

			if (!mainFulfill.scheduled.minTime) {
				changes.scheduled = scheduled		// inject when not scheduled, in store
			}

			await mainFulfill.updateAttributes(changes)
		})
	}

	/**
	 * Get Occupied (busy) time slots for period (from-to)
	 * @param	{String} placeId
	 * @param	{Date} from
	 * @param	{Date} to
	 * @return	{Object[]} occupied slots
	 *			{Date} startTime - start of slot
	 *			{Date} endTime - end of slot
	 */
	Kitchen.occupied = async function(placeId, from, to) {
		const { Fulfillment } = Kitchen.app.models,
			filter = {
				where: {
					placeId,
					type: KITCHEN,
					status: OPEN,
					and: [
						{ 'prepare.endTime': { gt: from } },
						{ 'prepare.startTime': { lt: to } }
					]
				},
				order: 'prepare.startTime ASC'
			},
			list = await Fulfillment.find(filter),
			occupied = list.map(({ prepare }) => prepare.toJSON())

		return occupied
	}

	/**
	 * Get allocation of time slot for preparation
	 * @param	{Object} fulfillment
	 * @return	{Object|void}
	 *			{Date} startTime - start of slot
	 *			{Date} endTime - end of slot
	 *			{Date} firstEta - ETA of first item
	 *			{Number} duration - in milliseconds
	 *			{Number} preparationTime - in milliseconds
	 *			{Number} shelfLife - in milliseconds
	 */
	Kitchen.schedule = async function(fulfillment) {
		const { app } = Kitchen,
			{ Fulfillment, Place } = app.models,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ maxScheduleWindow = MAX_WINDOW, maxShelfLife = MAX_SHELFLIFE, minFirstEtaTime = MIN_FIRST_ETA } = kitchen,
			{ scheduled = {}, itemList = [], placeId } = fulfillment,
			{ minTime } = scheduled,
			now = Date.now(),
			store = await Place.findById(placeId),
			{ preparationTime, first } = await Fulfillment.preparationTimeFor(itemList, store),	// ms
			shelfLife = computeShelfLife(itemList, maxShelfLife),		// ms
			at = minTime ? Math.max(minTime, now) : now,				// ms
			earliest = Math.max(at - preparationTime - shelfLife, now),
			from = new Date(earliest),
			to = new Date(at + maxScheduleWindow),
			occupied = await Kitchen.occupied(placeId, from, to),	// requested fulfillment excluded because prepare is undefined
			available = Kitchen.availableSlots(occupied, from, to),
			allocated = available.find(slot => slot.duration >= preparationTime),	// TODO squeeze in?
			{ startTime, endTime, duration } = allocated || {},
			firstEta = new Date(startTime.getTime() + Math.max(first, minFirstEtaTime))

		return allocated ? { startTime, endTime, firstEta, duration, preparationTime, shelfLife } : undefined
	}

	/**
	 * Initiate preparation for items at prepare.startTime
	 * @return {Number} number of fulfillments prepared
	 */
	Kitchen.startPreparation = async function() {
		const { app } = Kitchen,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ prepareLookahead = PREPARE_LOOKAHEAD } = kitchen,
			{ Fulfillment } = app.models,
			START = new Date(Date.now() + prepareLookahead),
			whenRequested = `when.${REQUESTED}`,
			whenQueued = `when.${QUEUED}`,
			filter = {
				where: {
					type: KITCHEN,
					status: OPEN,
					[whenQueued]: null,
					[whenRequested]: { neq: null },
					'prepare.startTime': { lte: START },
					placeId: { exists: true },
				},
				order: 'prepare.startTime ASC'
			},
			toPrepare = await Fulfillment.find(filter),
			{ length: count } = toPrepare,
			now = new Date(),
			preparations = []

		appNotify('[Kitchen]startPreparation', { count, START, prepareLookahead }, 'start', '-kitchen')

		// Log initial data
		appNotify('[Kitchen]initial orders', toPrepare.map(f => ({
			type: f.mainFulfillmentType,
			startTime: f.prepare.startTime,
			id: f.id
		})), 'debug', '-kitchen')

		// Sort fulfillments to prioritize DINEIN
		const sortedFulfillments = toPrepare.sort((a, b) => {
			if (a.mainFulfillmentType === DINEIN && b.mainFulfillmentType !== DINEIN) return -1
			if (a.mainFulfillmentType !== DINEIN && b.mainFulfillmentType === DINEIN) return 1
			return a.prepare.startTime - b.prepare.startTime
		})

		// Prepare sequentially to maintain priority order
		for (const fulfillment of sortedFulfillments) {
			await Kitchen.toPrepare(fulfillment)
		}

		return count
	}

	/**
	 * Queue items for production (sequentially)
	 * @param	{Fulfillment} fulfillment - mutated
	 * @return	{Promise<Object[]>} preparing items - [{ id, product = {}, variant = {}, quantity }]
	 */
	Kitchen.toPrepare = async function(fulfillment) {
		const { app } = Kitchen,
			{ Metric, models } = app,
			{ Place, Fulfillment, Order } = models,
			_start = Date.now(),
			at = new Date(),
			{ id, itemList, when, flow = {}, receipt = {}, destination, placeId, mainFulfillmentType: type, mainFulfillmentId, orderId } = fulfillment.toJSON(),
			atStep = stepOf(QUEUED, flow),
			tags = { id, placeId, orderId },
			stateTags = {
				...tags,
				type,
				table: spotName(destination),
			},
			opt = { child: KITCHEN, tags },
			store = await Place.findById(placeId)

		if (!store) {
			opt.tags.error = 'place not found'
			opt.tags.at = at

			appNotify('[Kitchen]toPrepare - store not found, skipped', { fulfillmendId: id, placeId }, 'error', '-kitchen')
			appMetric(Metric.fulfillment.error.queue, 1, opt)

			fulfillment.updateAttributes({ status: ERROR })
			return []
		}
		if (when.queued) {		// just in case
			return []
		}

		try {
			const NOW = new Date(),
				{ kitchen = {} } = app.getSettings(FULFILLMENT, store),
				{ autoPrepare, autoPack } = kitchen,
				[ serial, mainFulfill ] = await Promise.all([
					store.queueNumber(),
					Fulfillment.findById(mainFulfillmentId),
				]),
				preparing = []

			flow.at = atStep
			receipt.queue = serial
			when.queued = NOW
			await fulfillment.updateAttributes({ receipt, flow, when })

			appEmit(Event.fulfillment.kitchen.queued, fulfillment)

			fulfillment.printKitchenTicket(store, mainFulfill, serial)

			for (const item of itemList) {
				const { id, quantity = 1, product = {}, variant = {} } = item,
					{ id: variantId } = variant,
					{ id: productId } = product

				preparing.push({ id, productId, variantId, quantity })
			}

			{	// update main fulfillment
				const { receipt = {} } = mainFulfill

				receipt.queue = serial
				mainFulfill.updateAttributes({ receipt })
			}

			appMetric(Metric.fulfillment.queue, 1, opt)
			appMetric(Metric.fulfillment.latency.queue, Date.now() - _start, opt)

			appMetric(Metric.fulfillment.state, State.QUEUED, { child: KITCHEN, tags: stateTags })
			appMetric(Metric.fulfillment.time.queue, NOW - when.ordered, opt)

			appNotify('[Kitchen]prepare', { fulfillmentId: String(fulfillment.id), preparing, at: NOW }, 'scheduled', '-kitchen')

			if (autoPrepare) {
				const prepared = await fulfillment.preparing()
				if (autoPack) prepared.schedulePack(NOW)
			}
			if (type === DINEIN) {
				Order.findById(orderId)
					.then(order => order.printReceipt(store))
			}

			return preparing
		}
		catch (error) {
			appNotify('[Kitchen]toPrepare', { error }, 'error', '-kitchen')
			appMetric(Metric.fulfillment.error.queue, 1, { ...opt, error })
		}
	}

	/**
	 * Timer handler - start preparation of items due and setup next trigger
	 */
	Kitchen.preparationTimer = async function() {
		appNotify('[Kitchen]preparationTimer - called', undefined, 'debug', '-kitchen')

		try {
			await Kitchen.startPreparation()

			const { app } = Kitchen,
				{ Fulfillment } = app.models,
				whenQueued = `when.${QUEUED}`,
				filter = {
					where: {
						type: KITCHEN,
						status: OPEN,
						[whenQueued]: null,
						placeId: { exists: true },
						'prepare.startTime': { exists: true },
					},
					order: 'prepare.startTime ASC'
				},
				next = await Fulfillment.findOne(filter),
				{ tenant } = Context,
				timerId = TIMERS[tenant]

			if (!next) {
				if (timerId) clearTimeout(timerId)
				delete TIMERS[tenant]
				return
			}

			const { kitchen = {} } = app.getSettings(FULFILLMENT),
				{ prepareLookahead = PREPARE_LOOKAHEAD, minTimer = 3000 } = kitchen,
				{ prepare } = next,
				{ startTime } = prepare,
				// Allow lookahead for all order types
				timeout = Math.max(startTime.getTime() - (prepareLookahead / 2) - Date.now(), minTimer)

			appNotify('[Kitchen]preparationTimer - next', { startTime, timeout }, 'debug', '-kitchen')

			if (timerId) clearTimeout(timerId)
			TIMERS[tenant] = setTimeout(() => Kitchen.preparationTimer(), timeout)
		}
		catch (err) {
			appNotify('[Kitchen]preparationTimer', { err }, 'error', '-kitchen')
		}
	}

	/**
	 * Cancel fulfilled Kitchen		// TODO
	 * @param {Object[]} items
	 * @param {Object} fulfillment
	 * 			{Object[]} items (fulfilled)
	 * @return	{Promise<Offer[]>} cancelled
	 */
	Kitchen.cancel = async function(items = [], fulfillment = {}) {
		const { itemList: fulfilled = [] } = fulfillment,
			cancelled = []

		for (const item of items) {
			const { variant = {} } = item

			appNotify('[Kitchen]cancel ', { item }, 'debug', '-kitchen')
		}

		return cancelled
	}

	/**
	 * Calculate open time slots within given window (from-to)
	 * 	- occupied MUST be sorted by startTime ascending
	 * @param	{Object[]} occupied - list of occupied (existing) fulfillments
	 *			{Date} startTime - start of slot
	 *			{Date} endTime - end of slot
	 * @param	{Date} from - start of window
	 * @param	{Date} to - end of window
	 * @return	{Object[]} - list of available time slots
	 *			{Date} startTime - start of slot
	 *			{Date} endTime - end of slot
	 *			{Number} duration - milliseconds
	 */
	Kitchen.availableSlots = function(occupied = [], from, to) {
		if (!occupied.length) {
			return [ {
				startTime: from,
				endTime: to,
				duration: to - from
			} ]
		}

		const slots = []

		let start = from,
			i = 0

		while (start < to) {
			const { startTime: thisStart, endTime: thisEnd } = occupied[i]

			if (thisStart <= start && thisEnd >= to) break		// more than cover entire window

			let startTime = start
			let endTime = to

			if (thisStart > start) {	// slot at top
				endTime = thisStart
				start = endTime
			}
			else if (thisEnd < to) {	// slot in middle
				startTime = thisEnd

				if (++i < occupied.length) {	// has next occupied
					const { startTime: nextStart, endTime: nextEnd } = occupied[i]
					endTime = nextStart < startTime ? nextEnd : nextStart
				}
				start = endTime
			}
			else {
				start = to
				continue
			}

			if (startTime && endTime) {
				startTime = new Date(startTime.getTime() + 1000)	// offset 1 second

				const duration = endTime - startTime

				if (duration > 0) {
					slots.push({ startTime, endTime, duration })
				}
			}
		}

		return slots
	}

	// ----  Optimizations

	/**
	 * Prioritize fulfill sequence of items
	 * @param {Object[]} items
	 * @return {Object[]} sorted items
	 */
	Kitchen.prioritizeItems = function(items = []) {
		// Group items by bundleId
		const groupedItems = items.reduce((acc, item) => {
			const bundleId = item.bundleId || item.variant.id
			if (!acc[bundleId]) {
				acc[bundleId] = []
			}
			acc[bundleId].push(item)
			return acc
		}, {})

		// Extract bundles and sort based on bundled length
		const sortedBundles = Object.keys(groupedItems)
			.map(bundleId => {
				const items = groupedItems[bundleId],
					bundle = items.find(item => item.bundled)

				return { bundle, items }
			})
			.sort((a, b) => {
				const lengthA = a.bundle ? a.bundle.bundled.length : 0,
					lengthB = b.bundle ? b.bundle.bundled.length : 0

				return lengthB - lengthA
			})

		// Flatten the sorted bundles back into a single array
		return sortedBundles.reduce((acc, { items }) => acc.concat(items), [])
	}

	/**
	 * Optimize scheduling, bring forward STORE or DINEIN where possible
	 *	1. find next and establish available time window (from-to)
	 *	2. shift next (if non-queued) forward, OR find ONE (non-queued) fulfillment that fits within window
	 *	3. if found, re-schedule selected
	 * @param	{String} placeId
	 * @param	{Date} from
	 */
	Kitchen.optimize = async function(placeId, from) {
		const { Fulfillment } = Kitchen.app.models,
			nextOpen = {
				where: {
					placeId,
					type: KITCHEN,
					status: OPEN,
					'prepare.startTime': { gt: from }
				},
				order: 'prepare.startTime ASC'
			},
			next = await Fulfillment.findOne(nextOpen)

		if (!next) return

		const { prepare } = next,
			{ startTime: to } = prepare,
			available = to.getTime() - from.getTime(),		// available time window in ms
			whenQueued = `when.${QUEUED}`,
			filter = {
				where: {
					placeId,
					type: KITCHEN,
					status: OPEN,
					[whenQueued]: null,
					'prepare.startTime': { gt: from }
				},
				include: { relation: 'mainFulfill' },
				order: 'prepare.startTime ASC',
				limit: 50
			},
			nonQueued = await Fulfillment.find(filter),
			candidates = nonQueued.filter(({ mainFulfill }) => [ STORE, DINEIN ].includes(mainFulfill().type)),
			[ first ] = candidates,
			selected = (first && String(first.id) === String(next.id))
				? first
				: candidates.find(({ prepare: p }) => (p.endTime.getTime() - p.startTime.getTime()) <= available)

		if (selected) {
			const { prepare, scheduled, mainFulfillmentId } = selected,
				{ startTime: start, endTime: end } = prepare,
				{ minTime: min, maxTime: max, firstEta } = scheduled,
				offset = start.getTime() - from.getTime(),
				minTime = new Date(min.getTime() - offset),
				maxTime = new Date(max.getTime() - offset),
				mainFulfillment = await Fulfillment.findById(mainFulfillmentId)

			prepare.startTime = from
			prepare.endTime = new Date(end.getTime() - offset)
			scheduled.minTime = minTime
			scheduled.maxTime = maxTime
			scheduled.firstEta = new Date(firstEta.getTime() - offset)

			await Promise.all([
				selected.updateAttributes({ scheduled, prepare, minTime, maxTime }),
				mainFulfillment.updateAttributes({ scheduled, minTime, maxTime })
			])

			Kitchen.toPrepare(selected)

			appNotify('[Kitchen]optimize', { from, to, duration: available, selected }, 'good', '-kitchen')
		}
	}

	/**
	 * Inject item preparation time using variantId of items for lookup
	 * 		renames items -> itemList to align with Fulfillment instance
	 * @param	{Object} fulfillment
	 */
	Kitchen.injectPrepTime = async function (fulfillment) {
		const { Variant } = Kitchen.app.models,
			{ items: itemList = [], ...rest } = fulfillment,
			ids = itemList.map(i => i.variantId),
			filter = {
				where: {
					id: { inq: ids },
				}
			},
			variants = await Variant.find(filter)

		for (const instance of variants) {
			const { id, preparation } = instance.toJSON(),
				item = itemList.find(i => i.variantId === id),
				{ variant } = item ?? {}

			if (variant) {
				variant.preparation = preparation
			}
		}

		return { ...rest, itemList }
	}

	// ---  Use for auto-pack (during service boot)

	/**
	 * Find fulfillments that past scheduled preparation endtime, to be set to packed immediately
	 */
	Kitchen.findToPack = async function (now) {
		const { Fulfillment } = Kitchen.app.models,
			steps = STEPS[KITCHEN],
			at = stepOf(PREPARE, { steps }),
			toPack = {
				where: {
					type: KITCHEN,
					status: OPEN,
					'flow.at': at,
					'prepare.endTime': { lte: now }
				}
			}

		return Fulfillment.find(toPack)
	}

	/**
	 * Find fulfillments to be scheduled for auto-packing later
	 */
	Kitchen.findToSchedulePack = async function (now) {
		const { Fulfillment } = Kitchen.app.models,
			steps = STEPS[KITCHEN],
			at = stepOf(PREPARE, { steps }),
			filter = {
				where: {
					type: KITCHEN,
					status: OPEN,
					'flow.at': at,
					'prepare.endTime': { gt: now }
				}
			}

		return Fulfillment.find(filter)
	}

	// ----  Private Functions ----

	/**
	 * Calculate duration (ms) items can be shelfed BEFORE picked up
	 * @param {Object[]} items
	 * @param {Number} preset - from settings (ms)
	 * @return	{Number} duration in ms
	 */
	function computeShelfLife(items = [], preset) {
		// FIXME
		return preset
	}
}
