/**
 *  @module Mixin:Deliver - Fulfillment model
 */

const { Providers } = require('@crm/types'),
	{ buildSender, buildRecipient } = require('@perkd/fulfillments'),
	{ geo2Coordinates } = require('@provider/google')

const { GOOGLE } = Providers.PROVIDER,
	{ FULFILLMENT } = Providers.Service,
	MIN_DURATION = 30 * 60		// in seconds (30 minutes), default delivery duration

module.exports = function(Fulfillment) {

	/**
	 * Schedule Delivery for items of fulfillment
	 * @param	{Fulfillment} fulfillment requested
	 * @param	{Object} [acquired] - touchpoint
	 */
	Fulfillment.requestDelivery = async function(fulfillment = {}, acquired) {
		const { id: fulfillmentId, destination = {}, recipient = {}, itemList, scheduled = {}, orderId, fulfillFrom, note, provider: name } = fulfillment,
			[ origin, provider, google ] = await Promise.all([
				fulfillFrom.get(),
				this.getProviderByService(FULFILLMENT),
				this.getProvider(GOOGLE),
			])

		if (!provider || !origin) {
			console.error('[requestDelivery] provider & fulfillFrom mandatory', { fulfillment })
			return
		}
		if (name && provider.name !== name) {
			const msg = `[requestDelivery] provider mismatch (${name} & ${provider.name})`
			appNotify(msg, 'error')
			console.error(msg, { fulfillment })
			return
		}

		try {
			const { minTime } = scheduled,
				immediate = new Date(Date.now() + 20000),
				arrival = new Date(!minTime || minTime < immediate ? immediate : minTime),
				from = geo2Coordinates(origin.geo),
				to = geo2Coordinates(destination.geo),
				{ duration = MIN_DURATION, distance } = await google.maps.distance(from, to, { arrival }),
				pickup = new Date(Math.max(arrival.getTime() - (duration * 1000), immediate)),
				metadata = {
					orderId: String(orderId),
					fulfillmentId: String(fulfillmentId)
				},
				quotation = await provider.quotations.create(origin, [ destination ], pickup, metadata),
				sender = buildSender(origin),
				recipients = [ buildRecipient(recipient, itemList, note) ],
				order = await provider.orders.create(quotation, sender, recipients),
				{ id, amount: cost, currency, status, trackingUrl } = order,
				deliver = { pickup, duration, distance, cost, currency, ticket: id, trackingUrl, status },
				{ name: providerName } = provider,
				externalKey = `external.${providerName}.orderId`,
				updates = {
					provider: providerName,
					deliver,
					[externalKey]: id
				}

			await fulfillment.requested(new Date(), updates)
		}
		catch (err) {
			appNotify('[requestDelivery]', { err, fulfillment })
			console.error('[requestDelivery]', { err, fulfillment })
		}
	}
}
