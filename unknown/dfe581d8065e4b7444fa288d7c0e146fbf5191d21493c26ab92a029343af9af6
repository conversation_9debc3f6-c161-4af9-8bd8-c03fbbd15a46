/**
 *  @module Model:Grab	 shared by grabfood & grabmart (OrderProvider)
 */
const { Fulfillments, Settings, Providers } = require('@crm/types'),
	{ shortId, preStepOf } = require('@perkd/utils'),
	{ recipientFrom } = require('@perkd/fulfillments'),
	{ OrderAcceptType } = require('@provider/grabfood')

const {
	NODE_ENV
} = process.env

const { MANUAL_ACCEPT } = OrderAcceptType,
	{ COLLECTED } = Fulfillments.Step,
	{ ORDER } = Settings.Name,
	{ ORDERS } = Providers.Module,
	isProduction = () => NODE_ENV === 'production'

module.exports = function(Grab) {

	Grab.orderIdOf = function (data) {
		const { orderID: orderId = null } = data
		return orderId
	}

	// ---  External event handlers

	/**
	 * Request Order
	 *	1. place order
	 *	2. accept Grab order (if necc), mark Grab fulfillment as requested
	 *	3. request fulfillment of order (initiate rest of fulfillments)
	 * @param {Object} data - order details
	 */
	Grab.handleExtOrderRequest = async function(data) {
		const { modelName: GRAB, app } = this,
			{ Order, Variant, Place } = app.models,
			{ merchantID, featureFlags } = data,
			orderId = this.orderIdOf(data),
			{ orderAcceptedType } = featureFlags,
			needAccept = orderAcceptedType === MANUAL_ACCEPT,
			[ store, grab ] = await Promise.all([
				Place.findOneByProviderStoreId(GRAB, merchantID),
				this.getProvider(GRAB, ORDERS, merchantID),
			]),
			storeId = store ? String(store.id) : undefined,
			order = grab.orders.toAppOrder({ ...data, storeId }, { sendReceipt: false }),
			{ fulfillment, createdAt } = order,
			{ accept, readyAt } = await Order.toAccept(order, [ fulfillment ], store)

		appNotify('[grab]requestOrder', { orderId, accept, orderAcceptedType }, null, '-order')

		if (needAccept && !accept) {
			await grab.orders.reject(orderId)
			return
		}

		try {
			const userId = shortId(),
				pricings = [],
				through = {
					attributedTo: { type: GRAB },
					touchedAt: createdAt
				},
				options = { through, noNotify: true },
				{ orderId: id } = await Variant.order(userId, order, pricings, options)

			if (needAccept) {
				await grab.orders.accept(orderId, readyAt, id)
			}
		}
		catch (err) {
			console.error(`[${GRAB}]requestOrder`, { orderId, err, data })
			appNotify(`[${GRAB}]requestOrder`, { orderId, err, data }, 'error')
			await grab.orders.reject(orderId)
		}
	}

	Grab.handleExtOrderScheduled = async function(fulfillment, data) {
		// ignore
	}

	Grab.handleExtOrderFulfillmentAccepted = async function(fulfillment, data) {
		// ignore
	}

	/**
	 * Driver allocated
	 * @param {Fulfillment} fulfillment instance
	 * @param {Object} data
	 *			{String} orderID
	 *			{Number} driverETA - in seconds
	 */
	Grab.handleExtOrderFulfillmentAllocated = async function(fulfillment, data) {
		const { modelName: GRAB } = this,
			{ orderID: id, driverETA } = data,
			pickup = new Date(Date.now() + (driverETA * 1000)),
			delivery = { id, pickup }

		await fulfillment.allocated(GRAB, delivery)
	}

	/**
	 * IMPORTANT - SCHEDULED orders missing ALLOCATED events (BUG!)
	 */
	Grab.handleExtOrderFulfillmentCollected = async function(fulfillment, data) {
		const { modelName: GRAB } = this,
			{ orderID: id } = data,
			{ flow } = fulfillment.toJSON(),
			{ at } = flow,
			prevAt = preStepOf(COLLECTED, flow),
			delivery = { id, pickup: new Date() },
			fulfill = at < prevAt
				? await fulfillment.allocated(GRAB, delivery)		// patch missing event
				: fulfillment

		await fulfill.collected()
	}

	Grab.handleExtOrderFulfillmentDelivered = async function(fulfillment, data) {
		await fulfillment.delivered()
	}

	Grab.handleExtOrderFulfillmentCancelled = async function(fulfillment, data) {
		const { modelName: GRAB, app } = this,
			{ Order } = app.models,
			{ orderID: orderId, code, message } = data,
			reason = `${message} (${code})`

		return Order.cancelByProviderOrderId(GRAB, orderId, { reason })
	}

	Grab.handleExtOrderFulfillmentFailed = async function(fulfillment, data) {
		// FIXME
	}

	// ---  Internal (CRM) event handlers  (reminder: only handle OURS)

	Grab.handleFulfillmentDeliverPacked = async function (fulfillment) {
		// not required
	}

	/**
	 * 1. Get Recipient info from Grab
	 * 2. Invite to join membership (unless suppressed in settings)
	 * 3. Set billing and mark order as paid
	 * @param {Object} orderData
	 */
	Grab.handleOrderFulfilled = async function (orderData) {
		const { modelName: GRAB, app } = this,
			{ Order, Fulfillment } = app.models,
			{ id: orderId, billingList = [], external, acquired: through } = orderData,
			{ attributedTo = {} } = through ?? {},
			{ type } = attributedTo

		if (type !== GRAB) return	// not ours, ignore

		const { noInvite, nonMemberFirstSale = true } = app.getSettings(ORDER),
			opt = { orderId, noInvite, nonMemberFirstSale, through },
			{ orderId: orderID, shop } = external[GRAB],
			filter = {
				where: {
					orderId,
					provider: GRAB,
					[`external.${GRAB}.orderId`]: orderID
				}
			}

		if (!isProduction()) {
			process.env.GRAB_ORDER_ID = orderID		// used by grab-sdk to mock order
		}

		try {
			const [ crmOrder, fulfillment, grab ] = await Promise.all([
					Order.findById(orderId),
					Fulfillment.findOne(filter),
					this.getProvider(GRAB, ORDERS, shop)
				]),
				[ order, { order: adminOrder } ] = await Promise.all([
					grab.orders.get(orderID),
					grab.admin.getOrder(orderID)
				]),
				billing = grab.orders.billing(order),
				customer = grab.admin.customer(adminOrder),
				phones = grab.admin.phones(adminOrder),
				recipient = recipientFrom(customer),
				destination = grab.admin.address(adminOrder),
				[ { person, membership } ] = await Promise.all([
					this.findOrJoinMembership(GRAB, undefined, customer, phones, opt),
					customer && fulfillment.updateAttributes({ recipient, destination })
				]),
				{ id: personId, memberId } = person ?? {},
				{ id: membershipId } = membership ?? {}

			billingList.unshift(billing)
			await crmOrder.markPaid({ billingList, personId, memberId, membershipId })
		}
		catch (err) {
			appNotify('[Grab]handleOrderFulfilled', err, 'error')
		}
	}
}
