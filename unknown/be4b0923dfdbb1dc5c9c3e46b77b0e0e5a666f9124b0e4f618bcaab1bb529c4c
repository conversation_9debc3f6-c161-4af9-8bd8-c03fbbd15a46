# Sales Service Settings Documentation

## Table of Contents
- [Order Settings](#order-settings)
- [Fulfillment Settings](#fulfillment-settings)
  - [Kitchen Settings](#kitchen-settings)
  - [Delivery Settings](#delivery-settings)
  - [Dine-in Settings](#dine-in-settings)
  - [Store Settings](#store-settings)
  - [Pickup Settings](#pickup-settings)
- [Booking Settings](#booking-settings)


## Order Settings

### Receipt and Invoice Configuration
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `sendReceipt` | Boolean | `false` | Send digital receipt to customer |
| `receiptTemplateId` | String | - | RichMessage (X) template ID for digital receipt |
| `receiptTTL` | Number | - | Number of days until receipt message expires |
| `noReceiptNumber` | Boolean | `false` | Use receipt number from Order provider instead of internal numbering |
| `printReceiptOnPay` | Boolean | `false` | Print receipt on successful payment (in-store only) |
| `handleInvoice` | Boolean | `false` | Enable Taiwan government invoice handling |
| `invoiceTemplate` | String | - | Template for invoice generation |
| `invoiceHeader` | String | - | Business name for invoice header |
| `invoiceFooter` | String | - | Custom text for invoice footer |

### Order Processing
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `numbering` | Object | - | Receipt numbering policy configuration |
| `noInvite` | Boolean | `false` | Suppress post-sale membership invite |
| `nonMemberFirstSale` | Boolean | `true` | Treat first external channel order as non-member sale |
| `webMenu` | Boolean | `false` | Enable Web Menu ordering system |

### Example Configuration
```json
{
  "name": "order",
  "value": {
    "sendReceipt": true,
    "receiptTemplateId": "67526f97320937003ac397b1",
    "pushTemplate": "dinein",
    "printReceiptOnPay": true,
    "handleInvoice": true,
    "invoiceTemplate": "tw-invoice"
  }
}
```

## Fulfillment Settings

### Kitchen Settings
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `false` | Enable kitchen fulfillment service |
| `capacity` | Number | `1` | Kitchen preparation capacity multiplier |
| `defaultItemPrepTime` | Number | - | Default preparation time per item (in seconds) |
| `maxScheduleWindow` | Number | - | Maximum advance scheduling window (in ms) |
| `prepareLookahead` | Number | - | Preparation planning window (in ms) |
| `minFirstEtaTime` | Number | - | Minimum first item ETA (in ms) |
| `minTimer` | Number | `3000` | Minimum kitchen timer interval (in ms) |
| `busyWaitTime` | Number | `900000` | Kitchen busy threshold (in ms) |
| `maxShelfLife` | Number | - | Maximum item shelf life (in ms) |
| `autoPrepare` | Boolean | `false` | Auto-start preparation when queued |
| `autoPack` | Boolean | `false` | Auto-pack items after preparation |
| `batchMultiplier` | Number | `1` | Batch processing time multiplier |
| `printTicket` | Boolean | `false` | Enable kitchen ticket printing |
| `ticketTemplate` | String | - | Kitchen ticket template |
| `printSubtickets` | Array | `[]` | Kitchen stations for separate tickets |
| `subticketTemplate` | String | - | Sub-ticket template |

### Delivery Settings
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `false` | Enable delivery service |
| `requiresPreparation` | Boolean | - | Require preparation step |
| `pickupBufferTime` | Number | `0` | Additional buffer time (in ms) |
| `fulfillFrom` | String | `""` | Default store placeId |
| `manualCollect` | Boolean | `false` | Enable manual collection |
| `ticketTemplate` | String | `"deliver-ticket"` | Delivery ticket template |
| `ticketHeader` | String | `""` | Ticket header text |
| `ticketFooter` | String | `""` | Ticket footer text |

### Dine-in Settings
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `false` | Enable dine-in service |
| `ticketTemplate` | String | - | Dine-in ticket template |

### Store Settings
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `false` | Enable store service |
| `requiresPreparation` | Boolean | - | Require preparation step |
| `ticketTemplate` | String | - | Store ticket template |

### Pickup Settings
| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | - | Enable pickup service |
| `requiresPreparation` | Boolean | - | Require preparation step |
| `pickupBufferTime` | Number | - | Buffer time for pickup (in ms) |

### Example Fulfillment Configuration
```json
{
  "name": "fulfillment",
  "value": {
    "kitchen": {
      "enabled": true,
      "printTicket": false,
      "ticketTemplate": "kitchen-ticket",
      "maxScheduleWindow": 14400000,
      "prepareLookahead": 2400000,
      "minTimer": 3000,
      "busyWaitTime": 2400000,
      "autoPrepare": true,
      "autoPack": true,
      "printSubtickets": ["主廚"],
      "subticketTemplate": "kitchen-ticket-sub",
      "minFirstEtaTime": 300000
    },
    "deliver": {
      "enabled": true,
      "ticketTemplate": "deliver-ticket",
      "ticketHeader": "逸之牛",
      "ticketFooter": "感謝！",
      "pickupBufferTime": 900000,
      "fulfillFrom": ""
    },
    "dinein": {
      "enabled": true,
      "ticketTemplate": null
    },
    "pickup": {
      "enabled": true
    }
  }
}
```

## Booking Settings

### Calendar Configuration
| Setting | Type | Description |
|---------|------|-------------|
| `user` | String | Email address for calendar sharing |
| `deleteWithResource` | Boolean | Delete calendar when resource is deleted |

### Table Management
| Setting | Type | Description |
|---------|------|-------------|
| `enabled` | Boolean | - | Enable table management |
| `minSize` | Number | Smallest table size |
| `maxSize` | Number | Largest table size |
| `leadTime` | Number | Required booking lead time |
| `maxAdvanceBooking` | Number | Maximum days for advance booking |
| `minPartySize` | Number | Minimum party size allowed |
| `maxPartySize` | Number | Maximum party size allowed |
| `maxCombined` | Number | Maximum tables for combining |
| `maxDuration` | Number | Maximum booking duration |
| `minCapacity` | Number | Minimum capacity threshold |
| `maxCapacity` | Number | Maximum capacity threshold |

### Example Configuration
```json
{
  "name": "booking",
  "value": {
    "calendar": {
      "user": "<EMAIL>",
      "deleteWithResource": true
    },
    "table": {
      "enabled": true,
      "minSize": 2,
      "maxSize": 12,
      "leadTime": 3600000,        // 1 hour in milliseconds
      "maxAdvanceBooking": 30,    // 30 days
      "minPartySize": 1,
      "maxPartySize": 24,
      "maxCombined": 3,
      "maxDuration": 7200000,     // 2 hours in milliseconds
      "minCapacity": 0,
      "maxCapacity": 100
    }
  }
}
```
