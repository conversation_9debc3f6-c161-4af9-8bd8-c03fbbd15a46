{"name": "FulfillItem", "plural": "FulfillItems", "base": "<PERSON><PERSON>", "idInjection": false, "strict": false, "mixins": {}, "properties": {"ids": {"type": [{"type": "string"}], "description": "CRM offer instance ids (voucher & ticket)"}, "digitalIds": {"type": [{"type": "string"}], "description": "X offer instance ids (voucher & ticket)"}, "fulfilled": {"quantity": {"type": "number", "default": 0, "description": "For partial fulfillments"}}, "fulfilledAt": {"type": "date", "description": "Time of COMPLETE fulfillment, ie. fulfilled.quantity = quantity"}}}