# Fulfillment System

## Overview
The Fulfillment system manages the process of delivering ordered items to customers through various channels including in-store pickup, delivery, kitchen preparation, and digital delivery.

```mermaid
graph TD
    Order[Order] --> FulfillmentType{Fulfillment Type}

    %% Fulfillment Types
    FulfillmentType --> Digital[Digital]
    FulfillmentType --> Kitchen[Kitchen]
    FulfillmentType --> StorePickup[Store/Pickup]
    FulfillmentType --> Delivery[Delivery]
    FulfillmentType --> DineIn[Dine-in]
    FulfillmentType --> VendingMachine[Vending Machine]
```

## Types of Fulfillment

### 1. Digital
- For digital products, vouchers, and tickets
- Instant delivery through digital channels
- Properties: `digitalIds` for tracking voucher/ticket instances

```mermaid
graph TD
    Digital[Digital] --> |Instant Delivery| DigitalDelivery[Digital Channels]
```

### 2. Kitchen
- For food preparation orders
- Tracks preparation status and timing
- Supports partial fulfillment of items
- Links to main fulfillment through `mainFulfillmentId`
- Features:
  - Preparation queue management with priority handling
  - Station-based routing
  - Auto-pack capabilities
  - Real-time status tracking
  - Dynamic preparation time optimization based on kitchen capacity
  - Batch multiplier support for preparation timing
  - Groups items by kitchen location
  - Supports preparation scheduling
  - Emits metrics for kitchen order states
  - Queue-based retry mechanisms
  - Real-time load adjustment

```mermaid
graph TD
    Kitchen[Kitchen] --> |1.Queued| KitchenPrep[Preparation Queue]
    KitchenPrep --> |2.Prepare| KitchenPacked[Packed]
```

For detailed kitchen operations, see [Kitchen Documentation](kitchen.md)

### 3. Store/Pickup
- For in-store collection
- Includes scheduled pickup times
- Features:
  - Collection window management
  - Staff handover tracking
  - Queue optimization
  - Ready-state management

```mermaid
graph TD
    StorePickup[Store/Pickup] --> |1.Requested| PickupQueue[Queue]
    PickupQueue --> |2.Packed| ReadyForPickup[Ready for Pickup]
    ReadyForPickup --> |3.Collected| PickupComplete[Complete]
```

### 4. Delivery
- For delivery to customer location
- Features:
  - Provider integration
  - Status synchronization
  - Driver tracking
  - Delivery window management
  - Scheduled delivery windows
  - Real-time driver location updates
  - Nearby driver notifications

```mermaid
graph TD
    Delivery[Delivery] --> |1.Requested| ProviderAllocation[Provider Allocation]
    ProviderAllocation --> |2.Allocated| DeliveryPacked[Packed]
    DeliveryPacked --> |3.Collected| InDelivery[In Delivery]
    InDelivery --> |4.Delivered| DeliveryComplete[Complete]
```

### 5. Dine-in
- For on-premise dining
- Features:
  - Table service management
  - Kitchen coordination
  - Service timing
  - Links to kitchen fulfillment

```mermaid
graph TD
    DineIn[Dine-in] --> |1.Requested| TableService[Table Service]
    TableService --> |2.KitchenLink| KitchenPrep[Kitchen Preparation]
```

### 6. Vending Machine

Key features:
- Automated dispensing
- Digital access code generation
- Real-time inventory tracking
- Multiple vending purposes:
  - Direct ordering
  - Order pickup
  - Reward redemption
- Machine status monitoring
- Provider integration

```mermaid
graph TD
    VendingMachine[Vending Machine] --> |1.Requested| MachineAllocation[Machine Allocation]
    MachineAllocation --> |2.Reserved| ReadyForCollection[Ready for Collection]
    ReadyForCollection --> |3.Collected| Complete[Complete]
```

For implementation details and flow specifications, see: [Vending Machine Documentation](vending-machine.md)

## Core Properties

### Required Fields
- `type`: Type of fulfillment (one of the above types)
- `status`: Current fulfillment status

### Optional Fields
- `digitalIds`: Array of digital IDs
- `mainFulfillmentId`: ID of main fulfillment (kitchen)
- `providerId`: ID of delivery provider
- `tableId`: ID of table (dine-in)
- `machineId`: ID of vending machine
- `fulfilledAt`: Completion timestamp

## Status and Flow Management

### Status States
- `null`: Initial state
- `pending`: Awaiting processing
- `open`: In progress
- `success`: Completed successfully
- `cancelled`: Cancelled by user/system
- `error`: Failed due to error
- `failure`: Failed with specific failure reason
- `partial`: Partially fulfilled
- `packed`: Ready for delivery/pickup
- `collected`: Picked up by customer/driver
- `delivered`: Delivered to customer

```mermaid
graph TD
    subgraph Status States
        Pending[Pending]
        Open[Open]
        Success[Success]
        Cancelled[Cancelled]
        Error[Error]
        Partial[Partial]
    end
```

### Flow Management
1. **Basic Flow Steps**
   The system enforces a strict order of steps:
   1. `ordered`: Initial order creation
   2. `requested`: Fulfillment request sent
   3. `allocated`: Resources/driver assigned (delivery only)
   4. `queued`: Added to preparation queue (kitchen only)
   5. `prepare`: Being prepared (kitchen)
   6. `packed`: Ready for delivery/pickup
   7. `collected`: Picked up by customer/driver
   8. `delivered`: Delivered to customer (delivery only)

2. **Flow Rules**
   - Steps must follow defined sequence
   - Each step requires previous step completion
   - Some steps allow repetition (e.g., allocation for delivery)
   - Status changes emit events
   - Flow tracks current step index and history
   - Last step completion sets status to 'success'
   - Includes queue-based retry mechanisms
   - Supports error resilience with fallback handling

3. **Type-Specific Flows**
   - Kitchen Orders: ordered → requested → prepare → packed
   - Delivery Orders: ordered → requested → allocated → packed → collected → delivered
   - Pickup Orders: ordered → requested → packed → collected
   - Dine-in Orders: ordered → requested → prepare → packed

4. **Flow Context**
   - Tracks current step index (`flow.at`)
   - Validates step sequence and prerequisites
   - Maintains step history in `when` object
   - Prevents invalid step transitions
   - Supports step repetition for specific cases
   - Emits events on step transitions

### Staff Operations
- Staff can initiate step transitions
- Supports manual queue management
- Allows preparation cancellation
- Enables item-level fulfillment tracking
- Supports ticket reprinting

## Item Fulfillment Tracking

### Structure
- `items`: Array of items
- `status`: Item fulfillment status
- `quantity`: Quantity fulfilled
- `preparationTime`: Preparation time
- `shelfLife`: Shelf life (if applicable)
- `fulfilledAt`: Completion timestamp

### Features
- Concurrent fulfillment through queuing
- Individual item quantity tracking
- Event emission for each fulfilled item
- Automatic packed status when all items complete
- Partial fulfillment state handling

## Kitchen Queue Management

- Supports priority queuing of orders
- Tracks preparation start/end times
- Groups orders by kitchen station
- Manages concurrent preparation capacity
- Provides real-time preparation status
- Supports preparation cancellation
- Emits metrics for queue state changes
- Dynamic preparation time calculation based on:
  - Kitchen capacity
  - Current load
  - Batch multipliers
  - Item-specific timing

### Queue Operations
- `setQueued`: Manually queue fulfillment
- `getPrepareList`: Get preparation queue for station
- `getPackList`: Get packing queue for station
- `itemsFulfilled`: Mark individual items as fulfilled
- `schedulePack`: Auto-pack scheduling

### Auto-Pack Capabilities
- Configurable through kitchen settings
- Automatically schedules packing after preparation
- Supports preparation time optimization
- Includes queue number generation
- Prints kitchen tickets automatically
- Emits metrics for tracking

### Preparation Time Management
- Dynamic calculation based on item-level times
- Adjusts for kitchen capacity
- Supports batch multipliers
- Uses default preparation time fallback
- Real-time adjustment based on kitchen load

## Provider Integration

### Supported Providers
- Shopify
- GrabFood
- GrabMart
- UberEats
- Lalamove
- SFCC (Salesforce Commerce Cloud)

### Implementation Requirements
Providers must implement:
```javascript
handleExtOrderFulfillmentCreated()
handleExtOrderFulfillmentUpdated()
handleExtOrderFulfillmentDelivered()
handleExtOrderFulfillmentCancelled()
```

### Integration Details
- Provider capability configuration required
- Provider matching enforced for delivery requests
- Queue-based retry mechanisms
- Error resilience with fallback handling
- External ID mapping requirements:
  - Shopify: customerId, orderId
  - Lalamove: orderId, deliveryId
  - Grab: orderId, deliveryId
  - SFCC: orderId

## API Endpoints

### Staff Operations
GET    /staff/fulfillments/prepare             // Get preparation list
POST   /staff/fulfillments/:id/prepare         // Start preparation
POST   /staff/fulfillments/:id/packed          // Mark as packed
POST   /staff/fulfillments/:id/fulfill/items   // Update item fulfillment
POST   /staff/fulfillments/:id/queue           // Priority queue fulfillment
GET    /staff/fulfillments/kitchen             // Get kitchen fulfillment by mainFulfillmentId
POST   /staff/fulfillments/:id/prepare/cancel  // Cancel preparation
GET    /staff/fulfillments/pack                // Get pack list

### Customer Operations
GET    /fulfillments/:id/status    // Check fulfillment status
POST   /fulfillments/:id/cancel    // Cancel fulfillment

### Service Operations
POST   /perkd/fulfillments/:type/availability  // Check service availability
Parameters:
  - placeId: Store identifier (required)
  - items: Items to fulfill (required)
  - from: Start time (default: now)
  - duration: Window duration in minutes

## Events

### Core Events
- `fulfillment.created`
- `fulfillment.updated`
- `fulfillment.{type}.{step}` (e.g., `fulfillment.kitchen.prepare`)
- `fulfillment.item.success`
- `fulfillment.item.error`

### Provider Events
- `fulfillment.provider.allocated`: Driver/resource allocated
- `fulfillment.provider.arrived`: Driver arrived at pickup
- `fulfillment.provider.nearby`: Driver near pickup location
- `fulfillment.provider.collected`: Order collected
- `fulfillment.provider.delivered`: Delivery completed

### Provider Integration Events
- `fulfillment.created`: New fulfillment created
- `fulfillment.updated`: Fulfillment status updated
- `fulfillment.accepted`: Provider accepted fulfillment
- `fulfillment.allocated`: Driver/resource allocated
- `fulfillment.nearby`: Driver near pickup location
- `fulfillment.arrived`: Driver arrived at pickup
- `fulfillment.collected`: Order collected by driver
- `fulfillment.delivered`: Delivery completed
- `fulfillment.cancelled`: Fulfillment cancelled
- `fulfillment.failed`: Fulfillment failed

## Error Handling

### Common Errors
- `not_ready`: Previous step not completed
- `already_{step}`: Step already completed
- `provider_mismatch`: Wrong provider for fulfillment
- `invalid_transition`: Invalid flow step transition
- `queue_error`: Queue processing error

### Error Response Format
```javascript
{
  error: {
    code: String,
    message: String,
    details: Object
  }
}
```

## Best Practices

### Partial Fulfillment
- Track item-level fulfillment
- Update quantities progressively
- Mark `fulfilledAt` only when complete

### Status Updates
- Use queue for status updates
- Emit events after successful updates
- Include all relevant metadata
- Implement retry mechanisms for failed updates

### Provider Integration
- Validate provider capabilities
- Handle provider-specific errors
- Maintain provider state mapping
- Implement queue-based retries
- Handle provider state synchronization

### Performance Optimization
- Index frequent queries
- Implement request queuing
- Cache frequent data
- Rate limit provider calls
- Batch updates where possible
- Use connection pooling
- Build error resilience
- Implement fallback mechanisms
