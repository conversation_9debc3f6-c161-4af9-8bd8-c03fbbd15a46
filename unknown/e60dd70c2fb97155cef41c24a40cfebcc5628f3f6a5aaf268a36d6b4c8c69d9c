/**
 *  @module Mixin:FindFulfillment
 */

const { Fulfillments } = require('@crm/types'),
	{ Settings, Products } = require('@crm/types'),
	{ dayjs } = require('@perkd/utils')

const { KITCHEN, DINEIN } = Fulfillments.Type,
	{ OPEN, CANCELLED } = Fulfillments.Status,
	{ PREPARE, PACKED } = Fulfillments.Step,
	{ BOOKING, LOCALE } = Settings.Name,
	{ TABLE } = Products.ResourceKind,
	FIELDS = [ 'id', 'priority', 'destination', 'minTime', 'maxTime', 'itemList', 'prepare', 'receipt',
		'note', 'orderId', 'mainFulfillmentType' ]

module.exports = function(Fulfillment) {

	/**
	 * Find fulfillment(s) of provider by (external) order id OR CRM orderId
	 * @param	{String} provider
	 * @param	{String} orderId of provider OR CRM orderId (machine)
	 * @return	{Fulfillment}
	 */
	Fulfillment.findOneByProviderOrderId = async function(provider = '', orderId, where = {}) {
		const externalId = orderId ? String(orderId) : null,
			key = `external.${provider.toLowerCase()}.orderId`,
			filter = {
				where: {
					or: [
						{ orderId },
						{ [key]: externalId },
					],
					...where
				},
			}

		return Fulfillment.findOne(filter)
	}

	/**
	 * Find fulfillment(s) of provider by (external) order id
	 * @param	{String} provider
	 * @param	{String} orderId of provider
	 * @return	{Fulfillment[]}
	 */
	Fulfillment.findByProviderOrderId = async function(provider = null, orderId) {
		const { models } = Fulfillment.app,
			{ Order } = models,
			order = await Order.findOneByProviderOrderId(provider, orderId),
			filter = {
				where: { provider }
			}

		return order ? order.fulfillments.find(filter) : []
	}

	// --- Workflow Lists

	/**
	 * Get Prepare list for Store (Kitchen Order Tickets)
	 * @param	{String} placeId
	 * @param	{String} type of fulfillment (omit for all types)
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 */
	Fulfillment.prepareList = async function (placeId, type, stations) {
		const filter = {
				where: {
					type,
					status: OPEN,
					placeId,
					'when.prepare': null,
					'when.queued': { neq: null },
				},
				order: 'prepare.startTime ASC',
				fields: FIELDS
			},
			fulfillments = await Fulfillment.find(filter)

		return unfulfilledForStations(fulfillments, stations)
	}

	/**
	 * Get Pack list for Store (Kitchen Order Tickets)
	 * @param	{String} placeId
	 * @param	{String} type of fulfillment (omit for all types)
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 */
	Fulfillment.packList = async function (placeId, type, stations) {
		const filter = {
				where: {
					type,
					status: OPEN,
					placeId,
					'when.queued': { neq: null },
					'when.packed': null,
				},
				order: 'prepare.startTime ASC',
				fields: FIELDS
			},
			fulfillments = await Fulfillment.find(filter)

		return unfulfilledForStations(fulfillments, stations)
	}

	/**
	 * Cancelled fulfillments for period
	 * @param	{String} placeId
	 * @param	{String} type of fulfillment (omit for all types)
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 * @param	{Date} from start time
	 * @param	{Date} to end time
	 */
	Fulfillment.cancelledListForPeriod = async function (placeId, type, stations, from, to) {
		const filter = {
				where: {
					type,
					status: CANCELLED,
					placeId,
					createdAt: {
						between: [ from, to ]
					}
				},
				order: 'prepare.startTime DESC',
				fields: FIELDS
			},
			fulfillments = await Fulfillment.find(filter)

		return unfulfilledForStations(fulfillments, stations)
	}

	/**
	 * Get the next fulfillment to prepare
	 * @param	{String} placeId
	 * @param	{String} [type]
	 * @param	{String[]} [stations] - name of fulfillment stations to include
	 * @return	{Object | void} not instance
	 */
	Fulfillment.nextToPrepare = async function (placeId, type = KITCHEN, stations) {
		const NOW = new Date(),
			whenPrepare = `when.${PREPARE}`,
			whenPacked = `when.${PACKED}`,
			filter = {
				where: {
					placeId,
					type,
					status: OPEN,
					[whenPrepare]: null,
					[whenPacked]: null,
					'prepare.startTime': { gt: NOW }
				},
				order: 'prepare.startTime ASC'
			},
			fulfillments = await Fulfillment.find(filter),
			[ next ] = unfulfilledForStations(fulfillments, stations)

		return next
	}

	/**
	 * Get Open fulfillments for tables
	 * @param {Spot[]} tables
	 * @param {Boolean} [kitchen] - include kitchen fulfillments
	 */
	Fulfillment.findOpenForTables = async function (tables, kitchen = false) {
		const { app } = Fulfillment
		const { timeZone } = app.getSettings(LOCALE)
		const { maxDuration } = app.getSettings(BOOKING)[TABLE]
		const earliest = dayjs().tz(timeZone).subtract(maxDuration, 'minutes').toDate()
		const or = [ { type: DINEIN } ]
		const promises = []

		if (kitchen) {
			or.push({ mainFulfillmentType: DINEIN })
		}

		for (const table of tables) {
			const { placeId, resourceId } = table,
				filter = {
					where: {
						placeId,
						status: OPEN,
						createdAt: { gte: earliest },		// safety, exclude zombie old open fulfillments
						or,
						'destination.resourceId': resourceId
					}
				}

			promises.push(Fulfillment.find(filter))
		}

		const fulfillments = await Promise.all(promises)
		return fulfillments.flat()
	}

	// ---- Private Functions

	/**
	 * Return unfulfilled fulfillments (plain objects) for stations
	 * @param	{Fulfillment[]} fulfillments
	 * @param	{String[]} stations
	 * @return	{Object[]} fulfillment objects
	 */
	function unfulfilledForStations(fulfillments, stations = []) {
		const filtered = []

		for (const fulfillment of fulfillments) {
			const fulfill = fulfillment.toJSON(),
				{ itemList = [] } = fulfill

			fulfill.itemList = itemList.filter(i => !i.fulfilledAt)		// not fulfilled

			if (stations.length) {
				fulfill.itemList = fulfill.itemList.filter(item =>
					stations.some(station => item.product.tags?.kitchen?.includes(station))
				)
			}
			if (fulfill.itemList.length) {
				filtered.push(fulfill)
			}
		}

		return filtered
	}
}
