/**
 *  @module Model:shopify (OrderProvider)
 */

const { Touchpoints, Providers, Offers, Fulfillments } = require('@crm/types'),
	{ rateLimit } = require('@perkd/utils'),
	{ Metrics } = require('@perkd/metrics'),
	{ Err } = require('@perkd/errors'),
	{ TransactionKind, TransactionStatus, FinancialStatus, FulfillmentStatus, PerkdNoteAttribute,
		fulfillmentChanges, orderChanges, isShopifyOrder } = require('@provider/shopify')

const { SHOPIFY, POS } = Providers.PROVIDER,
	{ ORDERS } = Providers.Module,
	{ STORE } = Touchpoints.Type,
	{ DIGITAL } = Fulfillments.Type,
	{ RedemptionChannel } = Offers,
	{ SALE } = TransactionKind,
	{ PAID } = FinancialStatus,
	{ SUCCESS, CANCELLED } = FulfillmentStatus,
	{ ORDER_ID } = PerkdNoteAttribute,
	metrics = new Metrics(SHOPIFY),
	{ registry: Stats } = metrics,
	limiter = rateLimit({ limit: 5, interval: 2000 }),		// Shopify paid a/c: 2 per sec (REST), dev a/c: 5 per min
	Q_CREATE_ORDER = `${SHOPIFY}:createorder`

module.exports = function(Shopify) {

	//  ---  Handle Shopify Order events

	Shopify.handleExtOrderCreated = async function(extOrder = {}) {
		const { id, source_name: source, note_attributes: notes, shop } = extOrder

		if (notes.find(n => n.name === ORDER_ID)) return	// skip ours

		const api = await this.getProvider(SHOPIFY, ORDERS, shop)
		if (!api) return

		const { autoFulfill, nonMemberSale, noInvite, nonMemberFirstSale } =  api.module(ORDERS).options ?? {},
			exists = await this.findOrder(id)

		if (exists) return

		const data = await this.toOrder(extOrder, { nonMemberSale, noInvite, nonMemberFirstSale }),
			[ order ] = await this.findOrCreateOrder(data),
			{ when } = order,
			{ paid } = when

		if (paid) {
			if (source === POS) {
				await order.createInvoice()
			}
			await order.markPaid(undefined, true)
		}

		return order
	}

	// Shopify emits paid event only when order is fully paid
	Shopify.handleExtOrderPaid = async function(data = {}) {
		const { SUCCESS } = TransactionStatus,
			{ id, financial_status, note_attributes, processed_at, tags, source_name: source, shop } = data,
			extOrder = { id, financial_status, note_attributes, processed_at, tags }

		if (financial_status !== PAID || note_attributes.find(n => n.name === ORDER_ID)) return

		const api = await this.getProvider(SHOPIFY, ORDERS, shop)
		if (!api) return

		const orderId = String(id),
			order = await this.findOrder(orderId, { 'when.paid': null })

		if (!order) return

		const transactions = await api.transactions.list(orderId),
			paidTx = transactions.find(t => t.status === SUCCESS),
			options = {}

		if (paidTx) {
			options.paidAt = paidTx.processed_at
		}

		const orderData = await api.orders.toOrder(extOrder, options),
			{ receipt = {}, when, tags: t } = orderData

		if (source === POS) {
			receipt.invoice = await order.createInvoice()
		}

		return order.markPaid({ receipt, when, tags: t }, true)
	}

	/**
	 * Handle Shopify Fulfillment event
	 * @param	{Object} extFulfillment - shopify
	 * @return {Promise<void>}
	 */
	Shopify.handleExtOrderFulfillmentEvent = async function(extFulfillment) {
		const { Event } = this.app,
			{ order_id, status, shop } = extFulfillment ?? {}

		if (![ SUCCESS, CANCELLED ].includes(status)) return

		const order = await this.findOrder(order_id),
			{ id: orderId } = order,
			fulfillment = await this.findOneFulfillment(orderId)

		if (!order || !fulfillment) return

		// do not update digital fulfill orders or fulfilled orders
		const { type } = fulfillment,
			{ when } = order

		if (type === DIGITAL || when.fulfilled) return

		const api = await this.getProvider(SHOPIFY, ORDERS, shop)
		if (!api) return

		const extOrder = await api.orders.get(order_id)
		if (!extOrder) return

		const fulfillmentUpdates = fulfillmentChanges(fulfillment, extFulfillment),
			orderUpdates = orderChanges(order, extOrder),
			updatedFulfillment = await fulfillment.updateAttributes(fulfillmentUpdates),
			updatedOrder = await order.updateAttributes(orderUpdates)

		// --- events
		appEmit(Event.sales.order.updated, updatedOrder)

		if (updatedOrder.when.delivered) {
			appEmit(Event.sales.order.fulfilled, updatedOrder)
		}
	}

	Shopify.handleExtOrderFulfillmentCreated
	= Shopify.handleExtOrderFulfillmentUpdated
	= Shopify.handleExtOrderFulfillmentEvent

	Shopify.handleExtOrderDeleted = async function(data) {
		data.cancel_reason = 'deleted'
		return this.handleExtOrderCancelled(data)
	}

	Shopify.handleExtOrderCancelled = async function(data) {
		const order = await this.findOrder(data.id),
			{ cancelled } = order?.when || {}

		if (order && !cancelled) { // ignore cancelled order
			return this.cancelCrmOrder(order, data)
		}
	}

	Shopify.handleExtOrderRefund = async function(data) {
		return this.refundOrder(data)
	}

	//  ---  Handle CRM Order events

	Shopify.handleOrderCreated = async function(data) {
		if (isShopifyOrder(data)) return undefined	// TODO: skip ours and orders no need to sync to shopify (e.g. gift card campaign)

		const order = await this.createOrder(data)
		return this.linkOrderToCrm(order, data)
	}

	/**
	 * Handle CRM Order paid event
	 * @param	{Object} crmOrder
	 * @return	{Promise<Object>} order
	 */
	Shopify.handleOrderPaid = async function(crmOrder = {}) {
		const { through = {} } = crmOrder,
			{ attributedTo = {} } = through,
			{ type } = attributedTo

		if (type === SHOPIFY) return crmOrder	// TODO: skip ours and orders no need to sync to shopify (e.g. gift card campaign)

		if (isShopifyOrder(crmOrder)) {
			return this.markOrderAsPaid(crmOrder)
		}

		const order = await this.createOrder(crmOrder)
		return this.linkOrderToCrm(order, crmOrder)
	}

	/**
	 * NOTE: Too many CRM updates that trigger Shopify order update
	 * Should only update based on specific transaction
	 */
	Shopify.handleOrderUpdated = async function(data = {}) {
	// 	const { through = {} } = data,
	// 		{ attributedTo = {} } = through,
	// 		{ type } = attributedTo

		// 	if (type === SHOPIFY) return undefined

	// 	if (isShopifyOrder(data)) {
	// 		return this.updateOrder(data)
	// 	}
	}

	Shopify.handleOrderFulfilled = async function(data = {}) {
		const { through } = data,
			{ attributedTo = {} } = through ?? {},
			{ type } = attributedTo

		if (type !== SHOPIFY) {
			await this.fulfillOrder(data)
		}
	}

	Shopify.handleOrderCancelled = async function(data) {
		const { through } = data,
			{ attributedTo = {} } = through ?? {},
			{ type } = attributedTo

		if (type !== SHOPIFY) {
			await this.cancelOrder(data)
		}
	}

	// --- Order Methods ---

	Shopify.findOrder = async function(orderId, where) {
		const { Order } = Shopify.app.models
		return Order.findOneByProviderOrderId(SHOPIFY, orderId, where)
	}

	Shopify.findOneFulfillment = async function(orderId, where) {
		const { Fulfillment } = this.app.models
		return Fulfillment.findOne({ where: { orderId } })
	}

	Shopify.findOrCreateOrder = async function(order = {}) {
		const { Order } = this.app.models,
			{ external = {} } = order,
			{ shopify = {} } = external,
			{ orderId = null } = shopify,
			filter = {
				where: { [`external.${SHOPIFY}.orderId`]: orderId },
			}

		return Order.findOrCreate(filter, order)
	}

	Shopify.fulfillCrmOrder = async function(order, extOrder, api) {
		// TODO: update fulfillmentList?
		return order.requestFulfillment()
	}

	/**
	 * Link Shopify Order to CRM order
	 * @param	{Object} order shopify
	 * @param	{Object} data CRM order
	 * @return {Object} updated crmOrder
	 */
	Shopify.linkOrderToCrm = async function(order, data = {}) {
		if (!order) return undefined

		const { Order } = this.app.models,
			{ id: orderId, customer, fulfillmentOrderId = undefined } = order,
			{ id, acquired = {} } = data,
			{ attributedTo = {} } = acquired,
			{ name: shop } = attributedTo,
			customerId = customer ? customer.id : undefined,
			shopify = { orderId, customerId, fulfillmentOrderId, shop },
			crmOrder = await Order.findById(id)

		if (!crmOrder) {
			appNotify('[Shopify]linkOrderToCrm: CRM order not found', { crmOrder: data, order }, 'error')
			return undefined
		}

		const { external } = crmOrder,
			changes = {
				external: { ...external, shopify },
			}

		return crmOrder.updateAttributes(changes)
	}

	Shopify.cancelCrmOrder = async function(order, extOrder = {}) {
		const { cancel_reason: reason, cancelled_at: cancelledAt } = extOrder,
			through = {
				// type & format unknown
				attributedTo: { type: SHOPIFY },
				touchedAt: new Date(cancelledAt)
			}

		return order.cancel({ cancelledAt, reason, through })
	}

	Shopify.refundOrder = async function(extRefund = {}) {
		const { order_id, note: reason, transactions = [] } = extRefund,
			order = await this.findOrder(order_id)

		if (!order || !order.when.paid) return

		// FIXME

		const [ transaction ] = transactions,
			{ id: transactionId, amount: refundAmount } = transaction,
			{ amount: totalPrice, itemList = [] } = order,
			through = {
				attributedTo: { type: SHOPIFY },
				touchedAt: new Date()
			}

		if (Number(refundAmount) !== totalPrice) {
			appLog('refund_fail', new Err({ code: 'shopify_refund_partial', details: { order_id, transactionId, refundAmount, totalPrice } }))
			return
		}

		return order.cancel({ reason, through })
	}

	// ---  Shopify Orders  ---

	/**
	 * Create Shopify Order (from CRM order)
	 * @param	{Object} crmOrder
	 * @param	{Object[]} crmFulfillments
	 * @param	{Object} options
	 * 			{Boolean} markPaid
	 * @return {Order} - shopify order
	 */
	Shopify.createOrder = async function(crmOrder = {}, crmFulfillments = [], options = {}) {
		const { Order } = Shopify.app.models,
			{ acquired = {}, id } = crmOrder,
			{ attributedTo = {} } = acquired,
			{ name: shop } = attributedTo,
			api = await this.getProvider(SHOPIFY, ORDERS, shop)

		if (!api) return undefined

		const { noStore, linkToMerchant } = api.module(ORDERS).options ?? {}

		options.linkToMerchant = linkToMerchant	// inject

		if (noStore && acquired.type === STORE) return undefined

		return this.queue(`${Q_CREATE_ORDER}:${id}`, async () => {
			const order = id ? await Order.findById(id) : undefined,
				{ orderId } = order?.external?.shopify || {}

			if (orderId) return

			if (limiter.tryRemoveTokens(1)) {
				return buildAndCreate(api, crmOrder, crmFulfillments, options)
			}

			return limiter.removeTokens(1).then(() => buildAndCreate(api, crmOrder, crmFulfillments, options))
		})
	}

	/**
	 * Update corresponding shopify orders when crm order is updated
	 * Only receipt related update for now, can extend if there are further requirements
	 * @param	{Object} crmOrder
	 * @return {Order} - shopify order
	 */
	// Shopify.updateOrder = async function(crmOrder = {}) {
	// 	const { external = {}, receipt = {} } = crmOrder,
	// 		{ invoice } = receipt,
	// 		{ shopify = {} } = external,
	// 		{ orderId } = shopify

	// 	if (!orderId || !invoice) return undefined

	// 	const shop = shopOfOrder(crmOrder),
	// 		api = await this.getProvider(SHOPIFY, ORDERS, shop)

	// 	if (!api) return undefined

	// 	const order = await api.orders.get(orderId),
	// 		{ note_attributes: existing_note_attributes } = order,
	// 		note_attributes = api.orders.buildNoteAttributes(crmOrder)

	// 	for (const note of existing_note_attributes) {
	// 		if (!note_attributes.find( i => i.name === note.name)) note_attributes.push(note)
	// 	}

	// 	await limiter.removeTokens(1)
	// 	return api.orders.update(orderId, { note_attributes })
	// 		.catch(err => {
	// 			appLog('shopify_orders_update_fail', { err, orderId: crmOrder.id })
	// 			throw err
	// 		})
	// }

	/**
	 * Mark Shopify Order as Paid (of corresponding CRM order)
	 * @param	{Object} crmOrder
	 * @return {Order} - shopify order
	 */
	Shopify.markOrderAsPaid = async function(crmOrder = {}) {
		const { external = {}, amount, billingList = [] } = crmOrder,
			{ shopify = {} } = external,
			{ orderId } = shopify

		if (!orderId || !amount) return undefined

		const shop = shopOfOrder(crmOrder),
			api = await this.getProvider(SHOPIFY, ORDERS, shop)

		if (!api) return undefined

		const order = await api.orders.get(orderId),
			{ financial_status } = order

		const transactions = api.orders.fromBillings(billingList),
			note_attributes = api.orders.buildNoteAttributes(crmOrder),
			newTags = api.orders.buildTags(crmOrder),
			tags = Array.from(new Set([ ...order.tags.split(','), ...newTags ])).join(',')	// make unique

		await limiter.removeTokens(1)
			.then(() => api.orders.update(orderId, { note_attributes, tags }))
			.catch(err => {
				appLog('shopify_markPaidExtOrder_orders_update_fail', { err, orderId: crmOrder.id })
				throw err
			})

		if (financial_status === PAID) return undefined

		for (const transaction of transactions) {
			const { kind } = transaction

			if (kind === SALE) {
				await limiter.removeTokens(1)
					.then(() => api.transactions.add(orderId, transaction))
					.catch(err => {
						appLog('shopify_transaction_add_sale_fail', { err, orderId: crmOrder.id })
					})
			}
		}
	}

	/**
	 * Fulfill Shopify Order (of corresponding CRM order)
	 * @param	{Object} crmOrder
	 * @return {Order} shopify order
	 */
	Shopify.fulfillOrder = async function (crmOrder = {}) {
		const { paid } = crmOrder.when,
			{ external = {} } = crmOrder,
			{ shopify = {} } = external,
			{ orderId, fulfillmentOrderId } = shopify

		if (!orderId) return

		const shop = shopOfOrder(crmOrder),
			api = await this.getProvider(SHOPIFY, ORDERS, shop)

		if (!api) return

		try {
			const flmtOrderId = fulfillmentOrderId
				? fulfillmentOrderId
				: await api.fulfillmentOrders.getId(orderId)

			await limiter.removeTokens(1).then(() => api.fulfillments.create(flmtOrderId))
		}
		catch (err) {
			appLog('shopify_order_fulfill_fail', { err, orderId: crmOrder.id })
			throw err
		}
	}

	/**
	 * Cancel Shopify Order (of corresponding CRM order)
	 * @param	{Object} crmOrder
	 * @return {Order} shopify order
	 */
	Shopify.cancelOrder = async function(crmOrder = {}) {
		const { paid } = crmOrder.when,
			{ external = {} } = crmOrder,
			{ shopify = {} } = external,
			{ orderId } = shopify

		if (!orderId) return

		const shop = shopOfOrder(crmOrder),
			api = await this.getProvider(SHOPIFY, ORDERS, shop)

		if (!api) return

		try {
			await limiter.removeTokens(1).then(() => api.orders.cancel(orderId, { restock: true }))

			if (!paid) {
				await api.orders.delete(orderId)
			}
		}
		catch (err) {
			appLog('shopify_order_cancel_fail', { err, orderId: crmOrder.id })
			throw err
		}
	}

	/**
	 * Transform Shopify Order to CRM Order
	 * @param	{Object} data - https://shopify.dev/docs/admin-api/rest/reference/orders/order
	 * @param	{Object} options
	 * 			{Boolean} nonMemberSale
	 *			{Boolean} noInvite - no post-sales invite
	 *			{Boolean} nonMemberFirstSale - on post-sales invite, this order is treated as non-member sale
	 *			{Date} paidAt
	 * @return	{Promise<Object>} order
	 */
	Shopify.toOrder = async function(data, options) {
		const { Staff } = Shopify.app.models,
			api = await Shopify.getProvider(SHOPIFY, ORDERS)

		if (!api) return

		const { id, currency, taxes_included: taxIncluded } = data,
			{ paidAt, nonMemberSale, noInvite, nonMemberFirstSale } = options,
			order = api.orders.toOrder(data, { paidAt }),
			itemList = api.orders.toItems(data, currency, taxIncluded),
			{ cardNo, discountCodes, ...external } = order.external[SHOPIFY]

		// TODO post-processing of itemList
		await Shopify.linkProductsVariants(itemList)

		order.itemList = itemList

		// establish relations with Person, Membership, Staff & Store
		const customer = api.orders.customer(data),
			phones = api.orders.phones(data),
			{ through, storeId, staffId } = order,
			opt = { orderId: id, noInvite, nonMemberFirstSale, through },
			[ { person, membership }, place, staff ] = await Promise.all([
				Shopify.findOrJoinMembership(SHOPIFY, cardNo, customer, phones, opt),
				Shopify.findOrCreateStore(SHOPIFY, storeId),
				staffId ? Staff.findOrCreateProviderStaff(POS, staffId) : undefined,
			])

		if (membership && !nonMemberSale) {
			const { id: membershipId, tierLevel, cardNumber, programId, memberId } = membership

			order.program = { id: programId, tierLevel, cardNumber }
			order.memberId = memberId
			order.membershipId = membershipId
		}
		if (person) {
			order.personId = person.id
		}
		if (staff) {
			order.staffId = staff.id
		}
		if (place) {
			const { id: placeId, name } = place

			through.location = { type: STORE, id: placeId, name }
			order.storeId = placeId
		}

		// redeem offers
		const { personId } = order,
			{ touchedAt } = through

		if (personId && discountCodes) {
			const codes = discountCodes.split(','),
				offers = await Shopify.redeemOffers(codes, RedemptionChannel.STORE, personId, touchedAt)

			for (const code of codes) {
				const { id: offerId, discount = {} } = offers.find(o => o.code.store === code) || {},
					found = order.discountList.find(d => d.code === code),
					offer = { offerId, code, ...discount }

				if (found) Object.assign(found, offer)
				else order.discountList.push(offer)
			}
		}

		// remove extraneous payloads/parameters
		order.external[SHOPIFY] = external

		return order
	}

	/**
	 * Substitute product & variant in Items with CRM product & variant instances
	 * @param	{Object[]} items - itemList of CRM Order (product & variant mutated)
	 * @param	{String} orderId
	 * @param	{String} currency
	 * @param	{Boolean} taxInclusive
	 * @return	{Promise<Object[]>} - [{ product: {}, variant: {}, quantity: 1, discountAmount: 0, amount: 10, fulfillmentType: 'dinein' }]
	 */
	Shopify.linkProductsVariants = async function(items) {
		const { Product, Variant } = Shopify.app.models,
			productIds = [],
			variantIds = [],
			skus = []

		// link Shopify products & variants with corresponding CRM instances
		for (const item of items) {
			const { product, variant } = item,
				{ sku } = variant,
				{ productId } = product.external,
				{ variantId } = variant.external

			productId && productIds.push(productId)
			variantId && variantIds.push(variantId)
			sku && skus.push(sku)
		}

		const productFilter = {
				where: {
					'external.provider': SHOPIFY,
					or: [ { 'external.sku': { inq: skus } }, { 'external.productId': { inq: productIds } } ]
				}
			},
			variantFilter = {
				where: {
					or: [
						{ sku: { inq: skus } },
						{ 'external.provider': SHOPIFY, 'external.variantId': { inq: variantIds } }
					]
				}
			},
			[ products, variants ] = await Promise.all([
				Product.find(productFilter),
				Variant.find(variantFilter)
			])

		for (const item of items) {
			const { productId } = item.product.external,
				{ variantId } = item.variant.external,
				{ sku } = item.variant,
				product = products.find(p => p.external?.productId === productId || p.external?.sku === sku),
				variant = variants.find(v => v.external?.variantId === variantId || v.sku === sku)

			if (product) item.product = product
			if (variant) item.variant = variant
		}
	}

	// Shopify.provider = async function (shop) {
	// 	const api = await this.getProvider(SHOPIFY, undefined, shop)
	// 	if (!api) return null

	// 	const { enabled, apis } = getModule(api) || {},		// FIXME
	// 		{ disabled: apiDisabled } = apis || {}

	// 	return (enabled && !apiDisabled) ? api : null
	// }

	//  ----  Private Methods  ---

	function shopOfOrder(crmOrder = {}) {
		const { acquired = {} } = crmOrder,
			{ attributedTo = {} } = acquired,
			{ name } = attributedTo

		return name
	}

	/**
	 * @param	{Object} options
	 * 			{Boolean} markPaid - coerce status to paid
	 * 			{Boolean} linkToMerchant - associate order with business/merchant (marketplace) instead of person when true
	 */
	async function buildAndCreate(api, crmOrder, crmFulfillments, options) {
		const _start = Date.now(),
			{ Metric } = Shopify.app,
			{ id } = crmOrder,
			tags = { id }

		try {
			const data = await Shopify.buildOrder(api, crmOrder, crmFulfillments, options),
				order = await api.orders.create(data),
				{ id: orderId } = order

			const fulfillmentOrderId = await api.fulfillmentOrders.getId(orderId)
				.catch(err => {
					appNotify('shopify_retrieve_fulfillment_fail', { orderId: id, err })
				})

			if (fulfillmentOrderId) {
				order.fulfillmentOrderId = fulfillmentOrderId
			}

			appMetric(Metric.order.create, 1, { tags })
			appMetric(Metric.order.latency.create, Date.now() - _start, { tags })

			return order
		}
		catch (error) {
			appNotify('shopify_orders_create_fail', { err: error, order: crmOrder })
			appMetric(Metric.order.error.create, 1, { tags, error })
		}
	}
}