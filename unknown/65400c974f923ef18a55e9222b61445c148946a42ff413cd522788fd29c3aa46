/**
 *  @module Mixin:Receipt - digital receipt
 * 	Note on Message Template for receipt (created in Content service):
 *	- content: { default: { type: RICH, options: { kind: RECEIPT, ttl: RECEIPT_TTL } } }
 *	- RICH = 'rich', RECEIPT = 'receipt', RECEIPT_TTL = 31536000 (1 year, default)
 */
const { Providers, Fulfillments, Payments, Settings, Receipts, Notify } = require('@crm/types'),
	{ buildOrderStatus, buildFulfillmentStatus } = require('@perkd/fulfillments'),
	{ REGISTRY } = require('@perkd/event-registry-crm')

const { STORE, PICKUP, DELIVER, DINEIN, DIGITAL, VENDING_MACHINE } = Fulfillments.Type,
	{ PAID } = Payments.Status,
	{ PERKD } = Providers.PROVIDER,
	{ FULFILLMENT, ORDER } = Settings.Name,
	{ ORDER_PAID, ORDER_FULFILLED, ORDER_CANCELLED } = Receipts.Event,
	{ FULFILL_CREATED, FU<PERSON>ILL_CREATED_PAID, FULFILL_REQUESTED, FULFILL_PREPARE, FULFILL_PACKED } = Receipts.Event,
	{ Prefix } = Notify.Templates,
	{ RECEIPT: PREFIX } = Prefix,
	SKIP_NOTIFY = {
		[STORE]: [ ORDER_PAID, FULFILL_CREATED_PAID ],
		[PICKUP]: [ ORDER_PAID, FULFILL_CREATED_PAID ],
		[DELIVER]: [ ORDER_PAID, FULFILL_PACKED ],
		[DINEIN]: [ ORDER_PAID, FULFILL_CREATED_PAID, FULFILL_PREPARE, FULFILL_PACKED ],
		[VENDING_MACHINE]: [ ORDER_PAID, ORDER_FULFILLED, ORDER_CANCELLED ],
		[DIGITAL]: [ ORDER_PAID, ORDER_FULFILLED, ORDER_CANCELLED ]
	},
	{ sales: Event } = REGISTRY,
	RECEIPT_EVENT = {
		// order
		[Event.order.paid]: ORDER_PAID,
		[Event.order.fulfilled]: ORDER_FULFILLED,
		[Event.order.cancelled]: ORDER_CANCELLED,
		// fulfillment
		[Event.fulfillment.created]: FULFILL_CREATED,
		// requested
		[Event.fulfillment.store.requested]: FULFILL_REQUESTED,
		[Event.fulfillment.pickup.requested]: FULFILL_REQUESTED,
		[Event.fulfillment.deliver.requested]: FULFILL_REQUESTED,
		[Event.fulfillment.dinein.requested]: FULFILL_REQUESTED,
		// prepare
		[Event.fulfillment.store.prepare]: FULFILL_PREPARE,
		[Event.fulfillment.pickup.prepare]: FULFILL_PREPARE,
		[Event.fulfillment.deliver.prepare]: FULFILL_PREPARE,
		[Event.fulfillment.dinein.prepare]: FULFILL_PREPARE,
		// packed
		[Event.fulfillment.store.packed]: FULFILL_PACKED,
		[Event.fulfillment.pickup.packed]: FULFILL_PACKED,
		[Event.fulfillment.deliver.packed]: FULFILL_PACKED,
		[Event.fulfillment.dinein.packed]: FULFILL_PACKED
	},
	Q_RECEIPT = 'receipt'

module.exports = function(Order) {

	Order.handleReceiptOrderEvents = async function(event) {
		const { name, data = {} } = event,
			{ id, receipt = {} } = data,
			receiptEvent = RECEIPT_EVENT[name]

		if (!receiptEvent || !receipt.send) return undefined

		return Order.queue(`${Q_RECEIPT}:${id}`, async () => {
			try {
				const [ order, fulfillment ] = await Promise.all([
					Order.findById(id),
					Order.findMainFulfillment(id)		// do not exist for DIGITAL orders, membership/topup
				])

				await Order.sendOrUpdateReceipt(receiptEvent, order, fulfillment?.toJSON())
			}
			catch (err) {
				appNotify('[handleReceiptOrderEvents]', { err, name, data }, 'error')
			}
		})
	}

	Order.handleReceiptFulfillEvents = async function(event) {
		const { app } = Order,
			{ name, data: fulfillment = {} } = event,
			{ type, orderId } = fulfillment,
			trackProgress = [ STORE, PICKUP, DELIVER, DINEIN ].includes(type),		// ignore kitchen & others
			receiptEvent = RECEIPT_EVENT[name]

		if (!trackProgress || !receiptEvent) return undefined

		return Order.queue(`${Q_RECEIPT}:${orderId}`, async () => {
			try {
				const order = await Order.findById(orderId),
					{ receipt = {}, when = {} } = order,
					{ send } = receipt,
					paid = !!when[PAID]

				if (!send) return

				if (receiptEvent === FULFILL_CREATED) {
					const { kitchen = {} } = app.getSettings(FULFILLMENT),
						evt = paid ? FULFILL_CREATED_PAID : FULFILL_CREATED

					if (!(paid && kitchen.enabled)) {
						await Order.sendOrUpdateReceipt(evt, order, fulfillment)
					}
				}
				else {
					await Order.sendOrUpdateReceipt(receiptEvent, order, fulfillment)
				}
			}
			catch (err) {
				appNotify('[handleReceiptFulfillEvents]', { err, name, data: fulfillment }, 'error')
			}
		})
	}

	/**
	 * Send or Update Receipt from order + fulfillment
	 * @param	{String} receiptEvent
	 * @param	{Order} order instance
	 * @param	{Object} [fulfillment] not present for digital orders
	 */
	Order.sendOrUpdateReceipt = async function(receiptEvent, order, fulfillment) {
		const { app } = Order,
			{ Perkd } = appModule('perkd'),
			{ receiptTemplateId } = app.getSettings(ORDER),
			data = order.toJSON(),
			{ external = {} } = data,
			{ type = DIGITAL } = fulfillment ?? {},
			perkd = external[PERKD] || {},
			{ messageId, cardId } = perkd,
			template = `${PREFIX}:${type}:${receiptEvent}`,
			SKIP = SKIP_NOTIFY[type] || [],
			skipNotify = SKIP.includes(receiptEvent),
			options = {
				fetch: true,
				badge: 1,
				dialog: false,
				banner: true,
				noPreview: true
			},
			personalize = {},
			localStates = {}

		// TODO: Receipt message compatibility, to remove after WX migrate all receipts
		// localStates.order = buildOrderStatus(data)
		const formatted = buildOrderStatus(data),
			{ itemList, discountAmount: totalDiscount, taxAmount: totalTax, amount: totalPrice, ...rest } = formatted,
			{ newTemplate } = app.getSettings(ORDER),
			items = itemList.map(item => {
				const { price, ...restFields } = item
				return { amount: price, ...restFields }
			})

		localStates.order = newTemplate
			? formatted
			: { ...rest, items, totalDiscount, totalTax, totalPrice }

		if (fulfillment) {
			const { receipt = {}, scheduled = {},  destination = {} } = fulfillment,
				{ queue = '' } = receipt,
				{ name: storeName } = destination

			personalize.queue = queue
			personalize.storeName = storeName

			if (fulfillment.minTime || scheduled.minTime) {
				const { firstEta = scheduled.minTime } = scheduled,
					minTime = new Date(fulfillment.minTime),
					scheduledTime = new Date(scheduled.minTime),
					at = (type === DINEIN)
						? new Date(firstEta)
						: minTime > scheduledTime ? minTime : scheduledTime,
					eta = Math.round((at.getTime() - Date.now()) / (60 * 1000))		// minutes

				personalize.scheduledAt = at
				personalize.eta = eta

				appNotify('[Receipt] scheduledAt', { receiptEvent, minTime, scheduledTime, at, eta })
			}

			localStates.fulfillment = buildFulfillmentStatus(fulfillment)
		}

		if (messageId) {	// update existing message
			await Perkd.messages.localStates(messageId, localStates, { silentPush: skipNotify })

			if (!skipNotify) {
				Order.messageNotify(messageId, template, personalize, options)
			}
		}
		else {		// send new message
			// TODO: use return Messaging.sendRich(templateId, { cardId }) for rich.sent event (ie. billing, metrics etc.)
			// TODO: sent message deleted with expired card?

			const content = [ { to: cardId, localStates } ],
				opts = { banner: false },
				res = await Perkd.messages.send(receiptTemplateId, content, opts).catch(err => {
					appNotify('[Receipt] send error', { receiptTemplateId, receiptEvent, err }, 'error')
				}),
				[ messageId ] = res || []

			perkd.messageId = messageId
			external[PERKD] = perkd

			if (!skipNotify) {
				Order.messageNotify(messageId, template, personalize, options)
			}
			await order.updateAttributes({ external })
		}
	}
}
