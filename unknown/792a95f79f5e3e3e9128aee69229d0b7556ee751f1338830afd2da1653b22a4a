/**
 *  @module Mixin:Membership - used by OrderProviders for post-order recruitment
 */
const { Persons, Settings } = require('@crm/types'),
	{ parsePhoneNumber } = require('@perkd/utils')

const { CUSTOMER } = Persons.Identities,
	{ ORDER } = Settings.Name,
	NO_NAME = 'Unregistered'

module.exports = function(OrderProvider) {

	/**
	 * @param	{String} provider name
	 * @param	{Object} customer - { id, givenName, familyName, phone: {countryCode, number}, email, address: {countryCode, number, ...} }
	 * @param	{Object[]} phones - list of { countryCode, number }
	 * @param	{Object} options
	 * 			{String} orderId - for error reporting
	 * 			{Object} through
	 * 			{Boolean} noInvite
	 * 			{Boolean} nonMemberFirstSale
	 */
	OrderProvider.joinMembership = async function(provider, customer = {}, phones = [], options = {}) {
		const { id, givenName, familyName, email, createdAt = new Date() } = customer,
			{ through } = options,
			mobile = phones.reduce((res, { countryCode: country, number }) => {
				if (!res && number) {
					const { isMobile, valid, fullNumber } = parsePhoneNumber(number, country)
					if (isMobile && valid) res = fullNumber
				}
				return res
			}, '')

		if (!mobile) return {}

		const NOW = new Date(),
			profile = { mobile, email, familyName, givenName },
			acquired = {
				...through,
				touchedAt: createdAt
			},
			identity = {
				type: CUSTOMER,
				provider,
				externalId: String(id || mobile), // grab does not have id for customer, fallback to mobile
				createdAt: NOW,
				modifiedAt: NOW
			}

		if (givenName === NO_NAME) {
			delete profile.givenName
			delete profile.familyName
		}

		return OrderProvider.inviteMember(profile, { through: acquired, identity })
	}

	/**
	 * Invite new Member using customer profile into lowest Tier of a Program
	 * @param	{Object} profile { givenName, familyName, mobile, email }
	 * @param	{Object} options
	 * 				{Object} through { type, format, attributedTo, touchedAt }
	 * 				{Object} identity { provider, type, externalId }
	 * 				{String[]} mandatory
	 * @return	{Promise<Object>} { person, member, membership }
	 */
	OrderProvider.inviteMember = async function(profile, options) {
		const { Program } = this.app.models,
			program = await Program.findOneActiveFreeOrPaid()

		if (program) {
			const { tierList = [] } = program,
				{ level: tierLevel } = tierList[0]

			return program.invite(profile, tierLevel, options)
		}
	}

	/**
	 * Find or Join Membership based on cardNo or customer details
	 * @param	{String} provider name
	 * @param	{String} cardNo
	 * @param	{Object} customer
	 * 			{String} id - provider's customer id
	 * 			{String} givenName
	 * 			{String} familyName
	 * 			{String} email
	 * 			{Object} phone { countryCode, number }
	 * 			{Object} address { countryCode, number, ... }
	 * @param	{Object[]} phones - list of { countryCode, number }
	 * @param	{Object} options
	 * 			{String} orderId - for error reporting
	 * 			{Object} through
	 * 			{Boolean} noInvite
	 * 			{Boolean} nonMemberFirstSale
	 * @return	{Promise<Object>} { person: {}, membership: {} }
	 */
	OrderProvider.findOrJoinMembership = async function(provider, cardNo, customer, phones, options = {}) {
		const { app } = this,
			{ Person, Membership } = app.models,
			{ noInvite: noInv } = app.getSettings(ORDER),
			{ orderId, nonMemberFirstSale, noInvite = noInv } = options,
			result = {}

		// 1. non-member sales
		if (!cardNo && !customer) return result

		// 2. match with cardNo: epitex and zhanlu orders are stored as non-member sales in shopify, but attached cardNo
		if (cardNo) {
			const { personId, membership } = await Membership.findActiveAndPersonIdByCardNumber(cardNo) || {}

			if (personId) result.person = { id: personId }
			if (membership) result.membership = membership
			return result
		}

		// 3. then match with customer info
		const { id } = customer,
			person = id ? await Person.findOneByIdentity(provider, CUSTOMER, id) : undefined,
			membership = person ? await Membership.findActiveByPersonId(person.id) : undefined

		if (person) result.person = person
		if (membership) result.membership = membership

		// 4. invite to join if no matched membership
		if (!noInvite && (!person || !membership)) {
			const { person: newPerson, membership: newMembership } = await this.joinMembership(provider, customer, phones, options)
				.catch(err => {
					appLog('[OrderProvider]acquire_member_fail', { err, orderId })
					return {}
				})

			if (newPerson) result.person = newPerson
			if (!nonMemberFirstSale && newMembership) result.membership = newMembership
		}

		return result
	}
}
