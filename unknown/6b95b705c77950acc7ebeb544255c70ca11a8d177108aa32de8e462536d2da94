/**
 *  @module Mixin:Payment
 */

const { Payments, Touchpoints } = require('@crm/types'),
	{ updateBillingWithTransaction } = require('@perkd/commerce')

const { PERKD } = Touchpoints.Type,
	{ PAID, REFUNDED, CANCELLED } = Payments.Status

module.exports = function(Order) {

	/**
	 * Cancel or Refund all Payments (acquired by Perkd)
	 * @param	{Object} options
	 *				{String} reason
	 *				{Date} at
	 * @return	{Object|void} { transactions: [], errors: [] } | void - if payment not handled by Perkd
	 */
	Order.prototype.cancelOrRefundPayments = async function(options = {}) {
		const { id, currency, acquired, billingList, personId } = this,
			{ Business } = Order.app.models,
			{ reason } = options,
			result = { transactions: [], errors: [] }

		if (acquired.type !== PERKD) return

		for (const billing of billingList) {
			const { paymentMethod } = billing,
				{ status, method, amount, transactionId: referenceId, processor: provider, transactions = [] } = paymentMethod,
				[ first = {} ] = transactions,
				{ type, details = {} } = first,
				transaction = { id, type, amount, currency, referenceId, method, status, provider, personId, details }

			if (status === PAID) {
				try {
					const refunded = await Business.paymentRefund(transaction, reason)
					result.transactions.push(refunded)
					updateBillingWithTransaction(billingList, refunded)
				}
				catch (err) {
					result.errors.push({ method, referenceId, provider, reason: err.message })
				}
			}
			else if (![ CANCELLED, REFUNDED ].includes(status)) {
				try {
					const cancelled = await Business.paymentCancel(transaction, reason)
					result.transactions.push(cancelled)
					updateBillingWithTransaction(billingList, cancelled)
				}
				catch (err) {
					result.errors.push({ method, referenceId, provider, reason: err.message })
				}
			}
			else {
				result.errors.push({ method, referenceId, provider, reason: `Unable to reverse (status = '${status}')` })
			}
		}

		await this.updateAttributes({ billingList })
		return result
	}
}
