{"name": "ExtOrder", "base": "Model", "strict": true, "properties": {"orderId": {"type": "String", "description": "Unique order identifier in POS", "required": true}, "receiptNumber": {"type": "String", "description": "Receipt number for the purchase, printed on receipt given to customer.", "required": true}, "customerId": {"type": "String", "description": "Unique customer identifier in POS", "required": true}, "cardNumber": {"type": "String", "description": "Membership card number", "required": true}, "quantity": {"type": "Number", "description": "Total units purchased", "required": true}, "currency": {"type": "String", "description": "Currency (in ISO 4217 format) used in payment", "max": 3, "required": true}, "amount": {"type": "number", "description": "Total discounted, tax-inclusive price (positive number)", "required": true}, "store": {"type": {"id": {"type": "string", "description": "Unique store identifier in POS", "required": true}, "name": {"type": "string", "description": "Store name"}}, "description": "Store where this order created.", "required": true}, "staff": {"type": {"id": {"type": "string", "description": "Unique staff identifier in POS", "required": true}, "name": {"type": "string", "description": "Staff name"}, "description": "Staff who closed this order.", "required": true}}, "items": {"type": [{"type": {"productId": {"type": "string", "description": "Unique product identifier in POS", "required": true}, "variantId": {"type": "string", "description": "Unique variant identifier in POS", "required": true}, "productTitle": {"type": "string", "description": "Product name"}, "variantTitle": {"type": "string", "description": "Variant name, description displayed in checkout / receipt."}, "quantity": {"type": "number", "description": "Number of units purchased", "required": true}, "amount": {"type": "number", "description": "Discounted, tax exclusive item price (positive number)", "required": true}, "gtin": {"type": "string", "description": "Barcode value of product"}, "sku": {"type": "string", "description": "Unique product identifier for stock keeping"}, "discount": {"name": {"type": "string", "description": "Discount name", "required": true}, "kind": {"type": "string", "description": "Acceptable values: fixed, percentage, gift, shipping", "required": true}, "value": {"type": "number", "description": "Amount for fixed value / discount rate for percentage"}, "code": {"type": "string", "description": "Discount / promotion code, required for gift & shipping, others are optional"}}, "discountAmount": {"type": "number", "description": "Total discounts applied (positive number)"}, "grossMargin": {"type": "number", "description": "Percentage of gross profit divided by revenue"}}}], "description": "List of Item objects", "required": true}, "discounts": {"type": [{"type": {"name": {"type": "string", "description": "Discount name", "required": true}, "kind": {"type": "string", "description": "Acceptable values: fixed, percentage, gift, shipping", "required": true}, "value": {"type": "number", "description": "Amount for fixed value / discount rate for percentage"}, "code": {"type": "string", "description": "Discount / promotion code, required for gift & shipping, others are optional"}}}], "description": "List of Discounts applied to this order"}, "payments": {"type": [{"type": {"method": {"type": "string", "description": "Acceptable values: creditcard, cash, storedvalue, offer", "required": true}, "currency": {"type": "string", "description": "Currency (in ISO 4217 format) used in payment", "required": true}, "amount": {"type": "number", "description": "Total amount charged", "required": true}}}], "description": "List of Payment objects"}, "purchaseAt": {"type": "Date", "description": "Transaction time (in ISO 8601 format)", "required": true}, "cost": {"type": "number", "description": "Total cost (positive number)"}, "discountAmount": {"type": "number", "description": "Total discounts applied (positive number)"}, "taxAmount": {"type": "number", "description": "Total tax applied (positive number)"}, "note": {"type": "string", "description": "Additional details of order"}}}