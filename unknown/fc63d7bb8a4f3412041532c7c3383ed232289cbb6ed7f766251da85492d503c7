{"_id": "3b5D8GTXAYZydZXEn", "sessionId": "p4jKemsyLkEsmfB4E", "userId": "w4BSSEGECEhiRZkb6", "shopId": "J8Bhq3uTtdgwZx3rz", "workflow": {"status": "coreOrderWorkflow/completed", "workflow": ["coreOrderWorkflow/created", "coreOrderWorkflow/processing", "coreOrderWorkflow/completed"]}, "billing": [{"paymentMethod": {"processor": "Stripe", "storedCard": "Visa 1111", "method": "credit", "paymentPackageId": "bzagzv82SaBSTAbFZ", "paymentSettingsKey": "reaction-stripe", "transactionId": "ch_1C47KHJt9hAC4AWfr8acZ91T", "amount": 73, "status": "completed", "mode": "capture", "riskLevel": "normal", "createdAt": "2018-03-10T13:01:23.327Z", "transactions": [{"id": "ch_1C47KHJt9hAC4AWfr8acZ91T", "object": "charge", "amount": 7300, "amount_refunded": 0, "application": null, "application_fee": null, "balance_transaction": null, "captured": false, "created": 1520686889, "currency": "sgd", "customer": "cus_CT3Rf4fGO8iMJz", "description": null, "destination": null, "dispute": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "receipt_email": "<EMAIL>", "receipt_number": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/ch_1C47KHJt9hAC4AWfr8acZ91T/refunds"}, "review": null, "shipping": null, "source": {"id": "card_1C47KGJt9hAC4AWf6PInzEUW", "object": "card", "address_city": null, "address_country": null, "address_line1": null, "address_line1_check": null, "address_line2": null, "address_state": null, "address_zip": null, "address_zip_check": null, "brand": "Visa", "country": "US", "customer": "cus_CT3Rf4fGO8iMJz", "cvc_check": "pass", "dynamic_last4": null, "exp_month": 5, "exp_year": 2018, "fingerprint": "L7lDdU53IJjWaGmT", "funding": "unknown", "last4": "1111", "metadata": {}, "name": "zl", "tokenization_method": null}, "source_transfer": null, "statement_descriptor": null, "status": "succeeded", "transfer_group": null}, {"saved": true, "response": {"id": "ch_1C47KHJt9hAC4AWfr8acZ91T", "object": "charge", "amount": 7300, "amount_refunded": 0, "application": null, "application_fee": null, "balance_transaction": "txn_1C48HFJt9hAC4AWfbyKs5qrI", "captured": true, "created": 1520686889, "currency": "sgd", "customer": "cus_CT3Rf4fGO8iMJz", "description": null, "destination": null, "dispute": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "receipt_email": "<EMAIL>", "receipt_number": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/ch_1C47KHJt9hAC4AWfr8acZ91T/refunds"}, "review": null, "shipping": null, "source": {"id": "card_1C47KGJt9hAC4AWf6PInzEUW", "object": "card", "address_city": null, "address_country": null, "address_line1": null, "address_line1_check": null, "address_line2": null, "address_state": null, "address_zip": null, "address_zip_check": null, "brand": "Visa", "country": "US", "customer": "cus_CT3Rf4fGO8iMJz", "cvc_check": "pass", "dynamic_last4": null, "exp_month": 5, "exp_year": 2018, "fingerprint": "L7lDdU53IJjWaGmT", "funding": "unknown", "last4": "1111", "metadata": {}, "name": "zl", "tokenization_method": null}, "source_transfer": null, "statement_descriptor": null, "status": "succeeded", "transfer_group": null}}], "items": [{"_id": "eKRr3vy78o7iWRwTo", "productId": "r8rSRuv7xvhqc2WCt", "variantId": "noLcCJjiyFNfTqqHL", "shopId": "J8Bhq3uTtdgwZx3rz", "quantity": 1}, {"_id": "AmSh7NQeaygFkYRpn", "productId": "4xaqk3qSaFnWPk2Ee", "variantId": "iNgmkz8rEqhHNFj4b", "shopId": "J8Bhq3uTtdgwZx3rz", "quantity": 1}], "shopId": "J8Bhq3uTtdgwZx3rz", "workflow": {"status": "new"}, "metadata": {}}, "invoice": {"shipping": 10, "subtotal": 70, "taxes": 0, "discounts": 7, "total": 73}, "address": {"country": "SG", "fullName": "zl", "address1": "247 ponggole", "postal": "00000", "city": "singapore", "region": "singapore", "phone": "12323455", "isShippingDefault": true, "isBillingDefault": true, "isCommercial": false, "_id": "iPtT5kYKcKWCKLCgu", "failedValidation": false}, "shopId": "J8Bhq3uTtdgwZx3rz", "_id": "4stY39CzP6jx2Ncjz", "currency": {"userCurrency": "SGD", "exchangeRate": 1}}], "discount": 0, "items": [{"_id": "eKRr3vy78o7iWRwTo", "shopId": "J8Bhq3uTtdgwZx3rz", "productId": "r8rSRuv7xvhqc2WCt", "quantity": 1, "product": {"_id": "r8rSRuv7xvhqc2WCt", "title": "The Body Shop", "shopId": "J8Bhq3uTtdgwZx3rz", "ancestors": [], "description": "Can't decide on a gift? Give them just what they want with a Digital Gift card :)", "hashtags": ["rpjCvTBGjhBi2xdro", "nRbgBHP68Z838ixjT", "89GJJKhEFbKHDupPR"], "price": {"range": "9 - 40", "min": 9, "max": 40}, "isVisible": true, "isLowQuantity": false, "isSoldOut": false, "isBackorder": false, "metafields": [], "pageTitle": "Gift Card", "type": "simple", "vendor": "The Body Shop Singapore", "originCountry": "SG", "requiresShipping": true, "isDeleted": false, "template": "productDetailSimple", "workflow": {"status": "new"}, "handle": "the-body-shop-sg", "createdAt": "2018-03-10T13:01:23.311Z", "updatedAt": "2018-03-10T14:02:25.869Z"}, "variants": {"_id": "noLcCJjiyFNfTqqHL", "title": "$50 Gift Card", "ancestors": ["r8rSRuv7xvhqc2WCt"], "price": 40, "inventoryManagement": false, "inventoryPolicy": false, "inventoryQuantity": 99, "isVisible": true, "weight": 0, "metafields": [{"key": null, "value": null}], "shopId": "J8Bhq3uTtdgwZx3rz", "taxable": true, "type": "variant", "isDeleted": false, "compareAtPrice": 50, "length": 0, "width": 0, "height": 0, "lowInventoryWarningThreshold": 0, "taxCode": "RC_TAX", "optionTitle": "Untitled Option", "originCountry": "SG", "workflow": {"status": "new"}, "index": 0, "taxDescription": "GST"}, "title": "The Body Shop", "type": "simple", "parcel": null, "taxRate": 0, "shippingMethod": {"shopId": "J8Bhq3uTtdgwZx3rz", "address": {"country": "SG", "fullName": "zl", "address1": "247 ponggole", "postal": "00000", "city": "singapore", "region": "singapore", "phone": "12323455", "isShippingDefault": true, "isBillingDefault": true, "isCommercial": false, "_id": "iPtT5kYKcKWCKLCgu", "failedValidation": false}, "_id": "r5gzfFLdeYbzRhBcr", "shipmentQuotesQueryStatus": {"requestStatus": "success", "numOfShippingMethodsFound": 3}, "shipmentQuotes": [{"carrier": "Flat Rate", "method": {"name": "In-store Pick Up", "label": "In-store Pick Up", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "ijrjoGHhKQpzuBeJX", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Issue in-app", "label": "Issue in-app", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "jLwtKaknbLxQkuHad", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "rate": 10, "shopId": "J8Bhq3uTtdgwZx3rz"}], "shipmentMethod": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "paymentId": "4stY39CzP6jx2Ncjz", "items": [{"_id": "eKRr3vy78o7iWRwTo", "productId": "r8rSRuv7xvhqc2WCt", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "noLcCJjiyFNfTqqHL"}, {"_id": "AmSh7NQeaygFkYRpn", "productId": "4xaqk3qSaFnWPk2Ee", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "iNgmkz8rEqhHNFj4b"}], "workflow": {"status": "new", "workflow": ["coreOrderWorkflow/notStarted"]}}, "workflow": {"status": "coreOrderItemWorkflow/completed", "workflow": ["coreOrderWorkflow/created", "coreOrderItemWorkflow/packed", "coreOrderItemWorkflow/shipped", "coreOrderItemWorkflow/completed"]}}, {"_id": "AmSh7NQeaygFkYRpn", "shopId": "J8Bhq3uTtdgwZx3rz", "productId": "4xaqk3qSaFnWPk2Ee", "quantity": 1, "product": {"_id": "4xaqk3qSaFnWPk2Ee", "title": "National Gallery Singapore", "shopId": "J8Bhq3uTtdgwZx3rz", "ancestors": [], "description": "Concession fare is applicable to students, children 7-12 years old, and seniors 60 years and above. Identification is required upon check-out.", "hashtags": ["cseCBSSrJ3t8HQSNP", "7ymGqLxr8kXAc8uXJ", "7aWYnst3hBRJFtkpi", "eqrDxrPrchAFdSHRc", "rpjCvTBGjhBi2xdro"], "price": {"range": "25 - 150", "min": 25, "max": 150}, "isVisible": true, "isLowQuantity": false, "isSoldOut": false, "isBackorder": false, "metafields": [], "pageTitle": "Join membership to enjoy special privileges", "type": "simple", "vendor": "National Gallery Singapore", "originCountry": "SG", "requiresShipping": true, "isDeleted": false, "template": "productDetailSimple", "workflow": {"status": "new"}, "handle": "national-gallery-singapore", "createdAt": "2018-03-10T13:01:23.312Z", "updatedAt": "2018-03-10T14:02:25.870Z"}, "variants": {"_id": "iNgmkz8rEqhHNFj4b", "title": "Indvidual Adult - Singaporeans & PRs", "ancestors": ["4xaqk3qSaFnWPk2Ee", "z6CKXm2duTJXYcpNB"], "optionTitle": "Adult Singaporeans & PRs", "price": 30, "inventoryManagement": true, "inventoryPolicy": true, "inventoryQuantity": 1000, "isVisible": true, "weight": 0, "length": 0, "height": 0, "width": 0, "metafields": [{"key": null, "value": null}], "shopId": "J8Bhq3uTtdgwZx3rz", "taxable": true, "type": "variant", "isDeleted": false, "compareAtPrice": 50, "lowInventoryWarningThreshold": 0, "taxCode": "0000", "originCountry": "US", "workflow": {"status": "new"}}, "title": "National Gallery Singapore", "type": "simple", "parcel": null, "taxRate": 0, "shippingMethod": {"shopId": "J8Bhq3uTtdgwZx3rz", "address": {"country": "SG", "fullName": "zl", "address1": "247 ponggole", "postal": "00000", "city": "singapore", "region": "singapore", "phone": "12323455", "isShippingDefault": true, "isBillingDefault": true, "isCommercial": false, "_id": "iPtT5kYKcKWCKLCgu", "failedValidation": false}, "_id": "r5gzfFLdeYbzRhBcr", "shipmentQuotesQueryStatus": {"requestStatus": "success", "numOfShippingMethodsFound": 3}, "shipmentQuotes": [{"carrier": "Flat Rate", "method": {"name": "In-store Pick Up", "label": "In-store Pick Up", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "ijrjoGHhKQpzuBeJX", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Issue in-app", "label": "Issue in-app", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "jLwtKaknbLxQkuHad", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "rate": 10, "shopId": "J8Bhq3uTtdgwZx3rz"}], "shipmentMethod": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "paymentId": "4stY39CzP6jx2Ncjz", "items": [{"_id": "eKRr3vy78o7iWRwTo", "productId": "r8rSRuv7xvhqc2WCt", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "noLcCJjiyFNfTqqHL"}, {"_id": "AmSh7NQeaygFkYRpn", "productId": "4xaqk3qSaFnWPk2Ee", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "iNgmkz8rEqhHNFj4b"}], "workflow": {"status": "new", "workflow": ["coreOrderWorkflow/notStarted"]}}, "workflow": {"status": "coreOrderItemWorkflow/completed", "workflow": ["coreOrderWorkflow/created", "coreOrderItemWorkflow/packed", "coreOrderItemWorkflow/shipped", "coreOrderItemWorkflow/completed"]}}], "tax": 0, "taxRatesByShop": {}, "shipping": [{"shopId": "J8Bhq3uTtdgwZx3rz", "address": {"country": "SG", "fullName": "zl", "address1": "247 ponggole", "postal": "00000", "city": "singapore", "region": "singapore", "phone": "12323455", "isShippingDefault": true, "isBillingDefault": true, "isCommercial": false, "_id": "iPtT5kYKcKWCKLCgu", "failedValidation": false}, "_id": "r5gzfFLdeYbzRhBcr", "shipmentQuotesQueryStatus": {"requestStatus": "success", "numOfShippingMethodsFound": 3}, "shipmentQuotes": [{"carrier": "Flat Rate", "method": {"name": "In-store Pick Up", "label": "In-store Pick Up", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "ijrjoGHhKQpzuBeJX", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Issue in-app", "label": "Issue in-app", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "_id": "jLwtKaknbLxQkuHad", "carrier": "Flat Rate"}, "rate": 0, "shopId": "J8Bhq3uTtdgwZx3rz"}, {"carrier": "Flat Rate", "method": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "rate": 10, "shopId": "J8Bhq3uTtdgwZx3rz"}], "shipmentMethod": {"name": "Normal Delivery", "label": "Normal Delivery", "group": "Ground", "cost": 6, "handling": 2, "rate": 8, "enabled": true, "_id": "5i9FFT2XZLnGyWzTf", "carrier": "Flat Rate"}, "paymentId": "4stY39CzP6jx2Ncjz", "items": [{"_id": "eKRr3vy78o7iWRwTo", "productId": "r8rSRuv7xvhqc2WCt", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "noLcCJjiyFNfTqqHL"}, {"_id": "AmSh7NQeaygFkYRpn", "productId": "4xaqk3qSaFnWPk2Ee", "shopId": "J8Bhq3uTtdgwZx3rz", "variantId": "iNgmkz8rEqhHNFj4b"}], "workflow": {"status": "coreOrderWorkflow/shipped", "workflow": ["coreOrderWorkflow/notStarted", "coreOrderWorkflow/packed", "coreOrderWorkflow/shipped"]}}], "email": "<EMAIL>", "cartId": "BcQiAEcYb5LzKB4zC", "createdAt": "2018-03-10T13:01:23.301Z", "updatedAt": "2018-03-10T14:02:27.086Z"}