function getEmbeds({ relations }) {
	const embedsRelations = {};
	Object.keys(relations).forEach(r => {
		const { embed } = relations[r];
		if (embed && !embedsRelations[r]) embedsRelations[r] = relations[r];
	});
	return embedsRelations;
}
exports.getEmbeds = getEmbeds;

function setupEmbed({ prototype }, relation) {
	const { type, name, keyFrom } = relation,
		create = prototype[`__create__${name}`],
		destroy = prototype[`__destroy__${name}`],
		update = prototype[`__update__${name}`],
		findById = prototype[`__findById__${name}`],
		updateById = prototype[`__updateById__${name}`],
		destroyById = prototype[`__destroyById__${name}`];

	prototype[`__create__${name}`] = function(data, options, cb) {
		if (typeof options === 'function' && cb === undefined) { cb = undefined; options = {}; }

		return new Promise((resolve, reject) => {
			if (this.__embeddedCurrentInstance === undefined) this.__embeddedCurrentInstance = {};
			this.__embeddedCurrentInstance[keyFrom] = {};

			create.call(this, data, options, (err, created) => err ? reject(err) : resolve(created));
		});
	};

	if (type === 'embedsOne') {
		prototype[`__update__${name}`] = function(data, options, cb) {
			if (typeof options === 'function' && cb === undefined) { cb = undefined; options = {}; }

			return new Promise((resolve, reject) => {
				const found = this[keyFrom];
				if (found === null) resolve(null);
				else {
					if (this.__embeddedCurrentInstance === undefined) this.__embeddedCurrentInstance = {};
					this.__embeddedCurrentInstance[keyFrom] = found.toJSON();

					update.call(this, data, options, (err, updated) => err ? reject(err) : resolve(updated));
				}
			});
		};

		prototype[`__destroy__${name}`] = function(options, cb) {
			if (typeof options === 'function' && cb === undefined) { cb = undefined; options = {}; }

			return new Promise((resolve, reject) => {
				const found = this[keyFrom];
				if (found === null) resolve(null);
				else {
					if (this.__embeddedCurrentInstance === undefined) this.__embeddedCurrentInstance = {};
					this.__embeddedCurrentInstance[keyFrom] = found.toJSON();

					destroy.call(this, options, (err, destroyed) => err ? reject(err) : resolve(destroyed));
				}
			});
		};
	} else if (type === 'embedsMany') {
		prototype[`__updateById__${name}`] = function(fk, data, options, cb) {
			return new Promise((resolve, reject) => {
				if (typeof options === 'function' && cb === undefined) { cb = undefined; options = {}; }

				findById.call(this, fk, options, (err, found) => {
					if (err) reject(err);
					if (found === null) resolve(null);
					else {
						if (this.__embeddedCurrentInstance === undefined) this.__embeddedCurrentInstance = {};
						this.__embeddedCurrentInstance[keyFrom] = found.toJSON();

						updateById.call(this, fk, data, options, (err, updated) => err ? reject(err) : resolve(updated));
					}
				});
			});
		};

		prototype[`__destroyById__${name}`] = function(fk, options, cb) {
			return new Promise((resolve, reject) => {
				if (typeof options === 'function' && cb === undefined) { cb = undefined; options = {}; }

				findById.call(this, fk, options, (err, found) => {
					if (err) reject(err);
					if (found === null) resolve(null);
					else {
						if (this.__embeddedCurrentInstance === undefined) this.__embeddedCurrentInstance = {};
						this.__embeddedCurrentInstance[keyFrom] = found.toJSON();

						destroyById.call(this, fk, options, (err, destroyed) => err ? reject(err) : resolve(destroyed));
					}
				});
			});
		};
	}
}

function setupEmbeds(Model) {
	const embedsRelations = getEmbeds(Model);
	Object.values(embedsRelations).forEach(r => setupEmbed(Model, r));
}
exports.setupEmbeds = setupEmbeds;
