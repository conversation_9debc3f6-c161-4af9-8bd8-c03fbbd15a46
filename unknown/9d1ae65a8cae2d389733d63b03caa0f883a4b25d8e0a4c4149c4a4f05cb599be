/**
 *  @module Model:OrderApi	used by Collect, Orders & Payment widgets (Staff)
 */
const { Context } = require('@perkd/multitenant-context'),
	{ Settings, Fulfillments, Payments, Providers, Touchpoints } = require('@crm/types'),
	{ openFrom, openUntil } = require('@perkd/utils'),
	{ throughStaff } = require('@perkd/touchpoints'),
	{ manualBilling } = require('@provider/providers')

const { PICKUP, STORE, DELIVER, DINEIN } = Fulfillments.Type,
	{ MANUALS } = Payments,
	{ MANUAL, VOUCHER } = Payments.Method,
	{ PERKD } = Providers.PROVIDER,
	{ PERKDPAY } = Providers.Payment,
	{ LOCALE } = Settings.Name,
	{ STAFF_CARD } = Touchpoints.Format,
	LIST_FIELDS = [ 'id', 'receipt', 'currency', 'amount', 'taxes', 'itemList', 'discountList', 'billingList', 'when', 'note', 'orderId', 'createdAt', 'modifiedAt' ],
	FULFILLMENT_FIELDS = [ 'id', 'type', 'minTime', 'maxTime', 'prepare', 'status', 'recipient', 'receipt', 'destination', 'note', 'mainFulfillmentType', 'when' ]

module.exports = function(Order) {

	/**
	 * Get PACKED list pickup orders for Store
	 */
	Order.getPackedList = async function() {
		const { id: placeId } = getSpot()

		return Order.packedList(placeId)
	}

	/**
	 * Set Order as COLLECTED
	 * @param	{String} id
	 */
	Order.setCollected = async function(id) {
		const { user } = Context,
			{ staffId } = user,
			order = await Order.findById(id)

		return order.collected(new Date(), undefined, staffId)
	}

	/**
	 * Get Require ATTENTION list of Orders for Store
	 */
	Order.getAttentionList = async function() {
		const { id: placeId } = getSpot()

		return Order.attentionList(placeId)
	}

	/**
	 * Get OPEN Orders BEFORE Prepare for Store
	 */
	Order.getOpenList = async function() {
		const { id: placeId } = getSpot()

		return Order.openBeforePrepareList(placeId)
	}

	/**
	 * Get UNPAID orders for Store (MANUAL only)
	 */
	Order.getUnpaidList = async function() {
		const { id: storeId } = getSpot(),
			twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000),
			filter = {
				where: {
					storeId,
					'when.paid': null,				// unpaid
					'when.cancelled': null,			// not cancelled
					'acquired.type': PERKD,
					createdAt: { gte: twelveHoursAgo },
					'billingList.paymentMethod.method': MANUAL,
				},
				include: {
					relation: 'fulfillments',
					scope: {
						where: {
							type: { inq: [ PICKUP, STORE, DELIVER, DINEIN ] },
						},
						fields: FULFILLMENT_FIELDS
					}
				},
				fields: LIST_FIELDS,
				order: 'modifiedAt ASC',
			},
			orders = await Order.find(filter)

		return orders
	}

	/**
	 * Get PAID orders for Store (MANUAL only)
	 */
	Order.getPaidList = async function() {
		const { id: storeId } = getSpot(),
			twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000),
			filter = {
				where: {
					storeId,
					'when.paid': { ne: null },		// paid
					'when.cancelled': null,			// not cancelled
					'acquired.type': PERKD,
					createdAt: { gte: twelveHoursAgo },
					'billingList.paymentMethod.method': MANUAL,
				},
				include: {
					relation: 'fulfillments',
					scope: {
						where: {
							type: { inq: [ PICKUP, STORE, DELIVER, DINEIN ] },
						},
						fields: FULFILLMENT_FIELDS
					}
				},
				fields: LIST_FIELDS,
				order: 'modifiedAt ASC',
			},
			orders = await Order.find(filter)

		return orders
	}

	/**
	 * Mark Order as Paid
	 * @param {String} id
	 * @param {Number} [amount]
	 * @param {String} [currency]
	 * @param {String} [method]
	 */
	Order.setOrderAsPaid = async function(id, amount, currency, method) {
		const { Variant } = Order.app.models,
			{ user, staff, card, location } = Context.appContext,
			{ staffId } = user ?? {},
			source = STAFF_CARD,
			through = throughStaff(location, staff, card, source),
			order = await Order.findById(id),
			{ amount: amt, currency: code, billingList = [] } = order ?? {},
			pendingBilling = billingList.find(b => b.paymentMethod.method === MANUAL),
			{ transactionId } = pendingBilling?.paymentMethod ?? {},
			billing = manualBilling(amount || amt, currency || code, transactionId, method || MANUAL, PERKDPAY, { staffId }),
			{ transactions: trans } = billing.paymentMethod,
			[ transaction = {} ] = trans

		if (!order) throw { status: 404, message: 'Order not found' }
		if (method && !MANUALS.includes(method)) throw 'invalid method'

		if (!pendingBilling) {
			billingList.push(billing)
		}

		return Variant.orderCommit(transaction, { through })
	}

	/**
	 * All orders (with main fulfillment) for Store (today only)
	 * @param	{String[]} [types] of fulfillment (omit for all types)
	 * @return	{Fulfillment[]}
	 */
	Order.getAllList = async function(types) {
		const { app } = Order,
			{ Place } = app.models,
			{ id: placeId } = getSpot(),
			{ timeZone } = app.getSettings(LOCALE),
			{ openingHours } = await Place.findById(placeId) ?? {},
			now = new Date(),
			yesterday = new Date(now),
			todayOpen = openFrom(openingHours, now, timeZone)

		yesterday.setDate(now.getDate() - 1)

		const isOpen = now >= todayOpen,
			day = isOpen ? now : yesterday,
			from = isOpen ? todayOpen : openFrom(openingHours, day, timeZone),
			to = isOpen ? openUntil(openingHours, now, timeZone) : openUntil(openingHours, day, timeZone)

		return Order.allListForPeriod(placeId, types, from, to, FULFILLMENT_FIELDS)
	}

	/**
	 * Cancel Paid but Unfulfilled Order
	 */
	Order.cancelUnfulfilledOrder = async function(id, reason) {
		const { staff, card, location } = Context.appContext,
			source = STAFF_CARD,
			order = await Order.findById(id),
			{ when = {} } = order ?? {},
			{ fulfilled } = when,
			through = throughStaff(location, staff, card, source)

		if (!order) throw { status: 404, message: 'Order not found' }
		if (fulfilled !== null) throw { status: 403, message: 'Must be paid and unfulfilled' }

		return order.cancel({ reason, through })
	}

	// ----- Private Methods -----

	function getSpot() {
		const { location = {} } = Context,
			{ spot } = location,
			{ id = null, type, name } = spot || {}

		return { id, type, name }
	}

	// -----  Remote Methods  -----

	Order.remoteMethod('getPackedList', {
		description: 'Get Packed list for pickup at store  (StaffCard API)',
		http: { path: '/staff/orders/packed', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('setCollected', {
		description: 'Set (pickup) Order as COLLECTED  (StaffCard API)',
		http: { path: '/staff/orders/:id/collected', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Order id' },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('getAttentionList', {
		description: 'Get Orders requiring Attention at store  (StaffCard API)',
		http: { path: '/staff/orders/attention', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('getOpenList', {
		description: 'Get OPEN Orders BEFORE Prepare at store  (StaffCard API)',
		http: { path: '/staff/orders/open', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('getUnpaidList', {
		description: 'Get Unpaid Orders (StaffCard API)',
		http: { path: '/staff/orders/unpaid', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('getPaidList', {
		description: 'Get Paid Orders (StaffCard API)',
		http: { path: '/staff/orders/paid', verb: 'get' },
		accepts: [],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('getAllList', {
		description: 'All orders for Store, today only  (StaffCard API)',
		http: { path: '/staff/orders/all', verb: 'get' },
		accepts: [
			{ arg: 'types', type: [ 'string' ], description: 'Types of fulfillments to include, omit for all' },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('setOrderAsPaid', {
		description: 'Mark Order as Paid  (StaffCard API)',
		http: { path: '/staff/orders/:id/paid', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Order id' },
			{ arg: 'amount', type: 'number' },
			{ arg: 'currency', type: 'string' },
			{ arg: 'method', type: 'string', enum: MANUALS },
		],
		returns: { type: 'object', root: true },
	})

	Order.remoteMethod('cancelUnfulfilledOrder', {
		description: 'Cancel Paid, Unfulfilled Order  (StaffCard API)',
		http: { path: '/staff/orders/:id/cancel/unfulfilled', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', http: { source: 'path' }, required: true, description: 'Order id' },
			{ arg: 'reason', type: 'string' },
		],
		returns: { type: 'object', root: true },
	})
}
