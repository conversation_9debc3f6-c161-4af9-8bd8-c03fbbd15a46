{"shared": ["google"], "tenanted": {"shopify": {"Orders": {"enabled": true, "events": ["shopify.order.created", "shopify.order.paid", "shopify.order.cancelled", "shopify.order.deleted", "shopify.refund.created", "sales.order.created", "sales.order.updated", "sales.order.paid", "sales.order.fulfilled", "sales.order.cancelled"]}, "Fulfillments": {"enabled": true, "events": ["shopify.fulfillment.created", "shopify.fulfillment.updated"]}}, "lalamove": {"Fulfillments": {"enabled": true, "events": ["lalamove.fulfillment.accepted", "lalamove.fulfillment.allocated", "lalamove.fulfillment.collected", "lalamove.fulfillment.delivered", "lalamove.fulfillment.cancelled", "lalamove.fulfillment.failed"]}}, "grabfood": {"Orders": {"enabled": true, "events": ["grabfood.order.scheduled", "grabfood.order.request", "sales.order.fulfilled"]}, "Fulfillments": {"enabled": true, "events": ["grabfood.fulfillment.accepted", "grabfood.fulfillment.allocated", "grabfood.fulfillment.arrived", "grabfood.fulfillment.collected", "grabfood.fulfillment.delivered", "grabfood.fulfillment.cancelled", "grabfood.fulfillment.failed", "sales.fulfillment.packed.deliver"]}}, "grabmart": {"Orders": {"enabled": true, "events": ["grabmart.order.scheduled", "grabmart.order.request", "sales.order.fulfilled"]}, "Fulfillments": {"enabled": true, "events": ["grabmart.fulfillment.accepted", "grabmart.fulfillment.allocated", "grabmart.fulfillment.arrived", "grabmart.fulfillment.collected", "grabmart.fulfillment.delivered", "grabmart.fulfillment.cancelled", "grabmart.fulfillment.failed", "sales.fulfillment.packed.deliver"]}}, "ubereats": {"Orders": {"enabled": true, "events": ["ubereats.order.scheduled", "ubereats.order.request", "sales.order.fulfilled"]}, "Fulfillments": {"enabled": true, "events": ["ubereats.fulfillment.accepted", "ubereats.fulfillment.allocated", "ubereats.fulfillment.nearby", "ubereats.fulfillment.arrived", "ubereats.fulfillment.collected", "ubereats.fulfillment.delivered", "ubereats.fulfillment.cancelled", "ubereats.fulfillment.failed", "sales.fulfillment.packed.deliver"]}}, "machine": {"Fulfillments": {"enabled": true, "events": ["machine.fulfillment.collected", "machine.fulfillment.failed", "machine.reserve.success", "machine.reserve.failed"]}}, "mypay": {"Invoices": {"enabled": true}}, "nxiot": {"Orders": {"enabled": true, "events": ["nxiot.order.created"]}}}}