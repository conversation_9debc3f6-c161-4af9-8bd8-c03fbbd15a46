/**
 *  @module Model:FulfillmentProvider
 */

const { REGISTRY } = require('@perkd/event-registry-crm'),
	{ PROVIDER: PROVIDER_ERR } = require('@perkd/errors/dist/service')

const { sales } = REGISTRY,
	{ EVENT_NOT_HANDLED, HANDLER_NOT_IMPLEMENTED } = PROVIDER_ERR,
	// external events
	FULFILLMENT_CREATED = 'fulfillment.created',
	FULFILLMENT_UPDATED = 'fulfillment.updated',
	FULFILLMENT_ACCEPTED = 'fulfillment.accepted',
	FULFILLMENT_ALLOCATED = 'fulfillment.allocated',
	FULFILLMENT_ARRIVED = 'fulfillment.arrived',
	FULFILLMENT_COLLECTED = 'fulfillment.collected',
	FULFILLMENT_DELIVERED = 'fulfillment.delivered',
	FULFILLMENT_CANCELLED = 'fulfillment.cancelled',
	FULFILLMENT_FAILED = 'fulfillment.failed'

module.exports = function (FulfillmentProvider) {

	FulfillmentProvider.providerExternalEvents = async function(name, data = {}) {
		const { modelName: provider, app } = this,
			{ Fulfillment } = app.models,
			orderId = this.orderIdOf(data),
			fulfillment = await Fulfillment.findOneByProviderOrderId(provider, orderId)

		if (!fulfillment) {
			throw new Error(`[${provider}]fulfillment not found (id: ${orderId}) data: ${JSON.stringify(data)}`)
		}

		appNotify(`[${provider}]providerExternalEvents`, data)

		return this.queue(`providerExternalEvents_${provider}_${orderId}`, async () => {
			switch (name) {
			case FULFILLMENT_CREATED:
				return this.handleExtOrderFulfillmentCreated(fulfillment, data)

			case FULFILLMENT_UPDATED:
				return this.handleExtOrderFulfillmentUpdated(fulfillment, data)

			case FULFILLMENT_ACCEPTED:
				return this.handleExtOrderFulfillmentAccepted(fulfillment, data)

			case FULFILLMENT_ALLOCATED:
				return this.handleExtOrderFulfillmentAllocated(fulfillment, data)

			case FULFILLMENT_ARRIVED:
				appEmit(sales.fulfillment.deliver.arrived, fulfillment)
				break

			case FULFILLMENT_COLLECTED:
				return this.handleExtOrderFulfillmentCollected(fulfillment, data)

			case FULFILLMENT_DELIVERED:
				return this.handleExtOrderFulfillmentDelivered(fulfillment, data)

			case FULFILLMENT_CANCELLED:
				return this.handleExtOrderFulfillmentCancelled(fulfillment, data)

			case FULFILLMENT_FAILED:
				return this.handleExtOrderFulfillmentFailed(fulfillment, data)

			default:
				appNotify(EVENT_NOT_HANDLED, { provider, name, data }, 'error')
			}
		})
	}

	// ----  Order Provider common behaviors

	FulfillmentProvider.orderIdOf = function(data) {
		const { id = null } = data
		return id
	}

	// ----  Provider-specific methods  ----

	// external Fulfillment event handlers
	FulfillmentProvider.handleExtOrderFulfillmentCreated
	= FulfillmentProvider.handleExtOrderFulfillmentUpdated
	= FulfillmentProvider.handleExtOrderFulfillmentAccepted
	= FulfillmentProvider.handleExtOrderFulfillmentAllocated
	// = FulfillmentProvider.handleExtOrderFulfillmentArrived
	= FulfillmentProvider.handleExtOrderFulfillmentCollected
	= FulfillmentProvider.handleExtOrderFulfillmentDelivered
	= FulfillmentProvider.handleExtOrderFulfillmentCancelled
	= FulfillmentProvider.handleExtOrderFulfillmentFailed
	= function (fulfillment, extFulfillment) {
										return this.rejectErr(HANDLER_NOT_IMPLEMENTED)
									}
}
