{"name": "Order", "plural": "Orders", "base": "PersistedModel", "idInjection": true, "forceId": false, "strict": "filter", "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Common": true, "Timestamp": true, "FindOneAndUpdate": true, "MongoCollection": true, "Errors": true, "Find": true, "BatchUpload": true, "Payment": true, "Offer": true, "Queue": true, "Provider": true, "OrderFlow": true, "Pickup": true, "Fulfillment": true, "Receipt": true, "PrintReceipt": true, "CallbackApi": true, "OrderApi": true, "TableBooking": true, "StaffWidgetApi": true, "Notify": true, "DashboardAppApi": true, "MachineEvent": true, "StoredValueMetrics": true, "DisableAllRemotes": {"create": true, "upsert": false, "updateAll": false, "prototype.patchAttributes": true, "find": true, "findById": true, "findOne": true, "createChangeStream": false, "deleteById": true, "confirm": true, "count": true, "exists": true, "replaceById": false, "replaceOrCreate": false, "upsertWithWhere": false}}, "properties": {"receipt": {"type": {"number": {"type": "String", "max": 32, "description": "Generated or from POS/Gov. TW - Government Invoice number (統一發票號)"}, "taxId": {"type": "String", "max": 32, "description": "TW - individual: 載具號 or business: 統編"}, "merchantTaxId": {"type": "String", "max": 32, "description": "TW - 統編"}, "skipInvoice": {"type": "boolean", "description": "Skip creation of TW invoice"}, "invoice": {"type": "object", "description": "TW - invoice details"}, "send": {"type": "boolean", "description": "Send receipt via Message"}, "print": {"type": "boolean", "description": "Auto print receipt when receipt created"}}, "default": {}}, "quantity": {"type": "Number", "default": 0, "description": "Number of products of this order"}, "currency": {"type": "String", "max": 3}, "taxIncluded": {"type": "boolean"}, "cost": {"type": "number"}, "discountAmount": {"type": "number", "default": 0}, "subtotalPrice": {"type": "number", "default": 0, "description": "Sum of price of all items after applicable discounts"}, "shippingPrice": {"type": "number", "description": "Sum of price of all fulfillments after applicable discounts, TODO: default 0"}, "taxAmount": {"type": "number", "default": 0}, "amount": {"type": "number", "default": 0, "description": "Total amount (net sales)"}, "taxes": {"type": [{"title": {"type": "string"}, "rate": {"type": "number"}, "price": {"type": "number"}}]}, "program": {"type": {"id": {"type": "String", "description": "Program id"}, "tierLevel": {"type": "number", "description": "Program tier level"}, "cardNumber": {"type": "String", "description": "Membership card no. if this order belongs to a member"}}, "default": {}}, "flow": {"type": {"at": {"type": "number", "default": 0, "description": "Current step (index)"}, "steps": [{"type": "String", "enum": ["new", "received", "packed", "allocated", "collected", "delivered", "accepted", "declined", "fulfilled", "cancelled", "returned"], "description": "ordered steps for order progress"}]}}, "note": {"type": "string", "max": 256}, "external": {"type": {"perkd": {"type": {"userId": {"type": "String"}, "cardId": {"type": "String"}, "messageId": {"type": "String", "description": "X messageId of receipt message"}}}, "shopify": {"type": {"customerId": {"type": "String"}, "orderId": {"type": "String"}, "shop": {"type": "String", "description": "Shopify shopName"}}}, "grab": {"type": {"orderId": {"type": "String"}}}, "pos": {"type": {"customerId": {"type": "String"}, "orderId": {"type": "String"}}}}, "default": {}}, "tags": {"type": {"system": {"type": [{"type": "string", "max": "32"}]}, "user": {"type": [{"type": "string", "max": "32"}]}}, "default": {"system": [], "user": []}}, "visible": {"type": "boolean", "default": true}, "acquired": {"type": "TouchPoint", "required": true}, "sourceType": {"type": "String", "description": "Business | Program | Variant"}, "when": {"type": {"received": {"type": "date", "description": "Time order received from Customer", "default": null}, "paid": {"type": "date", "description": "Time of payment", "default": null}, "allocated": {"type": "date", "description": "Time order allocated to driver", "default": null}, "packed": {"type": "date", "default": null, "description": "Time order packed & ready for collection/delivery"}, "collected": {"type": "date", "description": "Time order collected at store", "default": null}, "dispatched": {"type": "date", "default": null, "description": "Time order dispatched for delivery/shipped"}, "delivered": {"type": "date", "default": null, "description": "Time customer reeceived delivery"}, "accepted": {"type": "date", "description": "Time order accepted by store", "default": null}, "declined": {"type": "date", "description": "Time order declined by store", "default": null}, "fulfilled": {"type": "date", "description": "Time order was fulfilled by provider", "default": null}, "returned": {"type": "date", "default": null}, "cancelled": {"type": "date", "description": "Time order cancelled by customer", "default": null}, "ended": {"type": "date", "description": "Time order ended (for dine-in only)", "default": null}}, "default": {}}, "reasons": {"type": {"cancelled": {"type": "String", "enum": ["customer", "expired", "deleted", "refund", "exchange", "error", "inventory", "fraud"], "description": "The reason why the order was cancelled"}}, "default": {}, "description": "Reason descriptions for 'when', eg. 'cancelled': 'returned goods'"}, "expiresAt": {"type": "Date", "description": "Order automatically cancelled on expiry (for pickup only)"}, "estimatedEndsAt": {"type": "Date", "description": "Estimated dining end time (for dine-in only)"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "validations": [], "relations": {"items": {"type": "embeds<PERSON><PERSON>", "model": "<PERSON><PERSON>", "property": "itemList", "options": {"validate": true}}, "discounts": {"type": "embeds<PERSON><PERSON>", "model": "Discount", "property": "discountList", "options": {"validate": true}}, "billings": {"type": "embeds<PERSON><PERSON>", "model": "Billing", "property": "billingList", "options": {"validate": true}}, "fulfillments": {"type": "hasMany", "model": "Fulfillment", "foreignKey": "orderId"}, "bookings": {"type": "hasMany", "model": "Booking", "foreignKey": "orderId"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "member": {"type": "belongsTo", "model": "Member", "foreignKey": "memberId"}, "membership": {"type": "belongsTo", "model": "Membership", "foreignKey": "membershipId"}, "store": {"type": "belongsTo", "model": "Place", "foreignKey": "storeId"}, "staff": {"type": "belongsTo", "model": "Staff", "foreignKey": "staffId"}, "business": {"type": "belongsTo", "model": "Business", "foreignKey": "businessId"}, "campaign": {"type": "belongsTo", "model": "Campaign", "foreignKey": "campaignId"}, "table": {"type": "belongsTo", "model": "Resource", "foreignKey": "resourceId"}}, "acls": [], "indexes": {"cardNumber_index": {"keys": {"program.cardNumber": 1}}, "external_index": {"keys": {"external.provider": 1, "external.customerId": 1}}, "deletedAt_index": {"keys": {"deletedAt": 1}}, "personId_index": {"keys": {"personId": 1}}, "membershipId_index": {"keys": {"membershipId": 1}}, "memberId_index": {"keys": {"memberId": 1}}}, "methods": {"toAccept": {"description": "Should order be accepted (considering fulfillment)", "http": {"path": "/accept", "verb": "get"}, "accepts": [{"arg": "order", "type": "object", "required": true}, {"arg": "fulfillments", "type": ["object"], "required": true}], "returns": {"type": "object", "description": "{ accept, readyAt }", "root": true}}, "createOrder": {"description": "Create an Order", "http": {"path": "/create", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "markPaid": {"description": "Mark an Order as Pa<PERSON>", "http": {"path": "/paid", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true, "description": "updates, bilingList, itemList, or membershipId, etc"}], "returns": {"type": "Order", "root": true}}, "findByTransaction": {"description": "Get an Order for payment Transaction id (with status injected)", "http": {"path": "/byTransaction", "verb": "get"}, "accepts": [{"arg": "transactionId", "type": "string", "required": true}, {"arg": "sourceType", "type": "string", "description": "Business | Program | Variant"}], "returns": {"type": "Order", "root": true}}, "cancel": {"description": "Cancel an Order", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "cancelByTransaction": {"description": "Cancel Order associated with transaction id", "http": {"path": "/cancel/byTransaction", "verb": "post"}, "accepts": [{"arg": "transactionId", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "cancelByProviderOrderId": {"description": "Cancel Order by (external) order id of provider", "http": {"path": "/cancel/provider/orderId", "verb": "post"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "orderId", "type": "string", "required": true, "description": "external order id of provider"}, {"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "relocateTableFulfillments": {"description": "Relocate (partial) fulfillments of DINEIN orders from source tables to destination tables", "http": {"path": "/tables/relocate", "verb": "post"}, "accepts": [{"arg": "from", "type": [{"type": "object", "description": "{ type, name, placeId, resourceId, position[] }"}], "required": true, "description": "List of source tables (Spot)"}, {"arg": "to", "type": [{"type": "object", "description": "{ type, name, placeId, resourceId, position[] }"}], "required": true, "description": "List of destination tables (Spot)"}], "returns": {"type": "object", "root": true}}, "storedValuesMetricsTransactions": {"description": "Stored Value metrics - transactions", "http": {"path": "/metrics/storedvalues/transactions", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": "object", "root": true, "description": "{ revenue, activeCustomers, averageDiscount, currency }"}}, "storedValuesMetricsHistory": {"description": "Stored Value metrics - history", "http": {"path": "/metrics/storedvalues/history", "verb": "get"}, "accepts": [{"arg": "from", "type": "date", "required": true}, {"arg": "to", "type": "date", "required": true}], "returns": {"type": ["object"], "root": true, "description": "[{ transactionId, type, transactionDate, balance, discount, expiryDate, currency, customerId, orderId }]"}}, "prototype.markPaid": {"description": "Mark order as Paid", "http": {"path": "/paid", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}, {"arg": "forced", "type": "boolean"}], "returns": {"type": "Order", "root": true}}, "prototype.requestFulfillment": {"description": "Request fulfillment of order", "http": {"path": "/fulfillments/request", "verb": "post"}, "accepts": [], "returns": {"type": ["Fulfillment"], "root": true}}, "prototype.cancelFulfillment": {"description": "Cancel fulfillment of order", "http": {"path": "/fulfillments/cancel", "verb": "post"}, "accepts": [{"arg": "items", "type": "array"}], "returns": {"type": "Object", "root": true}}, "prototype.cancel": {"description": "Cancel order", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Order", "root": true}}, "prototype.fulfilled": {"description": "Set status of ALL fulfillments of Order to fulfilled", "http": {"path": "/fulfillments/fulfilled", "verb": "post"}, "accepts": [{"arg": "fulfillment", "type": "object", "description": "immediate fulfill, mainly used for digital fulfillments"}], "returns": {"type": "Array", "root": true}}, "prototype.paymentStatus": {"description": "Get payment status of order", "http": {"path": "/payment/status", "verb": "post"}, "accepts": [], "returns": {"type": "Object", "root": true}}}}