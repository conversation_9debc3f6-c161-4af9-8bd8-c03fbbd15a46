/**
 *  @module Fulfillment model
 */

const { Fulfillments, Settings, Providers } = require('@crm/types'),
	{ REGISTRY } = require('@perkd/event-registry-crm'),
	{ isLastStep, delay } = require('@perkd/utils'),
	{ prepareTimeAndQuantity } = require('@perkd/orders'),
	{ spotName, Metrics } = require('@perkd/fulfillments')

const { Service, Type, Status, Step, Delivery, DestinationType } = Fulfillments,
	{ DELIVER, VENDING_MACHINE, PICKUP } = Type,
	{ DIGITAL, KITCHEN } = Service,
	{ OPEN, CANCELLED, ERROR } = Status,
	{ REQUESTED, ALLOCATED, PREPARE, PACKED, COLLECTED, DELIVERED } = Step,
	{ PICKING_UP, DELIVERING, COMPLETED } = Delivery.Status,
	{ FULFILLMENT } = Settings.Name,
	{ DEFAULT } = Settings,
	{ defaultItemPrepTime: ITEM_PREP_TIME } = DEFAULT.KITCHEN,
	{ sales: Event } = REGISTRY,
	{ State } = Metrics

module.exports = function(Fulfillment) {

	/**
	 * Get real-time (adjusted) preparation time
	 * @param	{Object[]} items of fulfillment
	 * @param	{Place} store
	 * @return	{Object} { preparationTime, first } - both in ms
	 */
	Fulfillment.preparationTimeFor = async function (items, store) {
		const { app } = Fulfillment,
			{ totalTime, first: firstInSec } = Fulfillment.prepareTimeAndQuantity(items),	// in seconds
			{ kitchen = {} } = app.getSettings(FULFILLMENT, store),
			{ capacity = 1 } = kitchen,
			preparationTime = Math.floor(totalTime * 1000 / capacity),
			first = Math.floor(firstInSec * 1000 / capacity)

		return { preparationTime, first }
	}

	/**
	 * Get aggregated Preparation Time, Quantity for items and time needed for First (longest prep time) item
	 * @param	{Object[]} items of fulfillment
	 * @return	{Object} { totalQuantity, totalTime (seconds), first (seconds) }
	 */
	Fulfillment.prepareTimeAndQuantity = function(items = []) {
		const { app } = Fulfillment,
			{ kitchen = {} } = app.getSettings(FULFILLMENT),
			{ batchMultiplier, defaultItemPrepTime = ITEM_PREP_TIME } = kitchen,
			opt = { batchMultiplier, defaultItemPrepTime },
			prepList = prepareTimeAndQuantity(items, opt),		// main items only, ie. less bundled items
			first = prepList.reduce((max, { itemTime }) => itemTime > max ? itemTime : max, 0),
			res = { first, totalTime: 0, totalQuantity: 0 }

		for (const { time, quantity } of prepList) {
			res.totalQuantity += quantity
			res.totalTime += time
		}

		return res
	}

	/**
	 * Send fulfillment request to provider
	 * @param	{Date} [at]
	 * @param	{Object} [acquired] - order touchpoint (mutated)
	 * @return 	{Fulfillment}
	 */
	Fulfillment.prototype.request = async function(at = new Date(), acquired = {}) {
		const { type, destination = {}, scheduled, orderId } = this.toJSON(),
			{ app } = Fulfillment,
			{ kitchen, OfferMaster } = app.models,
			{ context = {} } = acquired

		await this.updateAttributes({ status: OPEN })

		try {
			// deal with types handled by us, shouldn't throw for others
			switch (type) {
			case KITCHEN:
				await kitchen.request(this, acquired)		// this mutated
				break

			case VENDING_MACHINE: {
				acquired.context = context
				context.orderId = String(orderId)
				this.machineEvent(Event.fulfillment.machine.requested, acquired)
				break
			}

			case PICKUP: {
				const { vending = {} } = app.getSettings(FULFILLMENT),
					{ enabled, pickupOfferMasterId } = vending

				if (!enabled || destination.type !== DestinationType.VENDING_MACHINE) break

				const order = await this.order.get(),
					membership = await order.membership.get(),
					{ cardNumber } = membership ?? {}

				context.cardNumber = cardNumber
				context.scheduled = scheduled
				context.orderId = String(orderId)

				acquired.context = context
				acquired.location = destination

				this.machineEvent(Event.fulfillment.machine.requested, acquired)

				if (pickupOfferMasterId) {
					try {
						const { membershipId = null, itemList: items = [] } = order ?? {},
							options = { items, orderId: String(orderId) }

						await OfferMaster.issue(pickupOfferMasterId, membershipId, options)
					}
					catch (err) {
						console.error(err)
					}
				}
				break
			}

			case DELIVER:
				await Fulfillment.requestDelivery(this, acquired)		// this mutated
				break

			default:
				// refresh, 'this' may be updated by other fulfillments (eg. kitchen)
				this.setAttributes(await this.reload())
			}

			await this.toStep(REQUESTED, at)

			if (type === KITCHEN) {
				kitchen.preparationTimer()
			}

			return this
		}
		catch (error) {
			await this.updateAttributes({ status: ERROR })
			appNotify('[Fulfillmnet]request', { error, fulfillment: this }, 'error')
		}
	}

	/**
	 * Request fulfillment cancellation to provider // TODO: cancel fulfillment v.s. return item
	 * @param	{Object[]} items - of order
	 * @return 	{Object[]} cancelled
	 */
	Fulfillment.prototype.cancel = async function(items) {
		const { id, type, itemList: fulfilled, destination, mainFulfillmentType, placeId, orderId } = this.toJSON(),
			{ Metric, models } = Fulfillment.app,
			{ kitchen, Variant } = models,
			tags = {
				id: String(id),
				placeId: String(placeId),
				orderId: String(orderId),
				type
			},
			opt = { tags }

		let cancelled = this

		switch (type) {
		case DIGITAL:
			cancelled = await Variant.cancelDigitalFulfill(fulfilled)
			break

		case KITCHEN:
			cancelled = await kitchen.cancel(items, fulfilled)

			opt.child = KITCHEN
			tags.type = mainFulfillmentType
			tags.table = spotName(destination)
			break
		}

		await this.updateAttributes({ status: CANCELLED })
		appEmit(Event.fulfillment.cancelled, this)

		appMetric(Metric.fulfillment.state, State.CANCELLED, opt)
		delay(500).then(() => appMetric(Metric.fulfillment.state, State.END, opt))

		return cancelled
	}

	/**
	 * Mark as Requested
	 * @param	{Date} [at] - occurred at
	 * @param	{Object} [updates] - to be applied to fulfillment (mongo)
	 */
	Fulfillment.prototype.requested = async function(at = new Date(), updates = {}) {
		updates.status = OPEN

		const self = await this.toStep(REQUESTED, at, undefined, updates)
		if (!self) throw new Error('already_requested')
	}

	/**
	 * Mark as Allocated (DELIVER only - driver assigned, may repeat when re-allocated to new driver)
	 * @param	{String} provider
	 * @param	{Object} delivery
	 *				{String} id
	 *				{Object} driver { id, name, phone, plateNumber, photo }
	 *				{Geometry} location
	 *				{Date} pickup
	 *				{Date} eta
	 * @param	{Date} [at] - occurred at
	 */
	Fulfillment.prototype.allocated = async function(provider, delivery = {}, at) {
		const { id, driver, location, pickup, eta } = delivery,
			externalDeliveryId = `external.${provider}.deliveryId`,
			updates = {
				'deliver.status': PICKING_UP,
				'deliver.driver': driver,
				'deliver.location': location,
				'deliver.pickup': pickup,
				'deliver.eta': eta,
				[externalDeliveryId]: id,
			}

		return this.toStep(ALLOCATED, at, undefined, updates, true)
	}

	/**
	 * Mark as Collected
	 * @param	{Date} [at] - occurred at
	 * @param	{Date} [eta] - DELIVER only
	 * @param	{String} [staffId]
	 */
	Fulfillment.prototype.collected = async function(at, eta, staffId) {
		const { type, orderId, provider } = this,
			updates = {}

		if (type === DELIVER) {
			updates['deliver.status'] = DELIVERING
			updates['deliver.eta'] = eta

			// notify Order provider (if different from fulfillment provider)
			this.providerAndOrderId()
				.then(async ({ provider: orderProvider, providerOrderId }) => {
					if (orderProvider && (orderProvider.name !== provider) && providerOrderId) {
						await orderProvider.fulfillments.collected(providerOrderId)
					}
				})
				.catch(err => appNotify('[Fulfillment]collected - notify provider', { orderId, err }, 'error'))
		}

		const self = await this.toStep(COLLECTED, at, staffId, updates)

		if (!self) throw new Error('already_collected')
		return self
	}

	/**
	 * Mark as Delivered
	 * @param	{Date} [at] - occurred at
	 * @param	{String} [staffId] - not used
	 */
	Fulfillment.prototype.delivered = async function(at, staffId) {
		const { type, orderId, provider } = this,
			updates = {}

		if (type === DELIVER) {
			updates['deliver.status'] = COMPLETED

			// notify Order provider (if different from fulfillment provider)
			this.providerAndOrderId()
				.then(async ({ provider: orderProvider, providerOrderId }) => {
					if (orderProvider && (orderProvider.name !== provider) && providerOrderId) {
						await orderProvider.fulfillments.delivered(providerOrderId)
					}
				})
				.catch(err => appNotify('[Fulfillment]delivered - notify provider', { orderId, err }, 'error'))
		}

		const self = await this.toStep(DELIVERED, at, staffId, updates)

		if (!self) throw new Error('already_delivered')
		return self
	}

	/**
	 * Set new destination for fulfillment
	 * @param	{Spot} destination - new destination
	 *			{String} type
	 *			{Object[]} position - [{ key, value }]
	 * @param	{Object} [through]
	 */
	Fulfillment.prototype.relocate = async function (destination, through) {
		const { type, destination: from } = this,
			context = { from, to: destination },
			event = type === KITCHEN
				? Event.fulfillment.kitchen.relocated
				: Event.fulfillment.relocated

		await this.updateAttributes({ destination })
		appEmit(event, this, { through, context })
	}

	/**
	 * @param	{String} [name] of provider
	 * @returns {Object}
	 */
	Fulfillment.prototype.providerAndOrderId = async function(name) {
		const { orderId } = this,
			{ models } = Fulfillment.app,
			{ Order } = models,
			order = await Order.findById(orderId),
			{ external = {}, acquired = {} } = order ?? {},
			{ attributedTo = {} } = acquired,
			{ type } = attributedTo,
			providerName = name || type,
			{ orderId: providerOrderId } = external[providerName] || {},
			provider = providerName ? await Order.getProvider(providerName) : undefined

		return { provider, providerOrderId }
	}

	// ----  Kitchen only ----

	/**
	 * Mark as Preparing (Kitchen only)
	 * @param	{Date} [at] - occurred at
	 * @param	{String} [staffId] - prepared by
	 */
	Fulfillment.prototype.preparing = async function(at = new Date(), staffId) {
		const { id, prepare, destination, mainFulfillmentType: type,  mainFulfillmentId, placeId, orderId } = this.toJSON(),
			{ Metric } = Fulfillment.app,
			{ startTime, endTime } = prepare,
			preparationTime = endTime - startTime,
			eta = new Date(at.getTime() + preparationTime),
			updates = { staffId }

		// shift & update min/max time if have passed
		if (this.minTime < eta) {
			const duration = this.maxTime.getTime() - this.minTime.getTime(),
				minTime = eta,
				maxTime = new Date(minTime.getTime() + duration)

			updates.minTime = minTime
			updates.maxTime = maxTime

			appNotify('[Fulfillment]preparing - re-estimate', { minTime, maxTime, preparationTime, duration }, 'schedule', '-kitchen')
		}

		const res = await this.toStep(PREPARE, at, staffId, updates),
			tags = {
				id: String(id),
				placeId: String(placeId),
				orderId: String(orderId),
				type,
				table: spotName(destination),
			},
			opt = { child: Type.KITCHEN, tags }

		if (!res) throw new Error('already_prepare')

		appMetric(Metric.fulfillment.state, State.PREPARE, opt)

		// update main fulfillment
		if (mainFulfillmentId) {
			const mainFulfill = await Fulfillment.findById(mainFulfillmentId),
				{ minTime, maxTime } = this

			await mainFulfill.toStep(PREPARE, at, staffId, { minTime, maxTime })
		}

		return res
	}

	/**
	 * Cancel Preparing (Kitchen only)
	 * @param	{String} [staffId] - not used
	 */
	Fulfillment.prototype.cancelPrepare = async function(staffId) {
		const { mainFulfillmentId } = this,
			self = await this.revertToStep(PREPARE)

		if (!self) throw new Error('unable_to_cancel')

		// update main fulfillment
		if (mainFulfillmentId) {
			const mainFulfill = await Fulfillment.findById(mainFulfillmentId)
			await mainFulfill.revertToStep(PREPARE)
		}

		return self
	}

	/**
	 * Mark as Packed (Kitchen only)
	 * @param	{Date} [at] occurred at
	 * @param	{String} [staffId]
	 */
	Fulfillment.prototype.packed = async function(at = new Date(), staffId) {
		const { id, mainFulfillmentId, flow, when, prepare, destination, mainFulfillmentType: type, placeId, orderId } = this.toJSON(),
			{ Metric, models } = Fulfillment.app,
			{ kitchen, Place, Order } = models,
			{ endTime } = prepare,
			whenPacked = `when.${PACKED}`,
			self = await this.toStep(PACKED, at, staffId),
			tags = {
				id: String(id),
				placeId: String(placeId),
				orderId: String(orderId),
				type,
				table: spotName(destination)
			},
			opt = { child: Type.KITCHEN, tags }

		if (!self) throw new Error('already_packed')

		appMetric(Metric.fulfillment.state, State.PACKED, opt)
		delay(500).then(() => appMetric(Metric.fulfillment.state, State.END, opt))

		appMetric(Metric.fulfillment.time.prepare, at - when.prepare, opt)

		// update main fulfillment
		if (mainFulfillmentId && isLastStep(flow, PACKED)) {
			const [ order, mainFulfill ] = await Promise.all([
					Order.findById(orderId),
					Fulfillment.findById(mainFulfillmentId)
				]),
				{ provider, placeId: fulfillFrom } = mainFulfill,
				[ store ] = await Promise.all([
					Place.findById(fulfillFrom),
					mainFulfill.toStep(PACKED, at, staffId)
				])

			// Notify Fulfillment provider order is READY for pickup
			if (type === DELIVER) {
				await mainFulfill.updateAttributes({ [whenPacked]: at })	// no 'packed' step, updateStep() above ignores update
				appEmit(Event.fulfillment[type].packed, mainFulfill)		// not emitted in updateStep

				this.providerAndOrderId(provider)
					.then(async ({ provider, providerOrderId }) => {
						if (provider.supports(Providers.Service.FULFILLMENT) && providerOrderId) {
							await provider.fulfillments.ready(providerOrderId)
						}
					})
					.catch(err => {
						if (!mainFulfill.when.collected) { // it's normal if already collected
							appNotify('[Fulfillment]packed - notify provider', { orderId, err }, 'error')
						}
					})
			}

			if ([ DELIVER, PICKUP ].includes(type)) {
				order.printReceipt(store)
			}
		}

		// free up schedule IF packed BEFORE endTime (most current order)
		if (endTime && endTime > at) {
			prepare.endTime = at
			await this.updateAttributes({ prepare })

			kitchen.optimize(placeId, at)
		}

		return self
	}

	/**
	 * Schedule (auto) Pack
	 */
	Fulfillment.prototype.schedulePack = async function(now) {
		const { prepare = {} } = this,
			{ endTime } = prepare,
			preparation = Math.max(endTime.getTime() - now.getTime(), 1000)

		delay(preparation).then(() => this.packed())
			.catch(err => appNotify('[Fulfillment]schedulePack', { err }))
	}

	/**
	 * (Re)-print Kitchen Order Ticket  (kitchen type only)
	 */
	Fulfillment.prototype.reprintKitchenTicket = async function() {
		const { id, type, placeId, mainFulfillmentId } = this,
			{ Place } = Fulfillment.app.models,
			[ store, mainFulfill ] = await Promise.all([
				Place.findById(placeId),
				Fulfillment.findById(mainFulfillmentId)
			])

		if (type !== KITCHEN) throw new Error(`Type not 'kitchen' (fulfillmentId: ${id})`)

		return this.printKitchenTicket(store, mainFulfill)
	}

	// -----  Operation Hooks  -----

	Fulfillment.observe('after save', async ({ instance, isNewInstance }) => {
		if (isNewInstance) {
			appEmit(Event.fulfillment.created, instance)
		}
		else {
			appEmit(Event.fulfillment.updated, instance)
		}
	})
}
