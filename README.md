# Sales Service
A microservice for handling sales orders, fulfillment, and payment processing in the CRM platform.

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Core Features](#core-features)
4. [Order Flow](#order-flow)
5. [Fulfillment Types](#fulfillment-types)
6. [Configuration Settings](#configuration-settings)
7. [Service Integration](#service-integration)
8. [Data Models](#data-models)
9. [API Endpoints](#api-endpoints)
10. [Events](#events)

## Overview
This service provides comprehensive order management and fulfillment capabilities that handles multiple channels, payment processing, and various fulfillment types.

### System Architecture
```mermaid
graph TB
    subgraph OrderProviders[Order Providers]
        direction LR
        Shopify[Shopify]
        UberEats[UberEats]
        GrabFood[GrabFood]
        GrabMart[GrabMart]
    end

    subgraph SalesService[Sales Service]
        subgraph Core
            Orders[Order Processing]
            Payment[Payment Management]
            Fulfill[Fulfillment Manager]
        end

        subgraph Fulfillment
            Kitchen[Kitchen]
            Store[Store Pickup]
            Delivery[Delivery]
            DineIn[Dine-in]
            Digital[Digital]
        end
    end

    subgraph FulfillmentProviders[Fulfillment Providers]
        direction LR
        ShopifyF[Shopify]
        GrabFoodF[GrabFood]
        GrabMartF[GrabMart]
        UberEatsF[UberEats]
        Lalamove[Lalamove]
        GrabExpress[Grab Express]
    end

    %% Connections
    OrderProviders --> Orders
    Orders --> Payment
    Orders --> Fulfill
    Fulfill --> Kitchen
    Fulfill --> Store
    Fulfill --> Delivery
    Fulfill --> DineIn
    Fulfill --> Digital
    Delivery --> FulfillmentProviders

    %% Styles
    %% classDef provider fill:#f9f,stroke:#333,stroke-width:2px
    %% classDef fulfillProvider fill:#9ef,stroke:#333,stroke-width:2px
    %% classDef label fill:none,stroke:none
    %% classDef hidden fill:none,stroke:none,color:white
    %% class Shopify,UberEats,GrabFood,GrabMart provider
    %% class Lalamove,GrabExpress,UberEatsF,GrabFoodF,GrabMartF,ShopifyF fulfillProvider
```

### Core Features

1. **Multi-Channel Order Processing**
   - Support for multiple sales channels (Shopify, UberEats, GrabFood, GrabMart)
   - Channel-specific order handling and validation
   - Real-time order status synchronization
   - External provider integration (SFCC, Shopify)

2. **Payment Processing**
   - Multiple payment methods (card, bank, alipay, linepay, cash, stored value)
   - Payment validation and verification
   - Refund handling
   - Transaction tracking and reconciliation

3. **Fulfillment Management**
   - Multi-tenant fulfillment tracking
   - Multiple fulfillment types:
     - Store pickup
     - Delivery
     - Dine-in
     - Kitchen preparation
     - Vending machine
   - Real-time status tracking
   - Auto-packing capabilities

   For detailed fulfillment specifications and configuration options, see:
   - [Fulfillment Documentation](docs/fulfillment.md)
   - [Settings Documentation](docs/settings.md)
   - [Vending Machine Documentation](docs/vending-machine.md)

4. **Kitchen Operations**
   - Preparation timer tracking
   - Auto-packing functionality
   - Store-specific kitchen settings
   - Real-time kitchen monitoring
   - Preparation queue management
   - Station-based preparation routing
   - Partial fulfillment tracking
   - Kitchen order metrics

   ```mermaid
   graph TB
       Order[New Order] --> Handler[Request Handler]
       Handler --> Schedule[Schedule Allocation]
       Handler --> Queue[Queue Management]
       Schedule --> Timer[Preparation Timer]
       Schedule --> Slots[Time Slot Management]
       Timer --> Prepare[Kitchen Preparation]
       Queue --> Prepare
       Prepare --> Pack[Packing]
       Pack --> Ready[Order Ready]
   ```

   For detailed kitchen operations, see [Kitchen](docs/kitchen.md)

5. **Resource Booking**
   - Comprehensive booking and reservation system
   - Multiple resource types support (tables, venues, events, persons)
   - Resource capacity tracking and availability management
   - Real-time availability updates
   - Queue-based concurrent booking handling
   - Advanced booking features:
     - Shared/non-shared resource booking
     - Time-based capacity management
     - Booking confirmation/cancellation flows
     - Check-in/check-out tracking

   For detailed booking system specifications, see [Booking](docs/booking.md)

6. **Receipts & Tax Invoices**
   - Order receipts generation & printing
   - Government tax invoices (Taiwan only) generation & printing
   - Fulfillment tickets generation & printing
   - Kitchen order tickets (KOT) generation & printing

   For details on receipt and printing, see [Receipt](docs/receipt.md)


### Order Flow
The following diagrams show the order processing flow through the system:

#### Logic Flow
```mermaid
flowchart TD
    Start([Order Request]) --> Validate{Validate Order}

    Validate -->|Invalid| Reject([Reject Order])
    Validate -->|Valid| Process[Process Payment]

    Process --> PaymentCheck{Payment Status}
    PaymentCheck -->|Failed| ReverseChanges[Reverse Changes]
    ReverseChanges --> NotifyFailure([Notify Failure])

    PaymentCheck -->|Success| CreateFulfillment[Create Fulfillment]
    CreateFulfillment --> FulfillType{Fulfillment Type}

    FulfillType -->|Digital| Digital[Generate Digital Access]
    FulfillType -->|Kitchen| Kitchen[Create Kitchen Order]
    FulfillType -->|Delivery| Delivery[Allocate Delivery]
    FulfillType -->|Store| Store[Create Store Pickup]
    FulfillType -->|Dine-in| Dinein[Create Table Service]
    FulfillType -->|Vending| Vending[Create Machine Order]

    Digital --> UpdateOrder[Update Order Status]
    Kitchen --> PrepareItems[Prepare & Pack Items]
    PrepareItems --> UpdateOrder
    Delivery --> AssignDriver[Assign Driver]
    AssignDriver --> UpdateOrder
    Store --> UpdateOrder
    Dinein --> UpdateOrder
    Vending --> UpdateOrder

    UpdateOrder --> NotifySuccess([Send Confirmation])

    %% Error handling from any stage
    Kitchen -- Error --> ReverseChanges
    Delivery -- Error --> ReverseChanges
    Store -- Error --> ReverseChanges
    Dinein -- Error --> ReverseChanges
    Vending -- Error --> ReverseChanges
    Digital -- Error --> ReverseChanges
```

#### Sequence Flow
```mermaid
sequenceDiagram
    actor Customer
    participant Order
    participant Payment
    participant Fulfillment
    participant Kitchen
    participant Provider

    Customer->>Order: Place Order
    Note over Order,Provider: Begin Transaction

    Order->>Order: Validate & Accept
    Order->>Payment: Process Payment

    alt Payment Success
        Payment->>Fulfillment: Create Fulfillment

        alt Kitchen Items
            Fulfillment->>Kitchen: Create Kitchen Order
            Kitchen->>Kitchen: Prepare Items
            Kitchen->>Kitchen: Pack Items
        end

        alt Delivery
            Fulfillment->>Provider: Allocate Driver
            Provider->>Provider: Pickup & Deliver
        end

        alt Digital
            Fulfillment->>Fulfillment: Generate Digital Access
            Fulfillment->>Customer: Deliver Digital Content
        end

        Fulfillment->>Order: Update Status
        Order->>Customer: Send Confirmation
    else Payment/Process Failure
        Order->>Payment: Reverse Payment
        Order->>Fulfillment: Cancel Fulfillment
        Order->>Customer: Send Failure Notice
    end
    Note over Order,Provider: End Transaction
```

The flow includes these key stages, all handled within a single atomic transaction:
1. Order Creation & Validation
2. Payment Processing
3. Fulfillment Creation
4. Preparation (for kitchen orders)
5. Delivery (for delivery orders)
6. Digital Access (for digital orders)
7. Order Completion

If any stage fails, all changes are automatically rolled back to maintain data consistency. Different fulfillment types (store pickup, delivery, dine-in, digital) may have variations in their exact flow, but follow this general pattern.


## Fulfillment Types

1. **Digital** (`digital`)
   - Digital product fulfillment
   - Immediate delivery
   - Membership-based delivery

2. **Store** (`store`)
   - In-store pickup and fulfillment
   - Supports kitchen preparation
   - Direct store collection

3. **Pickup** (`pickup`)
   - Customer pickup from store/location
   - Optional vending machine integration
   - Supports scheduled pickup times

4. **Delivery** (`deliver`)
   - Third-party delivery services
   - Driver assignment and tracking
   - Real-time ETA updates
   - Delivery status tracking (pending, pickingup, delivering, delivered, etc.)

5. **Dine-in** (`dinein`)
   - Table service fulfillment
   - Restaurant/café orders
   - On-premise consumption

6. **Vending Machine** (`vending`)
   - Automated dispensing
   - Card-based authentication
   - Real-time inventory tracking

7. **Kitchen** (`kitchen`)
   - Preparation time tracking
   - Capacity-based scheduling
   - Batch processing support
   - Real-time kitchen monitoring
   - Station-based preparation routing


## Configuration Settings

The service supports extensive configuration options for customizing behavior across different tenants, stores, and fulfillment types. Key configuration categories include:

- Order processing rules and validation
- Kitchen preparation timings
- Store operational parameters
- Receipt and invoice templates
- Booking system settings

For detailed configuration options and examples, see [Settings](docs/settings.md).


## Service Integration
This service integrates with multiple microservices through APIs and events:

```mermaid
graph TB
    subgraph Platform Services
        TM[Tenants Management]
        TM[Tenants Management]

        SM[Settings Management]
        Monitor[CloudWatch Monitoring]
    end

    subgraph Sales Service
        subgraph API
            subgraph Order APIs
                OA1[Order Management]
                OA2[Payment Processing]
                OA3[Fulfillment]
            end

            subgraph Staff APIs
                SA1[Staff Operations]
                SA2[Kitchen Management]
                SA3[Store Operations]
            end

            subgraph Provider APIs
                PA1[Channel Integration]
                PA2[Delivery Management]
            end
        end

        subgraph Model
            Order[Orders]
            Payment[Payments]
            Fulfill[Fulfillments]
            Business[Business]
            Staff[Staff]
            Resource[Resources]
        end

        %% Order flows
        OA1 --> Order
        OA2 --> Payment
        OA3 --> Fulfill

        %% Staff flows
        SA1 --> Staff
        SA1 --> Order
        SA2 --> Fulfill
        SA3 --> Business

        %% Provider flows
        PA1 --> Order
        PA2 --> Fulfill
    end

    Model --> Events[Event Bus]

    subgraph External Services
        Events --> MS[Membership Service]
        Events --> RS[Reward Service]
        Events --> PS[Payment Service]
        Events --> BS[Business Service]
        Events --> CS[Content Service]
        Events --> PRS[Product Service]
        Events --> AS[Action Service]
        Events --> PLS[Place Service]
        Events --> TS[Trigger Service]
        Events --> OS[Offer Service]
    end

    %% Style definitions with white text
    classDef order fill:#4CAF50,color:#ffffff
    classDef payment fill:#FF9800,color:#ffffff
    classDef fulfill fill:#9C27B0,color:#ffffff
    classDef staff fill:#F44336,color:#ffffff
    classDef business fill:#2196F3,color:#ffffff
    classDef resource fill:#FF5722,color:#ffffff

    %% Apply styles to nodes
    class Order order
    class Payment payment
    class Fulfill fulfill
    class Staff staff
    class Business business
    class Resource resource

    %% Style for links
    linkStyle default stroke:#999
    %% Order flows (should match Order color: #4CAF50)
    linkStyle 0 stroke:#4CAF50
    %% Payment flows (should match Payment color: #FF9800)
    linkStyle 1 stroke:#FF9800
    %% Fulfill flows (should match Fulfill color: #9C27B0)
    linkStyle 2,5 stroke:#9C27B0
    %% Staff flows (should match Staff color: #F44336)
    linkStyle 3 stroke:#F44336
    %% Order flows (should match Order color: #4CAF50)
    linkStyle 4,7 stroke:#4CAF50
    %% Business flows (should match Business color: #2196F3)
    linkStyle 6 stroke:#2196F3
    %% Fulfill flows (should match Fulfill color: #9C27B0)
    linkStyle 8 stroke:#9C27B0
```


## Data Models
The key entities and their relationships:

```mermaid
erDiagram
    Order ||--o{ Item : contains
    Order ||--o{ Fulfillment : has
    Order ||--o{ Booking : has
    Order ||--|| Person : "placed by"
    Order ||--|| Staff : "handled by"
    Order ||--|| Business : "belongs to"
    Order ||--|| Place : "store"
    Order ||--|| Resource : "table"

    Fulfillment ||--|{ FulfillItem : contains
    Fulfillment ||--|| Place : "store"

    Business ||--|{ Staff : employs
    Business ||--|{ Place : owns

    Staff ||--|| Person : "is"
    Staff ||--|{ Place : "works at"

    Person ||--|{ Visit : makes
    Visit ||--|| Place : "to"

    Place ||--|{ Resource : has
    Resource ||--|{ Booking : reserves

    Item {
        string id PK
        string kind
        number quantity
        number price
        number discountAmount
        number tax
    }

    Order {
        string id PK
        string status
        string type
        number amount
        string currency
        date createdAt
        date updatedAt
        object flow
        object acquired
        object external
        object receipt
        string resourceId FK "For dine-in table"
    }

    Fulfillment {
        string id PK
        string status
        string type
        string service
        date requestedAt
        date completedAt
        object deliver
    }

    Business {
        string id PK
        string name
        string type
        object settings
    }

    Staff {
        string id PK
        string role
        object permissions
        date joinedAt
    }

    Person {
        string id PK
        string name
        object contact
        date birthDate
    }

    Place {
        string id PK
        string name
        object location
        object hours
    }

    Resource {
        string id PK
        string type
        number capacity
        object availability
    }

    Booking {
        string id PK
        date startTime
        date endTime
        string status
        string orderId FK
        string resourceId FK
    }

    FulfillItem {
        string id PK
        string itemId FK
        string status
        number quantity
        date preparedAt
        date packedAt
        object preparation
    }

    Visit {
        string id PK
        string kind
        object geo
        date from
        date until
        number duration
    }
```

### Key Entities

1. **Order**
   - Core entity representing customer orders
   - Tracks order status, amounts, and payment details
   - Links to fulfillment, items, and customer information
   - Supports multiple order types (retail, delivery, pickup)
   - Has direct relationship with Resource for dine-in table assignments
   - Has indirect relationship with Resource through Bookings

2. **Fulfillment**
   - Manages order execution and delivery
   - Handles different fulfillment types (kitchen, delivery, pickup)
   - Tracks preparation and delivery status
   - Manages resource allocation and scheduling

3. **Business**
   - Represents merchant/company entity
   - Manages business settings and configurations
   - Controls staff and store management
   - Handles payment gateway integrations

4. **Staff**
   - Represents employees and system users
   - Manages roles and permissions
   - Tracks assignments and schedules
   - Links to authentication and identity management

5. **Person**
   - Represents customers and users
   - Stores personal and contact information
   - Tracks visit history and preferences
   - Links to membership and loyalty programs

6. **Place**
   - Represents physical locations (stores, kitchens)
   - Manages operating hours and availability
   - Tracks location-specific settings
   - Controls resource management

7. **Resource**
   - Manages bookable/reservable assets (including tables)
   - Handles capacity and availability
   - Supports shared and exclusive resources
   - Tracks resource utilization
   - Can be directly linked to Orders (for dine-in tables)
   - Can be linked to Orders through Bookings

8. **Booking**
   - Manages reservations and appointments
   - Handles resource allocation
   - Tracks booking status and timeline
   - Links orders to resources
   - Properties:
     - Status tracking (pending, open, success, reserved, ended, cancelled, noshow, error)
     - Time slot management
     - Resource allocation
     - Order association
     - Check-in/check-out tracking (arrivedAt, departedAt)

9. **FulfillItem**
   - Represents individual items in a fulfillment
   - Tracks preparation and packing status
   - Manages item-level fulfillment workflow
   - Supports partial fulfillment tracking
   - Links to original order items
   - Properties:
     - Status tracking (pending, preparing, packed)
     - Preparation timing and details
     - Quantity management
     - Station/kitchen routing info

10. **Visit**
    - Tracks customer presence at locations
    - Supports both nearby and in-store visits
    - Records entry/exit timestamps
    - Calculates visit duration
    - Enables location-based services
    - Properties:
      - Visit type (nearby, instore)
      - Geolocation data
      - Duration tracking
      - Entry/exit timestamps

The data model continues to support multi-tenant architecture with strict access controls and data isolation between businesses.


## API Endpoints
For detailed API documentation, see [API.md](docs/API.md).

### Order Management
- `POST /Orders/create` - Create new order
- `POST /Orders/paid` - Mark order as paid
- `POST /Orders/cancel` - Cancel order
- `POST /Orders/:id/paid` - Mark specific order as paid
- `POST /Orders/:id/cancel` - Cancel specific order
- `GET /Orders/byTransaction` - Get order by transaction ID
- `GET /Orders/payment/status` - Get order payment status
- `POST /Orders/fulfillments/request` - Request order fulfillment
- `POST /Orders/fulfillments/cancel` - Cancel order fulfillment

### Fulfillment Operations
- `POST /shipping/rates` - Calculate shipping rates
- `POST /pickup/times` - Get estimated pickup

### Booking Operations
- `POST /Bookings/request` - Create a new booking request
- `POST /Bookings/confirm` - Confirm a pending booking
- `POST /Bookings/request/confirm` - Request and confirm a booking
- `POST /Bookings/cancel` - Cancel a booking
- `POST /Bookings/occupied` - Get occupied time slots for resources
- `GET /Bookings/pending/membership` - Get pending bookings for a membership
- `GET /Bookings/reservation/active` - Get active reservation for resource

### Table Booking Operations
- `POST /Products/app/booking/tables` - Book tables for member
- `POST /Products/web/booking/tables` - Book tables for non-member
- `DELETE /Products/app/booking/tables/:reservationId` - Cancel table booking

### Staff Operations (Widgets)
#### Orders
- `GET /staff/orders/store/:id/summary` - Get store sales summary
- `GET /staff/orders/open` - Get open orders before preparation
- `GET /staff/orders/unpaid` - Get unpaid orders
- `GET /staff/orders/paid` - Get paid orders
- `GET /staff/orders/all` - Get all orders for store (today)
- `POST /staff/orders/:id/invoice/reissue` - Reissue invoice
- `POST /staff/orders/:id/invoice` - Create/print invoice

#### Fulfillment
- `GET /staff/fulfillments/prepare` - Get preparation list
- `GET /staff/fulfillments/pack` - Get packing list for store
- `GET /staff/fulfillments/kitchen` - Get kitchen fulfillment status

- `POST /staff/fulfillments/:id/prepare` - Start preparation
- `POST /staff/fulfillments/:id/packed` - Mark as packed
- `POST /staff/fulfillments/:id/fulfill/items` - Update item fulfillment
- `POST /staff/fulfillments/:id/queue` - Priority queue fulfillment
- `POST /staff/fulfillments/:id/prepare/cancel` - Cancel preparation
- `POST /staff/fulfillments/:id/print/kitchen` - Print kitchen order ticket
- `POST /staff/fulfillments/:id/delivered` - Mark fulfillment as delivered

### Shopify Apps
- `GET /shopify/dashboard/summary` - Get sales summary
- `GET /metrics/storedvalues/history` - Get stored value metrics history  // TODO /shopify/storedvalues/history


## Events

### Published Events
Events emitted by this service for other services to consume.

#### Order Events
- `sales.order.created` - New order created
- `sales.order.paid` - Payment completed
- `sales.order.cancelled` - Order cancelled
- `sales.order.refunded` - Refund processed
- `sales.order.status.changed` - Order status updated
- `sales.order.accepted` - Order accepted by store
- `sales.order.declined` - Order declined by store
- `sales.order.returned` - Order return processed
- `sales.order.fulfilled` - Order fulfillment completed

#### Fulfillment Events
- `sales.fulfillment.created` - New fulfillment created
- `sales.fulfillment.updated` - Fulfillment details updated
- `sales.fulfillment.item.success` - Item fulfillment successful
- `sales.fulfillment.item.error` - Item fulfillment failed

#### Fulfillment Type Events
- `sales.fulfillment.requested.[store|pickup|deliver|dinein]`
- `sales.fulfillment.prepare.[store|pickup|deliver|dinein]`
- `sales.fulfillment.packed.[store|pickup|deliver|dinein]`
- `sales.fulfillment.allocated.deliver`
- `sales.fulfillment.collected.[deliver|machine]`
- `sales.fulfillment.delivered.deliver`

#### Booking Events
- `booking.confirmed` - a booking is confirmed (internal event)
- `booking.cancelled` - a booking is cancelled
- `booking.ended` - a booking is completed (after customer departed)
- `booking.noshow` - a booking becomes no-show (customer didn't arrive within grace period)
- `booking.deleted` - a booking is removed (cleanup of expired bookings)
- `sales.booking.reservation.confirmed` - a reservation (group of bookings) is confirmed
- `sales.booking.reservation.cancelled` - a reservation is cancelled

##### Provider Events
- `fulfillment.provider.allocated` - Driver/resource allocated
- `fulfillment.provider.arrived` - Driver arrived at pickup
- `fulfillment.provider.collected` - Order collected by provider
- `fulfillment.provider.delivered` - Delivery completed


### Subscribed Events
Events consumed by this service from other services.

#### Person Events
- `person.visit.arrive` - when a customer arrives (checked in) at the venue

#### Provider Events
- `provider.order.created` - New order from external provider
- `provider.order.updated` - Order update from provider
- `provider.order.cancelled` - Order cancellation from provider
- `provider.driver.assigned` - Delivery driver assignment
- `provider.delivery.status` - Delivery status updates

### Event Structure
```json
{
  "id": "unique_event_id",
  "name": "event.name",
  "domain": "service_domain",
  "actor": "entity_type",
  "action": "operation",
  "data": {
    // Event specific payload
  },
  "tenantCode": "tenant_identifier",
  "timezone": "Asia/Singapore",
  "published": "timestamp"
}
```
