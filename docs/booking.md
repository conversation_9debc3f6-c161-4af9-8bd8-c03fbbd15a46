# Booking System
The Booking system is part of the Sales Service in the CRM platform that handles the booking of resources.

## Table of Contents
1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Booking States](#booking-states)
4. [Booking Lifecycle](#booking-lifecycle)
5. [Booking Flow](#booking-flow)
6. [Tickets](#tickets)
7. [Integrations](#integrations)
8. [Data Models](#data-models)
9. [API EndPoints](#api-endpoints)
10. [Events](#events)

## Overview
The booking system provides a robust solution for managing resource reservations with support for both shared and non-shared resources. It handles various aspects of the booking lifecycle, from initial requests to confirmation and cancellation.

### Kinds of Bookings
The system supports 4 kinds of bookings:
- `events` with fixed time slots and capacity limits
- `venues` with operating hours and service areas
- `tables` with seating capacity and combination rules
- `staff` with availability schedules and service assignments

Each kind has specific validation rules, capacity handling, queue management, resource allocation algorithms, and business hours rules.

### Shared and Non-shared Resources
- **Shared**: Multiple bookings can share the same resource simultaneously (up to capacity, e.g., event spaces)
  - Managed through capacity limits
  - Support for concurrent bookings
  - Dynamic resource combinations
- **Non-shared**: Exclusive usage for entire duration (e.g., private rooms)
  - Complete resource locking
  - Turnover time management
  - Preparation period handling


## Core Components
```mermaid
graph TB
    API[Booking API]
    subgraph "Core Components"
        Q[Queue]
        PV[Pre-Validation]
        EO[Execution & Orchestration]
        RM[Resource Manager]
        DB[(Database)]

        Q -->|1\. enqueue| PV
        PV -->|2\. validate| RM
        PV -->|3\. process| EO
        EO -->|4\. allocate| RM
        EO -->|5\. commit| DB

        RM -.->|check/update capacity| DB
    end
    PS[Product Service API]

    API -.->|submit request| Q
    API -.->|query state| DB

    PV -.->|validate product rules| PS
    RM -.->|get capacity rules| PS
    PS -.->|query bookings| API

    %% Styling
    classDef composite fill:#f2f2f2,stroke:#333,stroke-width:1px;
    linkStyle 0,1,2,3,4 stroke:#2B7CE9,stroke-width:2px;
```

### Component Roles
1. **Queue**
   - Handles concurrent booking requests
   - Ensures ordered processing
   - Prevents race conditions

2. **Pre-Validation**
   - Validates booking requests
   - Checks product rules
   - Ensures request validity

3. **Execution & Orchestration**
   - Manages booking workflow
   - Coordinates components
   - Handles core booking logic

4. **Resource Manager**
   - Manages resource availability
   - Handles capacity tracking
   - Validates resource allocation


## Booking States
```mermaid
stateDiagram-v2
    [*] --> pending: Request
    [*] --> reserved: Staff Reserve
    pending --> open: Validation Success
    pending --> cancelled: Cancel/Expire
    pending --> error: System Error
    open --> success: Check-In
    open --> noshow: Grace Period Expired
    open --> cancelled: Cancel
    open --> error: System Error
    success --> ended: Check-Out/Complete
    success --> cancelled: Cancel
    success --> error: System Error
    reserved --> ended: Staff Release
    reserved --> cancelled: Cancel
    cancelled --> [*]
    noshow --> [*]
    error --> [*]
    ended --> [*]
```

Each state has specific rules and triggers:
- `pending`: Initial request state with temporary resource hold
- `open`: Validated booking awaiting check-in
- `success`: Active booking after check-in
- `reserved`: Table/resource reserved manually by staff (for maintenance, special events)
- `ended`: Completed booking after check-out
- `cancelled`: Cancelled by system or user
- `noshow`: No check-in within grace period
- `error`: System error in any stage

## Booking Lifecycle

1. **Request Phase**
   - Validate `startTime`, `endTime`, and party size
   - Check resource availability
   - Record `source` of booking request (channel or system)
   - Calculate required `deposit` amount if applicable
   - Create booking record with status: `pending`
   - Set optional `expiresAt` based on TTL parameter
   - Emit `booking.request` metric

2. **Confirmation Phase**
   - Assign and reserve resources
   - Associate booking with order, person, and membership
   - Verify deposit payment if required
   - Update status to `open` (not `success`)
   - Clear `expiresAt` timestamp
   - Emit `booking.confirmed` event
   - Create calendar events if configured

3. **Execution Phase**
   - Process customer arrival/check-in (sets `arrivedAt`)
   - Active booking management
   - Queue integration for no-shows
   - Real-time status tracking
   - Monitor capacity utilization

4. **Completion Phase**
   - Process customer departure/check-out (sets `departedAt`)
   - Release resource capacity
   - Update status to `ended`
   - Emit `booking.ended` event
   - Collect performance metrics

5. **Automatic Cleanup Process**
The system includes an efficient cleanup mechanism implemented via database access hook:
- Triggered by database access operations, with minimum 10-second intervals between runs
- Removes `expired` pending bookings (where `expiresAt` < current time) and releases capacity
- Set status of `success` bookings to `noshow` when not checked in after `grace period` (where `arrivedAt` is null and `startTime` + grace period < current time) and releases capacity
- Uses direct database operations for efficiency
- Emits `booking.deleted` events for tracking
- Includes error handling and recovery


## Booking Flow

### Logical Flow
```mermaid
graph TB
    %% Clusters
    subgraph InitialRequest["Initial Request"]
        Start([Start])
        ValidateTime{Time Valid?}
        BookingRequest[Booking Request]
        Queue[Atomic Queue]
    end

    subgraph ResourceValidation["Resource Validation"]
        AvailabilityCheck{"Resources Available?"}
        ResourceType{"Resource Type"}
        SharedProcess["Split Quantity"]
        NonSharedProcess["Single Booking"]
    end

    subgraph BookingProcess["Booking Creation"]
        CreateBookings["Create Booking(s)"]
        UpdateCapacity[Update Capacity]
        EmitEvents[Emit Events]
        RollbackChanges[Rollback Changes]
    end

    subgraph StateManagement["State & Cleanup"]
        States["Status Updates:
        pending → open → success → ended
        reserved → ended
        (or cancelled/noshow/error)"]
        TTLManagement["TTL Management"]
        Cleanup["Auto Cleanup Process"]
    end

    %% Critical Flow Connections
    Start --> ValidateTime
    ValidateTime -->|Future Date| BookingRequest
    ValidateTime -->|Past Date| ErrorResponse
    BookingRequest --> Queue
    Queue --> AvailabilityCheck

    AvailabilityCheck -->|Yes| ResourceType
    AvailabilityCheck -->|No| ErrorResponse

    ResourceType -->|Shared| SharedProcess
    ResourceType -->|Non-shared| NonSharedProcess
    SharedProcess --> CreateBookings
    NonSharedProcess --> CreateBookings

    CreateBookings --> UpdateCapacity
    CreateBookings -->|Error| RollbackChanges
    RollbackChanges --> UpdateCapacity
    UpdateCapacity --> EmitEvents
    EmitEvents --> States

    States --> TTLManagement
    TTLManagement --> Cleanup
    Cleanup -.->|Release| UpdateCapacity

    %% Error Handling
    subgraph ErrorHandling["Error Handling"]
        ErrorResponse[Error Response]
    end

    ErrorResponse --> RollbackChanges
```

### Service Interactions
The system uses a distributed queue to process booking requests atomically, with the following service interactions:

```mermaid
sequenceDiagram
    participant C as Client

    box Sales Service
        participant Q as Queue
        participant B as Booking
        participant R as Resource Manager
        participant DB as Database
    end

    participant PS as Product Service

    Note over C: Request Flow

    C->>Q: Request booking
    activate Q

    Q->>B: Process request
    activate B

    B->>PS: Validate product rules
    PS-->>B: Product validation result

    B->>R: Check resource availability
    R->>DB: Query current bookings
    DB-->>R: Booking status
    R-->>B: Available capacity

    alt Non-shared Resource
        B->>DB: Create booking (status=pending, TTL set)
    else Shared Resource
        Note over B: Split requested quantity
        loop Until quantity fulfilled
        B->>DB: Create booking (status=pending, TTL set)
        end
    end

    B-->>Q: Request processed
    deactivate B

    Q-->>C: Return pending booking(s)
    deactivate Q

    Note over C: Confirmation Flow

    C->>B: Confirm booking
    activate B

    B->>DB: Update (status=open, clear TTL)

    B-->>C: Return confirmed booking
    deactivate B

    Note over B: Cleanup Process (via access hook, debounced 10s)
    opt Model access triggers cleanup
        B->>DB: Find expired & noshow bookings
        DB-->>B: Expired & noshow bookings list
        B->>DB: Delete expired & set noshow & release capacity
    end
```


## Tickets
Bookings can generate tickets (implemented as Offers) that serve as proof of booking and enable access control.

- Tickets are created automatically when a booking is confirmed
- Each ticket is a proof of booking for a single resource
- Tickets inherit validity period from booking's `startTime` and `endTime`
- Ticket lifecycle is tied to booking status (e.g., cancellations cascade)
- Integration with fulfillment system for delivery tracking

For details, see [Tickets Documentation](tickets.md).


## Integrations

1. **Booking API**
   - External interface for booking system
   - Handles incoming booking requests
   - Provides booking status queries

2. **Product Service API**
   - Provides product rules and validation
   - Manages capacity rules for resources
   - Consumes booking data through Booking API

3. **Calendar Integration (indirect)**
   - Integration via Product Service
   - Booking model stores event references:
     ```json
     "calendar": {
       "id": "Google calendarId",
       "eventId": "Google calendar eventId"
     }
     ```
   - Event-driven synchronization through lifecycle events


## Data Models
Key entities and relationships in the booking system:

```mermaid
erDiagram
    Product ||--o{ Resource : "maps to"
    Resource }|--|| Place : "belongs to"
    Product ||--o{ Booking : "has"
    Resource ||--o{ Booking : "reserves"
    Order ||--o{ Booking : "contains"
    Person ||--o{ Booking : "makes"
    Membership ||--o{ Booking : "associated_with"
    Place ||--o{ Booking : "hosts"
    Booking ||--o{ Offer : "issues"

    Booking {
        string id PK
        string kind "event|venue|table|person"
        string status "pending|open|success|reserved|cancelled|error|ended|noshow"
        date startTime "start time of the booking"
        date endTime "end time of the booking"
        number quantity "number of people/units (always 1 in implementation)"
        number capacity "resource capacity for this booking"
        number partySize "number of people in the party"
        number price "price of the booking"
        boolean deposit "if deposit is required"
        string source "queuing|website|staff (omitted: in-app)"
        object preferences "booking preferences (allowCombined, adjacentOnly)"
        string note "customer instructions/comments (max 180 chars)"
        object digitalCard "associated digital card info"
        object calendar "Google calendar integration (id, eventId)"
        date arrivedAt "time customer arrived"
        date departedAt "time customer departed"
        date expiresAt "time booking will be cancelled if not confirmed"
        string reservationId "Groups bookings from the same request"
        string productId FK
        string resourceId FK
        string placeId FK
        string orderId FK
        string personId FK
        string membershipId FK
        date createdAt
        date modifiedAt
    }

    Product {
        string id PK
        string name
        string description
        number price
        boolean active
    }

    Resource {
        string id PK
        string kind "event|venue|table|person"
        Hours hours
        boolean shared
        number capacity
        number leadTime
        string calendarId
        string productId FK
        string placeId FK
    }

    Order {
        string id PK
        string status
        number totalAmount
    }

    Place {
        string id PK
        string name
        object openingHours
    }

    Offer {
        string id PK
        string kind "ticket|discount|voucher|pickup"
        string state "pending|active|authorized|redeemed|expired"
        date startTime "start of validity"
        date endTime "end of validity"
        string bookingId FK
    }
```

### Key Entities

1. **Booking**
   - Core entity for resource reservations
   - A booking is for usage of one resource for a period of time
   - Properties: id, status, startTime, endTime, quantity, capacity, price, deposit, source, arrivedAt, departedAt, expiresAt, reservationId
   - The `reservationId` groups multiple booking records that originate from the same initial request, especially useful for shared resources where one request might generate several bookings.
   - Status flow: pending → open → success → ended (or cancelled/noshow/error), or reserved → ended
   - Links to:
     - Order, Product, Resource, Person, Membership (existing)
     - Place (via placeId)
     - Offers (via tickets relationship)

2. **Resource**
   - Represents bookable assets (rooms, equipment, events etc)
   - Types: shared (multiple concurrent bookings) or non-shared (exclusive use)
   - Properties: type, capacity, availability settings
   - Belongs to: Place (physical location)
   - Referenced by: Products, Bookings

3. **Product**
   - Bookable offerings that map to resources
   - Properties: name, description, price, active status
   - Can combine multiple resources into one bookable unit
   - Referenced by: Bookings, Orders

4. **Order**
   - Tracks commercial transactions
   - Properties: status, type, amount, currency
   - Contains: Items, Bookings, Fulfillments
   - Links to: Person (customer), Staff (handler), Business, Place

5. **Person**
   - Represents customers and users
   - Properties: name, contact info, birthDate
   - Makes: Bookings, Orders, Visits
   - Can have: Membership

6. **Membership**
   - Manages loyalty programs
   - Links to: Person, Bookings, Orders
   - Used for: special pricing, priority booking

7. **Place**
   - Represents physical locations hosting resources
   - Properties: operating hours, timezone, location details
   - Hosts: Resources and their associated Bookings

8. **Offer**
   - Represents a ticket as proof of entry for a booking
   - One ticket (offer with kind `ticket`) per booking
   - Properties: state, startTime, endTime
   - Links to: Booking (via bookingId)

### Key Relationships
- **Product ↔ Resource** (many:many): Products map to multiple resources through configuration
- **Product → Bookings** (1:many): Products have multiple bookings over time
- **Resource → Bookings** (1:many): Resources have multiple time-based bookings
- **Resource ↔ Place** (many:1): Resources belong to exactly one place
- **Place → Bookings** (1:many): Places host bookings through their resources
- **Booking → Offer** (1:1): Each booking can have one ticket (for a single resource)


## API EndPoints
For detailed API documentation, see [API.md](docs/API.md).

### Booking Model Endpoints
- `POST /Bookings/request` - Create a new booking request
- `POST /Bookings/confirm` - Confirm a pending booking
- `POST /Bookings/request/confirm` - Request and confirm a booking
- `POST /Bookings/cancel` - Cancel a booking
- `POST /Bookings/occupied` - Get occupied time slots for resources
- `GET /Bookings/pending/membership` - Get pending bookings for a membership
- `GET /Bookings/reservation/active` - Get active reservation for resource

### Product Model Booking Endpoints
- `POST /Products/app/booking/products/availability` - Check product availability for booking
- `POST /Products/app/booking/products/items` - Get items for adding to bag for booking
- `POST /Products/app/booking/items/extend` - Get items for extending booking

### Table Booking Endpoints
- `POST /Products/app/booking/tables/availability` - Check table availability
- `POST /Products/app/booking/tables` - Book tables for member
- `GET /Products/web/booking/tables/availability` - Get table availability matrix for place
- `POST /Products/web/booking/tables` - Book tables for non-member
- `DELETE /Products/app/booking/tables/:reservationId` - Cancel table booking

**Note:** The documented `/Orders/bookings` endpoints do not exist in the current implementation. Booking operations are handled through the `/Bookings` model endpoints and Product model mixins.


## Events
The booking system uses an event-driven architecture to handle various aspects of the booking lifecycle.

### Published Events
- `sales.booking.reservation.confirmed` - a reservation (group of bookings) is confirmed
- `sales.booking.reservation.cancelled` - a reservation is cancelled

Internal events:
- `booking.confirmed` - a booking is confirmed
- `booking.cancelled` - a booking is cancelled
- `booking.ended` - a booking is completed (after customer departed)
- `booking.noshow` - a booking becomes no-show (customer didn't arrive within grace period)
- `booking.deleted` - a booking is removed (cleanup of expired bookings)

### Subscribed Events
- `person.visit.arrive` - when a customer arrives (checked in) at the venue

### Event Details
**Note:** The implementation uses internal events (without `sales.` prefix) for most booking operations, and `sales.booking.reservation.*` events for reservation-level operations.

**Check-in/Check-out:** Customer arrival is handled through the `person.visit.arrive` event which updates the `arrivedAt` field. Check-out functionality sets the `departedAt` field but doesn't emit a specific check-out event.
