{"type": "pickup", "priority": "normal", "schedule": {"startTime": "2019-11-08T00:00:00.000Z", "endTime": "2019-11-09T00:00:00.000Z"}, "scheduledAt": "2019-11-08T00:00:00.000Z", "expireAt": "2019-11-09T00:00:00.000Z", "placeId": "5dc37839d6f4856efa671e36", "recipient": {"fullName": "<PERSON>", "phone": {"id": "k0v562k5", "fullNumber": "6598765432", "type": "home"}}, "state": "pending", "shipmentQuotesQueryStatus": {"requestStatus": "success", "numOfShippingMethodsFound": 1}, "shipmentQuotes": [{"id": "5dc37a12d6f4856efa671e39", "name": "In-store Pick Up", "label": "In-store Pick Up", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "carrier": "Flat Rate"}], "shipmentMethod": {"id": "5dc37a12d6f4856efa671e39", "name": "In-store Pick Up", "label": "In-store Pick Up", "group": "Free", "cost": 0, "handling": 0, "rate": 0, "enabled": true, "carrier": "Flat Rate"}, "shipmentStatus": "labelprinted", "customerNote": "test", "staff": {}, "paymentId": "5dc37931d6f4856efa671e37", "trackingId": "5dc3793dd6f4856efa671e38", "items": {"type": [{"id": "k2n9fer2", "productId": "5dc272e4cef06f4579164d1a", "variantId": "5dc2833bcef06f457924a010", "quantity": 1}]}}