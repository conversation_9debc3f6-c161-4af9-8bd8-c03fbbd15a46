# Receipt & Ticket Printing Documentation

## Overview

The system handles various types of printed documents:
1. Official Tax Invoices (Taiwan)
2. Order Receipts
3. Fulfillment Tickets
4. Kitchen Order Tickets (KOT)

## System Flow Visualization

```mermaid
graph TD
    %% Triggers
    A[Order Events] --> B{Print Trigger Type}
    B -->C[Automatic]
    B -->D[Manual]
    B -->E[Event]
    
    %% Trigger Specific Flows
    C --> F[Order Confirmation]
    C --> G[Payment Completion]
    C --> H[Order Assignment]
    
    D --> D1[Receipt Reprint]
    D --> D2[Tax Invoice Reissue]
    D --> D3[On-demand KOT]
    
    E --> E1[Order Modification]
    E --> E2[Order Cancellation]
    E --> E3[Refund Processing]
    
    %% Digital Updates for Events
    E1 --> DR1[Update Digital Receipt]
    E2 --> DR2[Update Digital Receipt]
    E3 --> DR3[Update Digital Receipt]
    
    DR1 --> DT[Digital Update Complete]
    DR2 --> DT
    DR3 --> DT
    
    %% Document Types by Trigger
    F --> I[New Receipt]
    F --> J[Kitchen Order Ticket]
    G --> K[Tax Invoice]
    H --> L[Delivery Ticket]
    
    D1 --> I2[Duplicate Receipt]
    D2 --> K2[Reissued Tax Invoice]
    D3 --> J2[Priority KOT]
    
    %% Printer Selection
    I --> M{Printer Type}
    I2 --> M
    J --> M
    J2 --> M
    K --> M
    K2 --> M
    L --> M
    
    M -->|Receipt Printer| N[Receipt Processing]
    M -->|Label Printer| O[Label Processing]
    
    %% Processing
    N --> P[Format Content]
    O --> P
    P --> Q[Apply Template]
    Q --> R[Print Document]
    
    %% Error Handling
    R --> S{Success?}
    S -->|Yes| T[Complete]
    S -->|No| U[Error Handling]
    U --> V[Retry/Fallback]
    V --> R
    
    %% Styling
    classDef trigger fill:#666,stroke:#333,stroke-width:2px
    classDef process fill:#4444bb,stroke:#333,stroke-width:2px
    classDef decision fill:#886644,stroke:#333,stroke-width:2px
    classDef endpoint fill:#446644,stroke:#333,stroke-width:2px
    classDef document fill:#6666bb,stroke:#333,stroke-width:2px
    classDef digital fill:#888,stroke:#333,stroke-width:2px
    
    class A,C,D,E trigger
    class F,G,H,D1,D2,D3,E1,E2,E3 process
    class I,I2,J,J2,K,K2,L document
    class DR1,DR2,DR3 digital
    class N,O,P,Q,R process
    class B,M,S decision
    class T,U,V,DT endpoint
```

## Print Triggers

### Automatic Triggers
- Order confirmation: Generates receipt and KOT
- Order status changes: Updates digital receipt
- Payment completion: Triggers tax invoice (Taiwan)
- Order assignment: Prints delivery ticket
- Item preparation: Triggers station-specific KOT

### Manual Triggers
- Receipt reprint by staff
  - For original receipts
  - For updated receipts (after modifications)
  - For void receipts
  - For refund receipts
- Tax invoice reissue (with cancellation of previous)
- On-demand KOT for specific stations
- Batch printing for unprinted tickets

### Event-Based Triggers
- Order modifications: Updates digital receipt
- Order cancellations: Updates digital receipt with void status
- Refund processing: Updates digital receipt with refund status

Note: Event-based triggers only update digital records in the system. Physical reprints of updated receipts must be manually triggered through the Manual Triggers if needed.

## Printer Types & Purposes

### Printer Types
- Label Printer
- Receipt Printer

### Printer Purposes
- Kitchen
- Receipt 
- Delivery

## Receipt Types

### Tax Invoice (Taiwan Only)
- Generated for business transactions
- Contains official tax information
- Requires merchant tax ID
- Can be cancelled with reason
- Can be skipped if `receipt.skipInvoice` is true

### Digital Receipt
- Sent through messaging system
- Supports multiple languages
- Updates based on order status
- Contains fulfillment tracking information

## Ticket Types

### Fulfillment Ticket
Content includes:
- Order type (delivery/pickup/dine-in)
- Queue/serial number
- Time information
- Delivery/pickup location
- Item list with prices
- Header/footer from settings

Reference implementation:

```javascript:server/mixins/PrintTicket.js
startLine: 23
endLine: 65
```

### Kitchen Order Ticket (KOT)
Types:
1. Receipt-style KOT
   - Complete order summary
   - Can include sub-tickets by station
   
2. Label-style KOT
   - Individual item labels
   - Sequential printing
   - Shows item count (e.g., "1/5")

Reference implementation:

```javascript:server/mixins/PrintTicket.js
startLine: 99
endLine: 148
```

## Printing Flow

### Receipt Printing
1. Check if receipt printing is enabled
2. Select appropriate printer
3. Format content based on type
4. Print with template
5. Handle errors and notifications

### Kitchen Ticket Printing
1. Check kitchen settings
2. Determine printer type (label/receipt)
3. Format content
4. Print main ticket
5. Print sub-tickets if configured
6. Print individual item tickets if required

## Templates

Templates are configured per:
- Order type
- Document type
- Language
- Store settings

Example template variables:

```javascript
{
  type: 'delivery',
  ticket: '1234',
  serial: 'A123',
  timeLabel: 'Delivery Time',
  time: '14:30',
  note: 'Leave at door',
  spot: {
    name: 'John Doe',
    address: '123 Street',
    postCode: '12345'
  },
  items: [
    {
      title: 'Product Name',
      quantity: 2,
      price: '$9.99'
    }
  ],
  header: 'Store Name',
  footer: 'Thank you!'
}
```

## Localization

Receipts and tickets support multiple languages:
- English (default)
- Traditional Chinese (Taiwan)
- Traditional Chinese (Hong Kong)
- Simplified Chinese

Language files location:

```javascript:server/lib/crm/i18n/receipt/
startLine: 1
endLine: 52
```

## Error Handling

The system includes:
- Print error catching
- Queue management for sequential printing
- Rate limiting for invoice creation
- Error notifications
- Fallback printer selection

## Configuration Settings

Settings that affect printing:
1. Fulfillment settings
   - Ticket templates
   - Headers/footers
   - Kitchen settings
   
2. Order settings
   - Invoice handling
   - Receipt templates
   - Merchant information

3. Kitchen settings
   - Sub-ticket configuration
   - Station routing
   - Print sequencing

## Best Practices

1. Queue Management
   - Use queue system for sequential printing
   - Implement rate limiting for invoice creation
   - Handle printer selection fallbacks

2. Error Handling
   - Catch and log all printer errors
   - Provide clear error notifications
   - Implement retry mechanisms

3. Template Management
   - Keep templates in version control
   - Test templates with various data scenarios
   - Maintain language-specific templates

4. Kitchen Operations
   - Configure station-specific printers
   - Set up clear item routing rules
   - Test print sequencing
