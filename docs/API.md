# API Documentation - Sales Service (CRM)

The Sales Service provides comprehensive APIs for managing orders, fulfillment, payments, and kitchen 
operations across multiple channels in the CRM platform.

## Table of Contents
- [Order Management](#order-management)
  - [Create Order](#create-order)
  - [Mark Order as Paid](#mark-order-as-paid)
  - [Cancel Order](#cancel-order)
  - [Get Order by Transaction](#get-order-by-transaction)
  - [Get Order Payment Status](#get-order-payment-status)
- [Fulfillment Operations](#fulfillment-operations)
  - [Request Order Fulfillment](#request-order-fulfillment)
  - [Cancel Order Fulfillment](#cancel-order-fulfillment)
  - [Calculate Shipping Rates](#calculate-shipping-rates)
  - [Get Pickup Times](#get-pickup-times)
- [Booking Operations](#booking-operations)
  - [Request Booking](#request-booking)
  - [Confirm Booking](#confirm-booking)
  - [Request and Confirm Booking](#request-and-confirm-booking)
  - [Cancel Booking](#cancel-booking)
  - [Check Occupied Slots](#check-occupied-slots)
- [Kitchen Operations](#kitchen-operations)
  - [Get Preparation List](#get-preparation-list)
  - [Get Packing List](#get-packing-list)
  - [Get Kitchen Fulfillment Status](#get-kitchen-fulfillment-status)
  - [Start Preparation](#start-preparation)
  - [Mark as Packed](#mark-as-packed)
  - [Update Item Fulfillment](#update-item-fulfillment)
  - [Priority Queue Fulfillment](#priority-queue-fulfillment)
  - [Cancel Preparation](#cancel-preparation)
  - [Print Kitchen Order Ticket](#print-kitchen-order-ticket)
  - [Mark as Delivered](#mark-as-delivered)
- [Staff Operations (Widgets)](#staff-operations-widgets)
  - [Get Store Orders Summary](#get-store-orders-summary)
  - [Get Open Orders](#get-open-orders)
  - [Get Unpaid Orders](#get-unpaid-orders)
  - [Get Paid Orders](#get-paid-orders)
  - [Get All Store Orders](#get-all-store-orders)
  - [Reissue Invoice](#reissue-invoice)
  - [Create/Print Invoice](#createprint-invoice)
- [Shopify Apps](#shopify-apps)
  - [Get Sales Summary](#get-sales-summary)
  - [Get Stored Value Metrics History](#get-stored-value-metrics-history)

## Order Management

### Create Order
Creates a new order in the system.

```http
POST /orders/create
```

**Request Body:**
```json
{
	"id": "string",
	"receipt": {
		"type": "object"
	},
	"quantity": "number",
	"currency": "string",
	"discountAmount": "number",
	"subtotalPrice": "number",
	"shippingPrice": "number",
	"amount": "number",
	"taxIncluded": "boolean",
	"taxes": "object",
	"taxAmount": "number",
	"itemList": [
		{
			"id": "string",
			"kind": "string",
			"quantity": "number",
			"price": "number",
			"discountAmount": "number",
			"tax": "number"
		}
	],
	"discountList": [],
	"billingList": [],
	"fulfillmentList": [],
	"personId": "string",
	"memberId": "string",
	"membershipId": "string",
	"storeId": "string",
	"when": {
		"type": "object"
	},
	"expiresAt": "date"
}
```

**Response:**
```json
{
	"id": "string",
	"status": "string",
	"type": "string",
	"amount": "number",
	"currency": "string",
	"createdAt": "date",
	"updatedAt": "date"
}
```

### Mark Order as Paid
Marks an order as paid.

```http
POST /orders/paid
```

**Request Body:**
```json
{
	"data": {
		"updates": "object",
		"billingList": "array",
		"itemList": "array",
		"membershipId": "string"
	}
}
```

**Response:**
```json
{
	"id": "string",
	"status": "string",
	"when": {
		"paid": "date"
	}
}
```

### Cancel Order
Cancels an existing order.

```http
POST /orders/cancel
```

**Request Body:**
```json
{
	"id": "string",
	"options": {
		"type": "object"
	}
}
```

**Response:**
```json
{
	"id": "string",
	"status": "cancelled",
	"when": {
		"cancelled": "date"
	}
}
```

### Get Order by Transaction
Retrieves order details using a transaction ID.

```http
GET /orders/byTransaction
```

**Query Parameters:**
- `transactionId` (string, required): The transaction ID
- `sourceType` (string, optional): Business | Program | Variant

**Response:**
```json
{
	"id": "string",
	"status": "string",
	"amount": "number",
	"currency": "string",
	"transactionId": "string"
}
```

### Get Order Payment Status
Gets the payment status of an order.

```http
GET /orders/payment/status
```

**Response:**
```json
{
	"status": "string",
	"paid": "boolean",
	"amount": "number",
	"currency": "string"
}
```

## Fulfillment Operations

### Request Order Fulfillment
Requests fulfillment for an order.

```http
POST /orders/fulfillments/request
```

**Response:**
```json
[
	{
		"id": "string",
		"type": "string",
		"status": "string",
		"orderId": "string"
	}
]
```

### Cancel Order Fulfillment
Cancels fulfillment for an order.

```http
POST /orders/fulfillments/cancel
```

**Request Body:**
```json
{
	"items": [
		{
			"id": "string",
			"quantity": "number"
		}
	]
}
```

**Response:**
```json
{
	"status": "cancelled",
	"when": {
		"cancelled": "date"
	}
}
```

### Calculate Shipping Rates
Calculates shipping rates for delivery.

```http
POST /shipping/rates
```

**Request Body:**
```json
{
	"origin": {
		"type": "object",
		"required": true
	},
	"destination": {
		"type": "object",
		"required": true
	},
	"items": {
		"type": "array",
		"required": true
	},
	"currency": {
		"type": "string",
		"required": true
	}
}
```

**Response:**
```json
{
	"rates": [
		{
			"provider": "string",
			"service": "string",
			"amount": "number",
			"currency": "string",
			"estimatedDays": "number"
		}
	]
}
```

### Get Pickup Times
Gets estimated pickup times for items at a location.

```http
POST /pickup/times
```

**Request Body:**
```json
{
	"placeId": "string",
	"items": [
		{
			"id": "string",
			"quantity": "number"
		}
	],
	"options": {
		"type": "object"
	}
}
```

**Response:**
```json
{
	"earliest": "date",
	"latest": "date",
	"slots": [
		{
			"from": "date",
			"until": "date",
			"available": "boolean"
		}
	]
}
```

## Booking Operations

### Request Booking
Creates a new booking request for specified resources.

```http
POST /bookings/request
```

**Parameters:**
- `products`: Array of products to book
  - `id`: Product identifier
  - `resourceId`: Resource identifier
  - `options`: Additional booking options
    - `priority`: Booking priority (1-5)
    - `notes`: Additional booking notes
- `from`: Start time of the booking
- `to`: End time of the booking
- `quantity`: Number of units to book
- `note` (optional): Special requests from customer
- `price` (optional): Price validation amount
- `ttl` (optional): Time-to-live in milliseconds for auto-cancellation of pending bookings
- `personId` (optional): Associated person identifier
- `membershipId` (optional): Associated membership identifier for tracking

**Request Body:**
```json
{
  "products": [{
    "id": "string",
    "resourceId": "string",
    "options": {
      "priority": "number",
      "notes": "string"
    }
  }],
  "from": "date",
  "to": "date",
  "quantity": "number",
  "price": "number",
  "ttl": "number"
}
```

**Response:**
```json
[
  {
    "id": "string",
    "status": "string",
    "resourceId": "string",
    "productId": "string",
    "from": "date",
    "to": "date",
    "quantity": "number",
    "price": "number",
    "createdAt": "date",
    "updatedAt": "date"
  }
]
```

**Error Responses:**
```json
{
  "statusCode": 400,
  "message": "Invalid booking time range",
  "details": {
    "code": "INVALID_TIME_RANGE",
    "from": "2024-01-01T00:00:00Z",
    "to": "2023-12-31T00:00:00Z",
    "reason": "End time must be after start time"
  }
}

{
  "statusCode": 409,
  "message": "Resource unavailable",
  "details": {
    "code": "RESOURCE_UNAVAILABLE",
    "resourceId": "resource123",
    "conflictingBookings": ["booking123", "booking124"]
  }
}

{
  "statusCode": 409,
  "message": "Capacity exceeded",
  "details": {
    "code": "CAPACITY_EXCEEDED",
    "resourceId": "resource123",
    "requested": 5,
    "available": 3,
    "period": {
      "from": "2024-01-01T00:00:00Z",
      "to": "2024-01-01T02:00:00Z"
    }
  }
}
```

### Confirm Booking
Confirms a pending booking.

```http
POST /bookings/confirm
```

**Parameters:**
- `id`: Booking identifier to confirm
- `orderId` (optional): Associated order identifier
- `personId` (optional): Associated person identifier
- `membershipId` (optional): Associated membership identifier for tracking

**Request Body:**
```json
{
  "id": "string",
  "orderId": "string",
  "personId": "string",
  "membershipId": "string"
}
```

**Response:**
```json
{
  "booking": {
    "id": "string",
    "status": "success",
    "resourceId": "string",
    "productId": "string",
    "from": "date",
    "to": "date",
    "quantity": "number",
    "price": "number",
    "orderId": "string",
    "personId": "string",
    "membershipId": "string",
    "createdAt": "date",
    "updatedAt": "date"
  },
  "calendar": {
    "eventId": "string",
    "link": "string"
  }
}
```

**Error Responses:**
```json
{
  "statusCode": 400,
  "message": "Invalid booking state for confirmation",
  "details": {
    "code": "INVALID_BOOKING_STATE",
    "bookingId": "booking123",
    "currentState": "cancelled",
    "allowedStates": ["pending", "open"]
  }
}
```

### Request and Confirm Booking
Creates and confirms a booking in a single operation. This is a convenience method that combines the request and confirm steps, with automatic rollback if any part fails.

```http
POST /bookings/request/confirm
```

**Parameters:**
- `products`: Array of products to book
  - `id`: Product identifier
  - `resourceId`: Resource identifier
  - `options` (optional): Additional booking options
    - `priority` (optional): Booking priority (1-5)
    - `notes` (optional): Additional booking notes
- `from`: Start time of the booking
- `to`: End time of the booking
- `quantity`: Number of units to book
- `note` (optional): Special requests from customer
- `price` (optional): Price validation amount
- `personId` (optional): Associated person identifier
- `membershipId` (optional): Associated membership identifier for tracking

**Request Body:**
```json
{
  "products": [{
    "id": "string",
    "resourceId": "string",
    "options": {
      "priority": "number",
      "notes": "string"
    }
  }],
  "from": "date",
  "to": "date",
  "quantity": "number",
  "price": "number",
  "personId": "string",
  "membershipId": "string"
}
```

**Response:**
```json
[
  {
    "id": "string",
    "status": "success",
    "resourceId": "string",
    "productId": "string",
    "startTime": "date",
    "endTime": "date",
    "quantity": "number",
    "personId": "string",
    "membershipId": "string",
    "createdAt": "date",
    "updatedAt": "date"
  }
]
```

**Error Handling:**
- Includes all error responses from request and confirm operations
- If any booking confirmation fails, all previously confirmed bookings are automatically cancelled (rollback)

**Error Responses:**
```json
{
  "statusCode": 400,
  "message": "Invalid booking time range",
  "details": {
    "code": "INVALID_TIME_RANGE",
    "from": "2024-01-01T00:00:00Z",
    "to": "2023-12-31T00:00:00Z",
    "reason": "End time must be after start time"
  }
}

{
  "statusCode": 409,
  "message": "Resource unavailable",
  "details": {
    "code": "RESOURCE_UNAVAILABLE",
    "resourceId": "resource123",
    "conflictingBookings": ["booking123", "booking124"]
  }
}

{
  "statusCode": 409,
  "message": "Capacity exceeded",
  "details": {
    "code": "CAPACITY_EXCEEDED",
    "resourceId": "resource123",
    "requested": 5,
    "available": 3,
    "period": {
      "from": "2024-01-01T00:00:00Z",
      "to": "2024-01-01T02:00:00Z"
    }
  }
}
```

### Cancel Booking
Cancels (deletes) a confirmed or pending booking from the system.

```http
POST /bookings/cancel
```

**Parameters:**
- `id`: Booking identifier to cancel
- `reason` (optional): Reason for cancellation

**Request Body:**
```json
{
  "id": "string",
  "reason": "string"
}
```

**Response:**
```json
{
  "id": "string",
  "status": "cancelled",
  "resourceId": "string",
  "productId": "string",
  "from": "date",
  "to": "date",
  "quantity": "number",
  "createdAt": "date",
  "updatedAt": "date"
}
```

### Check Occupied Slots
Retrieves occupied booking records for the given resources.

```http
POST /bookings/occupied
```

**Parameters:**
- `resourceIds` (array[string], required): Array of resource identifiers to check
- `from` (date, required): Start time of the period to check
- `to` (date, required): End time of the period to check

**Request Body:**
```json
{
  "resourceIds": ["string"],
  "from": "date",
  "to": "date"
}
```

**Response:**
```json
{
  "slots": [{
    "resourceId": "string",
    "periods": [{
      "from": "date",
      "to": "date",
      "bookingId": "string",
      "quantity": "number",
      "available": "number"
    }]
  }],
  "meta": {
    "totalSlots": "number",
    "fullyBooked": "number"
  }
}
```

## Kitchen Operations

### Get Preparation List
Gets the list of items to prepare.

```http
GET /staff/fulfillments/prepare
```

**Query Parameters:**
- `type` (string, optional): Type of fulfillment, default 'kitchen'
- `stations` (array[string], optional): For these stations (name)

**Response:**
```json
{
	"fulfillments": [
		{
			"id": "string",
			"type": "string",
			"status": "string",
			"itemList": [
				{
					"id": "string",
					"product": "object",
					"variant": "object",
					"quantity": "number"
				}
			],
			"prepare": {
				"startTime": "date",
				"endTime": "date"
			}
		}
	]
}
```

### Get Packing List
Gets the packing list for a store.

```http
GET /staff/fulfillments/pack
```

**Query Parameters:**
- `type` (string, optional): Type of fulfillment, default 'kitchen'
- `stations` (array[string], optional): For these stations (name)

**Response:**
```json
{
	"fulfillments": [
		{
			"id": "string",
			"type": "string",
			"status": "string",
			"itemList": [
				{
					"id": "string",
					"product": "object",
					"variant": "object",
					"quantity": "number",
					"packed": "boolean"
				}
			]
		}
	]
}
```

### Get Kitchen Fulfillment Status
Gets kitchen fulfillment status.

```http
GET /staff/fulfillments/kitchen
```

**Query Parameters:**
- `mainFulfillmentId` (string, required): Main fulfillment ID

**Response:**
```json
{
	"id": "string",
	"type": "kitchen",
	"status": "string",
	"prepare": {
		"startTime": "date",
		"endTime": "date",
		"status": "string"
	},
	"itemList": [
		{
			"id": "string",
			"status": "string",
			"quantity": "number",
			"preparedAt": "date",
			"packedAt": "date",
			"preparation": "object"
		}
	]
}
```

### Start Preparation
Starts preparation of a fulfillment.

```http
POST /staff/fulfillments/:id/prepare
```

**Response:**
```json
{
	"id": "string",
	"status": "preparing",
	"when": {
		"prepare": "date"
	}
}
```

### Mark as Packed
Marks a fulfillment as packed.

```http
POST /staff/fulfillments/:id/packed
```

**Response:**
```json
{
	"id": "string",
	"status": "packed",
	"when": {
		"packed": "date"
	}
}
```

### Update Item Fulfillment
Updates the fulfillment status of specific items.

```http
POST /staff/fulfillments/:id/fulfill/items
```

**Request Body:**
```json
{
	"items": [
		{
			"id": "string",
			"quantity": "number"
		}
	]
}
```

**Response:**
```json
{
	"id": "string",
	"status": "string",
	"itemList": [
		{
			"id": "string",
			"status": "fulfilled",
			"quantity": "number",
			"fulfilledAt": "date"
		}
	]
}
```