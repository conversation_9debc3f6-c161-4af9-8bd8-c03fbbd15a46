# Vending Machine Ordering and Fulfillment Documentation

## Overview
The vending machine fulfillment system is an automated dispensing solution that handles product ordering and fulfillment through self-service machines. The system supports multiple purposes including direct ordering, pickup, and redemption.

```mermaid
flowchart TD
    Customer([Customer])
    
    subgraph System["Vending Machine System"]
        VM[Vending Machine]
        CRM[CRM System] 
        INV[Inventory System]
        PAY[Payment System]
    end
    
    Customer -->|1.Place Order| CRM
    CRM -->|2.Process Order| VM
    VM -->|3.Update Stock| INV 
    VM -->|4.Process Payment| PAY
    VM -->|5.Dispense| Customer
    
    classDef machine fill:#456345,stroke:#333,stroke-width:2px
    classDef crm fill:#3d547a,stroke:#333,stroke-width:2px
    classDef inventory fill:#3d633d,stroke:#333,stroke-width:2px
    classDef payment fill:#7a3d7a,stroke:#333,stroke-width:2px
    
    class VM machine
    class CRM crm
    class INV inventory
    class PAY payment
```

## Vending Machine Types and Purposes
- **Type**: Standard
- **Purposes**:
  - `order`: Direct product purchase from machine
  - `pickup`: Collection point for orders placed elsewhere
  - `redeem`: Redemption of vouchers or rewards

```mermaid
flowchart LR
    Customer([Customer])
    VM[Vending Machine]
    Order[Order]
    Pickup[Pickup]
    Redeem[Redeem]

    VM --> Order
    VM --> Pickup
    VM --> Redeem

    Order -->|Direct Purchase| Customer
    Pickup -->|Collect Orders| Customer
    Redeem -->|Vouchers/Rewards| Customer

    classDef machine fill:#456345,stroke:#333,stroke-width:2px
    classDef option fill:#3d547a,stroke:#333,stroke-width:2px

    class VM machine
    class Order,Pickup,Redeem option
```

## Order Flow

### 1. Order Creation
1. Customer places an order
2. System creates a fulfillment record with type `vending`
3. Order is associated with a specific machine via `machineId`
4. System generates necessary digital access codes or credentials

### 2. Fulfillment Steps
The vending machine fulfillment follows these steps:
1. `ordered`: Initial order creation
2. `requested`: Fulfillment request sent to machine
3. `allocated`: Resources reserved in machine
4. `collected`: Items dispensed to customer

```mermaid
sequenceDiagram
    actor C as Customer
    participant O as Order System
    participant V as Vending Machine
    participant I as Inventory

    C->>O: Place Order
    O->>V: Create Fulfillment
    V->>I: Check Inventory
    I-->>V: Confirm Stock
    V->>O: Allocate Resources
    O->>C: Send Access Code
    C->>V: Present Code
    V->>C: Dispense Items
    V->>I: Update Inventory
```

### 3. Machine Integration
- Each vending machine has a unique `machineId`
- Machines are integrated with the CRM system
- Location tracking via coordinates and address
- Real-time inventory management
- Digital access control

## Event System

### Key Events
1. **Machine Request Event**
```javascript
Event.fulfillment.machine.requested
```
- Triggered when order is sent to machine
- Contains order details and machine context
- Includes customer credentials if needed

2. **Collection Events**
- Success: `handleExtOrderFulfillmentCollected`
- Failure: `handleExtOrderFulfillmentFailed`

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Ordered
    Ordered --> Requested: Create Request
    Requested --> Allocated: Reserve Stock
    Allocated --> Collected: Dispense
    Allocated --> Failed: Error
    Failed --> [*]
    Collected --> [*]
```

### Event Data Structure
```javascript
{
	type: "vending",
	destination: {
		type: "vending",
		placeId: "[machine_id]",
		external: {
			vending: {
				machineId: "[machine_id]"
			}
		}
	}
}
```

## Pickup Flow
For orders designated for vending machine pickup:

```mermaid
sequenceDiagram
    actor C as Customer
    participant O as Online Store
    participant V as Vending Machine
    
    C->>O: Place Order
    O->>V: Assign to Machine
    O->>C: Send Pickup Code
    C->>V: Enter Code
    V->>V: Verify Code
    V->>C: Dispense Items
```

1. **Order Creation**
   - Order placed through other channels
   - Vending machine selected as pickup point

2. **Pickup Assignment**
   - System assigns pickup to specific machine
   - Generates pickup credentials
   - Sets pickup time window if applicable

3. **Customer Collection**
   - Customer authenticates at machine
   - Machine verifies credentials
   - Products are dispensed

## Error Handling
The system includes robust error handling:
- Failed dispensing attempts
- Machine offline scenarios
- Inventory discrepancies
- Authentication failures

```mermaid
graph TD
    Error[Error Detected] --> Type{Error Type}
    Type -->|Dispensing| Retry[Retry Dispensing]
    Type -->|Offline| Notify[Notify Support]
    Type -->|Inventory| Update[Update Stock]
    Type -->|Auth| Reset[Reset Auth]

    Retry --> Success
    Retry --> Fail[Manual Intervention]
    Update --> Success
    Reset --> Success

    style Error fill:#7a2323,stroke:#333,stroke-width:2px
    style Success fill:#234023,stroke:#333,stroke-width:2px
    style Fail fill:#7b4a2e,stroke:#333,stroke-width:2px
```

## Integration Points
1. **CRM System**
   - Customer management
   - Order history
   - Machine location mapping

2. **Inventory System**
   - Real-time stock levels
   - Automatic reordering
   - Stock alerts

3. **Payment System**
   - Direct payment processing
   - Digital wallet integration
   - Receipt generation

```mermaid
graph TB
    VM[Vending Machine] --> CRM
    VM --> INV
    VM --> PAY

    subgraph CRM[CRM System]
        CM[Customer Mgmt]
        OH[Order History]
        LM[Location Mapping]
    end

    subgraph INV[Inventory System]
        SL[Stock Levels]
        AR[Auto Reorder]
        AL[Alerts]
    end

    subgraph PAY[Payment System]
        DP[Direct Payment]
        DW[Digital Wallet]
        RG[Receipts]
    end

    style VM fill:#456345,stroke:#333,stroke-width:2px
    style CRM fill:#3d547a,stroke:#333,stroke-width:2px
    style INV fill:#3d633d,stroke:#333,stroke-width:2px
    style PAY fill:#7a3d7a,stroke:#333,stroke-width:2px
```

## Machine Management
1. **Location Tracking**
   - Geographic coordinates
   - Physical address
   - Place identification

2. **Status Monitoring**
   - Operational status
   - Inventory levels
   - Temperature control
   - Error states

3. **Access Control**
   - Authentication methods
   - Digital access codes
   - Security protocols

```mermaid
graph TB
    subgraph "Machine Management"
        direction TB
        
        subgraph "Location"
            GEO[Coordinates]
            ADD[Address]
            ID[Place ID]
        end

        subgraph "Monitoring"
            OP[Operations]
            INV[Inventory]
            TEMP[Temperature]
            ERR[Errors]
        end

        subgraph "Security"
            AUTH[Authentication]
            CODE[Access Codes]
            PROT[Protocols]
        end
    end

    style Location fill:#3d547a,stroke:#333,stroke-width:2px
    style Monitoring fill:#3d633d,stroke:#333,stroke-width:2px
    style Security fill:#7a3d7a,stroke:#333,stroke-width:2px
```
