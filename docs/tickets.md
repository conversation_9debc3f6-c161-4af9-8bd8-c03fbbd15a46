# Tickets System

## Overview
The Tickets system is an integral part of the Sales Service that manages digital proofs of booking and access control. Tickets are implemented as Offers and serve as the bridge between bookings and fulfillment/access control systems.

## Table of Contents
1. [Core Concepts](#core-concepts)
2. [Ticket Lifecycle](#ticket-lifecycle)
3. [States and Transitions](#states-and-transitions)
4. [Integrations](#integrations)
5. [Data Model](#data-model)
6. [API Endpoints](#api-endpoints)

## Core Concepts

### Purpose
- Proof of booking for customers
- Access control management
- Usage tracking
- Fulfillment handling

### Implementation
Tickets are implemented as Offers with:
- Direct relationship to parent booking (`hasMany`)
- Inherited validity period from booking timeframe
- Unique identifiers for tracking (`ids` and `digitalIds`)
- Integration with fulfillment system

## Ticket Lifecycle

### 1. Creation
- Automatically generated upon booking confirmation
- Inherits validity period from booking
- Multiple tickets possible per booking
- Initial state: `valid`

### 2. Distribution
- Delivery via Perkd App of the Perkd platform
- Tracked through `FulfillItem` entities

### 3. Usage
- Validation at entry points
- NFC check-in via Perkd App (`redeemed`)
- Real-time status tracking

### 4. Completion
- Status updates based on usage
- Automatic expiration handling
- Integration with booking completion

## States and Transitions

```mermaid
stateDiagram-v2
    [*] --> valid: Creation
    valid --> redeemed: Use for Entry
    valid --> cancelled: Booking Cancelled
    valid --> expired: Time Elapsed
    redeemed --> reverted: Redemption Reversed
    redeemed --> extended: Validity Modified
    cancelled --> [*]
    expired --> [*]
    reverted --> valid: Reset
    extended --> valid: New Period
```

### State Descriptions
- `valid`: Active and ready for use
- `redeemed`: Used for entry/access
- `cancelled`: Invalidated (linked to booking cancellation)
- `reverted`: Redemption reversed (e.g., entry mistake)
- `extended`: Validity period modified
- `expired`: Beyond valid timeframe


## Integrations

### 1. Booking System
- Creation trigger on booking confirmation
- Automatic cancellation on booking changes
- Validity period synchronization
- Capacity tracking

### 2. Fulfillment System
- Delivery tracking via `FulfillItem`
- Status updates
- Distribution channel management
- Delivery confirmation

### 3. Offer Service
- Offer creation and management
- Offer redemption tracking
- Offer status updates


## Data Model

```mermaid
erDiagram
    Booking ||--o{ Offer : "issues"
    Offer ||--o{ FulfillItem : "tracked_by"
    FulfillItem }|--|| Fulfillment : "belongs_to"
    
    Offer {
        string id PK
        string kind "ticket"
        date startTime
        date endTime
        string state
        object digital
        string bookingId FK
        string orderId FK
    }

    FulfillItem {
        string id PK
        string offerId FK
        string fulfillmentId FK
        string status
        string channel
        date deliveredAt
    }
```

## API Endpoints
For details, see [Offer API](offer-api.md) of the Offer Service.
