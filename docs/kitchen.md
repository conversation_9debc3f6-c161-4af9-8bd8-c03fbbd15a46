# Kitchen Scheduling System

The Kitchen Scheduling System manages the preparation timing and scheduling of kitchen orders, optimizing kitchen workflow while maintaining order constraints and quality standards.

## System Architecture

```mermaid
graph TB
    Order((New Order)) --> RH

    subgraph Kitchen Scheduling System
        RH[Request Handler]
        SA[Schedule Allocation]
        TSM[Time Slot Management]
        OS[Optimization System]
        PT[Preparation Timer]
        QM[Queue Management]
        
        QM --> RH
        RH --> SA
        RH --> TSM
        SA --> TSM
        TSM --> OS
        OS --> SA
        SA --> PT
    end

    subgraph Time Slot Management
        OCS[Occupied Slots]
        AS[Available Slots]
        OCS --> AS
    end

    subgraph Constraints
        SW[Schedule Window]
        SL[Shelf Life]
        FE[First Item ETA]
        
        SW --> SA
        SL --> SA
        FE --> SA
    end

    PT --> KP[Kitchen Preparation]
    KP --> Ready((Order Ready))

    style Order fill:#800080,stroke:#333,stroke-width:4px
    style Ready fill:#006400,stroke:#333,stroke-width:4px
```

## Core Components

### 1. Request Handler
The entry point for new kitchen fulfillment requests. Handles:
- Validation of incoming requests
- Schedule allocation
- Updates to fulfillment records
- Metrics tracking

### 2. Schedule and Queue Management

#### Schedule Allocation
Core scheduling and queuing logic that determines when orders should be prepared:

- **Scheduling Details**
	- `startTime`: When preparation should begin
	- `endTime`: When preparation should complete
	- `firstEta`: Expected time first item will be ready
	- `duration`: Total slot duration
	- `preparationTime`: Required preparation time
	- `shelfLife`: Maximum time items can remain after preparation

- **Queue Management**
	- Maintains tenant-specific preparation queues
	- Handles concurrent preparation limits
	- Generates queue numbers automatically
	- Supports priority-based ordering
	- Real-time queue adjustments based on kitchen load

#### Time Slot Management
- **Occupied Slots**: Tracks existing kitchen commitments
```javascript
Kitchen.occupied(placeId, from, to)
```

- **Available Slots**: Calculates open time slots
```javascript
Kitchen.availableSlots(occupied, from, to)
```

#### Timer System
Manages precise timing of order preparations:

- **Core Features**
	- Dynamic lookahead window by order type
	- Adaptive timer intervals (min 3000ms)
	- Automatic timer cleanup
	- Fault tolerance for missed events

- **Configuration**
```javascript
{
	kitchen: {
		enabled: true,
		prepareLookahead: 600000,  // 10 minutes
		minTimer: 5000,            // 5 seconds
		autoPack: true,
		maxRetries: 3,
		retryDelay: 30000
	}
}
```

### 3. Optimization System

### Schedule Optimization

```mermaid
flowchart TB
    %% Triggers
    A[Early Order Completion] & B[Order Cancellation] & C[Manual Request] --> Start
    Start((Start Optimization))
    Start --> Analysis

    subgraph Stage 1: Schedule Analysis
        Analysis{Check Schedule}
        Analysis --> D1[Find Next Open Order]
        Analysis --> D2[Calculate Available Window]
        note1[Time between now<br>and next order start]
        note1 -.-> D2
    end

    subgraph Stage 2: Candidate Selection
        D1 & D2 --> E[Get Non-Queued Orders]
        E --> F[Filter Priority Orders]
        F --> G[Find Best Fit]
        note2[STORE/DINEIN orders that<br>fit within available window]
        note2 -.-> F
    end

    subgraph Stage 3: Time Adjustment
        G --> H1[Calculate Time Offset]
        H1 --> H2[Update Schedule Times]
        note3[Adjust startTime, endTime,<br>minTime, maxTime, firstEta]
        note3 -.-> H2
    end

    subgraph Stage 4: Updates
        H2 --> I1[Update Selected Fulfillment]
        H2 --> I2[Update Main Fulfillment]
        I1 & I2 --> J[Trigger Preparation]
    end

    J --> End((End))
```

The optimization system continuously improves kitchen efficiency through several mechanisms:

1. **Order Shifting**
- Moves STORE/DINEIN orders earlier when capacity allows
- Preserves DELIVERY/PICKUP scheduled times
- Maintains preparation sequence constraints
- Respects shelf life limitations

2. **Gap Management** 
- Identifies and fills schedule gaps
- Consolidates small gaps into usable slots
- Prevents fragmentation of kitchen capacity

3. **Wait Time Reduction**
- Prioritizes orders with longer wait times
- Balances preparation timing with order freshness
- Adjusts scheduling based on real-time kitchen load

```javascript
Kitchen.optimize(placeId, from)
```

### Preparation Timer
Manages the precise timing of order preparation starts through a sophisticated timer system.

Key Features:
- Dynamic lookahead window based on order type and volume
- Adaptive timer intervals (minimum 3000ms)
- Tenant-specific configuration options
- Automatic cleanup of completed timers
- Fault tolerance for missed timer events

#### Preparation Timer Flow

```mermaid
flowchart TB
    Start((Timer Start)) --> CheckPrep[Check Due Preparations]
    
    subgraph Preparation Check
        CheckPrep --> FindFulfillments[Find Due Fulfillments<br>startTime <= now + prepareLookahead]
        FindFulfillments --> HasFulfillments{Any Fulfillments<br>to Prepare?}
        HasFulfillments -->|No| NextTimer
        HasFulfillments -->|Yes| SortFulfillments[Sort Fulfillments by Priority:<br>DINEIN fulfillments first<br>then by startTime ASC]
        
        SortFulfillments --> ProcessFulfillments[Process Each Fulfillment]
        ProcessFulfillments --> PrepareFulfillment[Prepare Fulfillment]
        PrepareFulfillment --> NextFulfillment{More Fulfillments?}
        NextFulfillment -->|Yes| ProcessFulfillments
        NextFulfillment -->|No| NextTimer
    end
    
    subgraph Next Timer Setup
        NextTimer[Find Next Fulfillment] --> HasNext{Has Next Fulfillment?}
        HasNext -->|No| CleanupTimer[Cleanup Timer]
        HasNext -->|Yes| CalcTimeout[Calculate Timeout<br>timeout = max&#40;startTime - prepareLookahead/2 - now, minTimer&#41;]
        
        note1[prepareLookahead: 600000ms &#40;10min&#41;<br>minTimer: 3000ms]
        note1 -.-> CalcTimeout
        
        CalcTimeout --> SetTimer[Set Next Timer<br>Clear existing timer<br>Set TIMERS&#91;tenant&#93; = setTimeout&#40;&#41;]
        
        note2[TIMERS: tenant-specific<br>preparation timers]
        note2 -.-> SetTimer
    end
    
    subgraph Timer Cleanup
        CleanupTimer --> ClearTimer[Clear existing timer<br>Delete from TIMERS&#91;tenant&#93;]
    end
    
    ClearTimer --> End((Timer End))
    SetTimer --> End
```

The flowchart illustrates the preparation timer's operation:
1. **Preparation Check**: Finds and processes fulfillments due for preparation
   - Fulfillments are sorted to prioritize DINEIN fulfillments
   - All fulfillments within the lookahead window are prepared
2. **Next Timer Setup**: Schedules the next timer check
   - Uses same lookahead time for all fulfillment types
   - Ensures minimum interval between checks
3. **Timer Cleanup**: Handles timer cleanup when no fulfillments pending

### Optimization Triggers
The system initiates optimization in response to:
- Order cancellations and modifications
- Early order completions and adjustments
- Manual optimization requests via API
- Timer-based preparation events

### Performance Monitoring
The optimization system tracks:
- Schedule density metrics
- Gap utilization rates
- Order wait times
- Kitchen capacity usage
- Optimization success rates

## Time Constraints

The system enforces several time-based constraints:

1. **Schedule Window**
   - Maximum time window for advance scheduling
   - Prevents orders being scheduled too far in advance

2. **Shelf Life**
   - Maximum time items can remain after preparation
   - Ensures food quality and safety

3. **First Item ETA**
   - Minimum time before first item is ready
   - Manages customer expectations

## Algorithm Details

### Slot and Queue Processing

1. **Time Window Calculation**
```javascript
earliest = max(at - preparationTime - shelfLife, now)
to = new Date(at + maxScheduleWindow)
```

2. **Slot Processing**
	- Query existing commitments
	- Identify available slots
	- Select first suitable slot
	- Apply queue positioning rules

3. **Queue Positioning**
	- Consider preparation requirements
	- Account for shelf life
	- Maintain order sequence
	- Apply priority rules

### Timer Behavior
- Tracks preparation lifecycle
- Manages concurrent preparations
- Optimizes kitchen capacity
- Emits preparation metrics
- Handles auto-packing workflow

## Usage Examples

### Basic Scheduling
```javascript
// Schedule a new kitchen fulfillment
const fulfillment = {
    itemList: [...],
    scheduled: {
        minTime: new Date(),
        maxTime: new Date(Date.now() + 3600000)
    }
}
await Kitchen.request(fulfillment)
```

### Manual Optimization
```javascript
// Optimize schedule after a cancellation
await Kitchen.optimize(placeId, new Date())
```

## Best Practices

1. **Schedule and Queue Management**
	- Regular optimization runs
	- Proactive gap filling
	- Priority queue handling
	- Careful modification handling

2. **Resource Planning**
	- Monitor kitchen capacity
	- Track staff availability
	- Plan for peak periods
	- Balance queue loads

3. **Quality Control**
	- Enforce shelf life constraints
	- Monitor preparation timing
	- Track satisfaction metrics
	- Validate queue integrity

Key parameters:
- `maxScheduleWindow`: Maximum time window for advance scheduling
- `maxShelfLife`: Maximum time items can remain after preparation
- `minFirstEtaTime`: Minimum time before first item is ready

### Preparation Timer Configuration
The timer system supports tenant-specific settings:
- `prepareLookahead`: Advance time for preparation planning (default varies by order type)
- `minTimer`: Minimum interval between timer checks (default 3000ms)
- `enabled`: Whether kitchen scheduling is enabled for tenant

Example configuration:
```javascript
{
  kitchen: {
    enabled: true,
    prepareLookahead: 600000,  // 10 minutes
    minTimer: 5000,            // 5 seconds
    autoPack: true,
    maxRetries: 3,             // Add retry limit
    retryDelay: 30000         // Add retry delay
  }
}
```

### Slot Calculation Logic

The system calculates available slots using the following process:

1. **Empty Schedule**
   If no occupied slots exist, returns entire window as available:
   ```javascript
   {
     startTime: from,
     endTime: to,
     duration: to - from
   }
   ```

2. **With Existing Bookings**
   - Examines gaps between occupied slots
   - Adds 1-second offset between slots
   - Validates slot duration is positive
   - Returns array of valid slots ordered by start time

3. **Slot Selection**
   - Uses first-fit approach
   - Requires slot.duration >= preparationTime
   - Considers shelf life constraints

## Queue & Timer Management

- Maintains tenant-specific preparation timers
- Automatically reschedules next preparation check
- Cleans up timers when no pending preparations exist

### Timer Behavior
- Tracks preparation start/end times
- Supports dynamic scheduling
- Handles concurrent preparation
- Optimizes kitchen capacity
- Emits preparation metrics

### Preparation Time Calculation
- Based on item-level preparation times
- Adjusted for kitchen capacity
- Supports batch multipliers
- Uses default preparation time fallback
- Real-time adjustment based on kitchen load

### Auto-Pack Features
- Configurable through kitchen settings (`autoPrepare`, `autoPack`)
- Automatically schedules packing after preparation completion
- Supports preparation time optimization
- Includes queue number generation
- Prints kitchen tickets automatically
- Emits metrics for tracking

### Error Handling
The system includes robust error handling for:
- Schedule allocation failures
- Timer management issues
- Resource conflicts
- Queue management problems
- Concurrent preparation limits
- Network timeouts and retries

## Order Flow

```mermaid
sequenceDiagram
    participant C as Perkd App
    participant RH as Request Handler
    participant SA as Schedule Allocation
    participant TSM as Time Slot Management
    participant PT as Preparation Timer
    participant K as Kitchen

    C->>RH: Submit Order
    RH->>RH: Validate Request
    RH->>TSM: Get Available Slots
    TSM->>TSM: Check Occupied Slots
    TSM-->>RH: Return Available Slots
    RH->>SA: Allocate Schedule
    SA->>SA: Apply Constraints
    SA-->>RH: Return Schedule Details
    RH->>PT: Set Preparation Timer
    PT->>K: Trigger Preparation
    K-->>C: Order Ready
```

The order flow consists of the following stages:

1. **Request Processing**
   - Validate fulfillment request
   - Calculate preparation requirements
   - Find available schedule slot

2. **Schedule Allocation**
   - Update fulfillment with schedule
   - Set preparation window
   - Update main fulfillment record

3. **Preparation Triggering**
   - Timer monitors upcoming preparations
   - Triggers preparation at scheduled time
   - Updates fulfillment status

4. **Optimization**
   - Periodically checks for schedule improvements
   - Moves compatible orders forward
   - Fills schedule gaps when possible

## Metrics and Monitoring

The system tracks several key metrics:

1. **Schedule Performance**
   - `fulfillment.schedule`: Successful schedule allocations
   - `fulfillment.error.schedule`: Failed scheduling attempts
   - `fulfillment.wait.schedule`: Schedule wait times

2. **Kitchen Utilization**
   - `schedule.fulfillment`: Preparation time metrics
   - Schedule start/end tracking
   - State transitions (PREPARE, etc.)

Metrics include tags for:
- placeId
- orderId
- startTime
- duration
- units
- type
- step
- status
